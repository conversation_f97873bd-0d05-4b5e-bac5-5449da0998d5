<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Exception;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'barcode',
        'unit',
        'category_id',
        'supplier_id',
        'brand',
        'minimum_stock',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'minimum_stock' => 'integer',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Relations
     */
    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function storeStocks()
    {
        return $this->hasMany(StoreStock::class);
    }

    public function productPrices()
    {
        return $this->hasMany(ProductPrice::class);
    }

    /**
     * Obtenir le stock d'un produit dans un magasin spécifique
     */
    public function getStockInStore($storeId)
    {
        return $this->storeStocks()
            ->where('store_id', $storeId)
            ->first();
    }

    /**
     * Obtenir le prix d'un produit dans un magasin spécifique
     */
    public function getPriceInStore($storeId)
    {
        return $this->productPrices()
            ->where('store_id', $storeId)
            ->first();
    }

    /**
     * Obtenir la quantité totale du produit dans tous les magasins
     */
    public function getTotalStock()
    {
        return $this->storeStocks()
            ->where('is_active', true)
            ->sum('quantity');
    }

    /**
     * Vérifier si le produit est en stock dans un magasin spécifique
     */
    public function isInStock($storeId, $quantity = 1): bool
    {
        $storeStock = $this->getStockInStore($storeId);
        return $storeStock && $storeStock->quantity >= $quantity;
    }

    /**
     * Mettre à jour le stock dans un magasin spécifique
     */
    public function updateStockInStore($storeId, $quantity, $operation = 'add'): bool
    {
        $storeStock = $this->getStockInStore($storeId);
        if (!$storeStock) {
            return false;
        }

        if ($operation === 'add') {
            $storeStock->quantity += $quantity;
        } else {
            if ($storeStock->quantity < $quantity) {
                return false;
            }
            $storeStock->quantity -= $quantity;
        }

        return $storeStock->save();
    }

    // Nouvelles méthodes utilitaires
    public function getCurrentPriceInStore($storeId)
    {
        return $this->productPrices()
            ->where('store_id', $storeId)
            ->where('is_active', true)
            ->orderBy('effective_date', 'desc')
            ->first();
    }

    public function updateStock($storeId, $quantity, $type, $reason = null)
    {
        $storeStock = $this->storeStocks()->firstOrCreate(
            ['store_id' => $storeId],
            ['quantity' => 0]
        );

        $oldQuantity = $storeStock->quantity;

        switch ($type) {
            case 'add':
                $storeStock->quantity += $quantity;
                break;
            case 'subtract':
                if ($storeStock->quantity < $quantity) {
                    throw new Exception("Stock insuffisant");
                }
                $storeStock->quantity -= $quantity;
                break;
            case 'set':
                $storeStock->quantity = $quantity;
                break;
            default:
                throw new Exception("Type d'opération invalide");
        }

        $storeStock->save();

        // Enregistrer le mouvement de stock
        StockMovement::create([
            'product_id' => $this->id,
            'store_id' => $storeId,
            'quantity' => $quantity,
            'type' => $type,
            'old_quantity' => $oldQuantity,
            'new_quantity' => $storeStock->quantity,
            'reason' => $reason,
            'user_id' => auth()->id(),
        ]);

        // Vérifier le stock minimum
        if ($storeStock->quantity <= $this->minimum_stock) {
            $store = Store::find($storeId);
            $store->manager->notify(new LowStockAlert($this, $store));
        }

        return $storeStock;
    }

    public function setPrice($storeId, $price, $effectiveDate = null)
    {
        // Désactiver les anciens prix
        $this->productPrices()
            ->where('store_id', $storeId)
            ->where('is_active', true)
            ->update(['is_active' => false]);

        // Créer le nouveau prix
        return $this->productPrices()->create([
            'store_id' => $storeId,
            'price' => $price,
            'effective_date' => $effectiveDate ?? now(),
            'is_active' => true,
            'created_by' => auth()->id(),
        ]);
    }

    public function getPriceHistory($storeId)
    {
        return $this->productPrices()
            ->where('store_id', $storeId)
            ->orderBy('effective_date', 'desc')
            ->get();
    }

    public function getTotalSales($storeId = null, $period = null)
    {
        $query = $this->saleItems()
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id');

        if ($storeId) {
            $query->where('sales.store_id', $storeId);
        }

        if ($period) {
            $query->where('sales.created_at', '>=', $period);
        }

        return $query->sum('sale_items.quantity');
    }
}
