<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-store mr-3 text-blue-600"></i>{{ $store->name }}
                    <span class="text-lg text-gray-500 font-normal">({{ $store->code }})</span>
                </h2>
                <p class="text-sm text-gray-600 mt-1">Détails et statistiques du magasin</p>
            </div>
            <div class="flex space-x-2">
                <x-button href="{{ route('stores.edit', $store) }}" variant="secondary" icon="fas fa-edit">
                    Modifier
                </x-button>
                <x-button href="{{ route('stores.index') }}" variant="outline" icon="fas fa-arrow-left">
                    Retour
                </x-button>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Statut et informations de base -->
            <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>Informations générales
                    </h3>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        {{ $store->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        <i class="fas fa-circle mr-1 text-xs"></i>
                        {{ $store->is_active ? 'Actif' : 'Inactif' }}
                    </span>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Description</h4>
                        <p class="text-gray-900">{{ $store->description ?: 'Aucune description' }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Adresse</h4>
                        <p class="text-gray-900">{{ $store->address ?: 'Non renseignée' }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Contact</h4>
                        <div class="space-y-1">
                            @if($store->phone)
                                <p class="text-gray-900"><i class="fas fa-phone mr-1"></i>{{ $store->phone }}</p>
                            @endif
                            @if($store->email)
                                <p class="text-gray-900"><i class="fas fa-envelope mr-1"></i>{{ $store->email }}</p>
                            @endif
                            @if(!$store->phone && !$store->email)
                                <p class="text-gray-500">Non renseigné</p>
                            @endif
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Responsable</h4>
                        <div class="space-y-1">
                            <p class="text-gray-900">{{ $store->manager_name ?: 'Non défini' }}</p>
                            @if($store->manager_phone)
                                <p class="text-sm text-gray-600"><i class="fas fa-phone mr-1"></i>{{ $store->manager_phone }}</p>
                            @endif
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Configuration</h4>
                        <div class="space-y-1">
                            <p class="text-gray-900">Taxe: {{ $store->default_tax_rate }}%</p>
                            <p class="text-gray-900">Devise: {{ $store->currency }}</p>
                            @if($store->timezone)
                                <p class="text-gray-900">Fuseau: {{ $store->timezone }}</p>
                            @endif
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Dates</h4>
                        <div class="space-y-1">
                            <p class="text-gray-900">Créé: {{ $store->created_at->format('d/m/Y') }}</p>
                            <p class="text-gray-900">Modifié: {{ $store->updated_at->format('d/m/Y') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistiques -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 rounded-full">
                            <i class="fas fa-chart-line text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Ventes totales</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format($store->getTotalSales()) }} FCFA</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 rounded-full">
                            <i class="fas fa-coins text-green-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Bénéfices</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format($store->getTotalProfit()) }} FCFA</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="p-3 bg-yellow-100 rounded-full">
                            <i class="fas fa-box text-yellow-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Produits</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $store->getTotalProducts() }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 rounded-full">
                            <i class="fas fa-users text-purple-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Utilisateurs</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $store->users->count() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Utilisateurs assignés -->
            <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-users mr-2 text-purple-600"></i>Utilisateurs assignés ({{ $store->users->count() }})
                    </h3>
                    <x-button href="#" onclick="openAssignUsersModal()" variant="secondary" size="sm" icon="fas fa-user-plus">
                        Assigner des utilisateurs
                    </x-button>
                </div>

                @if($store->users->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($store->users as $user)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                            <i class="fas fa-user text-gray-600"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">{{ $user->name }}</p>
                                            <p class="text-xs text-gray-500">{{ $user->email }}</p>
                                            @if($user->roles->count() > 0)
                                                <p class="text-xs text-blue-600">{{ $user->roles->pluck('name')->join(', ') }}</p>
                                            @endif
                                        </div>
                                    </div>
                                    <form method="POST" action="{{ route('stores.assign-users', $store) }}" class="inline">
                                        @csrf
                                        <input type="hidden" name="action" value="remove">
                                        <input type="hidden" name="user_ids[]" value="{{ $user->id }}">
                                        <button type="submit" class="text-red-600 hover:text-red-800" 
                                               onclick="return confirm('Retirer cet utilisateur du magasin ?')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Aucun utilisateur assigné</h4>
                        <p class="text-gray-500 mb-4">Assignez des utilisateurs pour qu'ils puissent accéder à ce magasin.</p>
                        <x-button href="#" onclick="openAssignUsersModal()" variant="primary" icon="fas fa-user-plus">
                            Assigner des utilisateurs
                        </x-button>
                    </div>
                @endif
            </div>

            <!-- Alertes de stock -->
            @if($lowStockProducts->count() > 0)
                <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-exclamation-triangle mr-2 text-yellow-600"></i>Alertes de stock ({{ $lowStockProducts->count() }})
                    </h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Produit</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">Stock actuel</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">Seuil minimum</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase">Statut</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @foreach($lowStockProducts->take(10) as $stock)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $stock->product->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $stock->product->barcode }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                            {{ $stock->quantity }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-500">
                                            {{ $stock->min_stock_level }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            @if($stock->quantity == 0)
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                    Rupture
                                                </span>
                                            @else
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                    Stock faible
                                                </span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($lowStockProducts->count() > 10)
                        <div class="mt-4 text-center">
                            <x-button href="{{ route('store-stocks.index', ['store_id' => $store->id, 'status' => 'low_stock']) }}" 
                                     variant="outline" size="sm">
                                Voir tous les produits en stock faible ({{ $lowStockProducts->count() }})
                            </x-button>
                        </div>
                    @endif
                </div>
            @endif

            <!-- Actions rapides -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h3 class="text-lg font-medium text-gray-900 mb-4">
                    <i class="fas fa-bolt mr-2 text-orange-600"></i>Actions rapides
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <x-button href="{{ route('store-stocks.index', ['store_id' => $store->id]) }}" 
                             variant="outline" class="justify-center" icon="fas fa-boxes">
                        Gérer les stocks
                    </x-button>
                    <x-button href="{{ route('product-prices.index', ['store_id' => $store->id]) }}" 
                             variant="outline" class="justify-center" icon="fas fa-tags">
                        Gérer les prix
                    </x-button>
                    <x-button href="{{ route('sales.index', ['store_id' => $store->id]) }}" 
                             variant="outline" class="justify-center" icon="fas fa-receipt">
                        Voir les ventes
                    </x-button>
                    <x-button href="{{ route('reports.sales', ['store_id' => $store->id]) }}" 
                             variant="outline" class="justify-center" icon="fas fa-chart-bar">
                        Rapports
                    </x-button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'assignation d'utilisateurs -->
    <div id="assignUsersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <form method="POST" action="{{ route('stores.assign-users', $store) }}">
                    @csrf
                    <input type="hidden" name="action" value="add">
                    
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            Assigner des utilisateurs
                        </h3>
                        
                        <div class="space-y-3">
                            @foreach($availableUsers as $user)
                                <label class="flex items-center">
                                    <input type="checkbox" name="user_ids[]" value="{{ $user->id }}"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-900">
                                        {{ $user->name }} ({{ $user->email }})
                                        @if($user->roles->count() > 0)
                                            <span class="text-blue-600">- {{ $user->roles->pluck('name')->join(', ') }}</span>
                                        @endif
                                    </span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                    
                    <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-2">
                        <button type="button" onclick="closeAssignUsersModal()" 
                               class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Annuler
                        </button>
                        <button type="submit" 
                               class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">
                            Assigner
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            function openAssignUsersModal() {
                document.getElementById('assignUsersModal').classList.remove('hidden');
            }
            
            function closeAssignUsersModal() {
                document.getElementById('assignUsersModal').classList.add('hidden');
            }
            
            // Fermer le modal en cliquant à l'extérieur
            document.getElementById('assignUsersModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeAssignUsersModal();
                }
            });
        </script>
    @endpush
</x-app-layout>
