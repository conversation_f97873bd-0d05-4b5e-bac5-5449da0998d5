<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-user mr-2"></i>{{ $customer->name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('customers.edit', $customer) }}"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-edit mr-2"></i>Modifier
                </a>
                <a href="{{ route('customers.index') }}"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <!-- Statistiques du client -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-shopping-cart text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total achats</p>
                                <p class="text-2xl font-semibold text-gray-900">
                                    {{ number_format($customer->sales->sum('total_amount'), 0, ',', ' ') }} FCFA
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-receipt text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Nombre d'achats</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $customer->sales->count() }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-credit-card text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Solde crédit</p>
                                <p
                                    class="text-2xl font-semibold {{ $customer->current_balance > 0 ? 'text-red-600' : 'text-green-600' }}">
                                    {{ number_format($customer->current_balance, 0, ',', ' ') }} FCFA
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-percentage text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Limite crédit</p>
                                <p class="text-2xl font-semibold text-gray-900">
                                    {{ number_format($customer->credit_limit, 0, ',', ' ') }} FCFA
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">

                <!-- Informations du client -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-6">Informations du client</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Nom complet</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $customer->name }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Email</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $customer->email ?? 'Non renseigné' }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Téléphone</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $customer->phone ?? 'Non renseigné' }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Date de naissance</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        @if ($customer->date_of_birth)
                                            {{ $customer->date_of_birth->format('d/m/Y') }}
                                            ({{ $customer->date_of_birth->age }} ans)
                                        @else
                                            Non renseignée
                                        @endif
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Genre</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        @switch($customer->gender)
                                            @case('male')
                                                Masculin
                                            @break

                                            @case('female')
                                                Féminin
                                            @break

                                            @default
                                                Non spécifié
                                        @endswitch
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Client depuis</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $customer->created_at->format('d/m/Y') }}
                                    </p>
                                </div>
                            </div>

                            @if ($customer->address)
                                <div class="mt-6">
                                    <label class="block text-sm font-medium text-gray-700">Adresse</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $customer->address }}</p>
                                </div>
                            @endif

                            @if ($customer->notes)
                                <div class="mt-6">
                                    <label class="block text-sm font-medium text-gray-700">Notes</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $customer->notes }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Historique des achats récents -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-6">
                                <i class="fas fa-history mr-2"></i>Achats récents
                            </h3>

                            @if ($customer->sales->count() > 0)
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th
                                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    N° Vente</th>
                                                <th
                                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Date</th>
                                                <th
                                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Montant</th>
                                                <th
                                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Paiement</th>
                                                <th
                                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            @foreach ($customer->sales->sortByDesc('created_at')->take(10) as $sale)
                                                <tr class="hover:bg-gray-50">
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm font-medium text-gray-900">
                                                            {{ $sale->sale_number }}</div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {{ $sale->created_at->format('d/m/Y H:i') }}
                                                    </td>
                                                    <td
                                                        class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        {{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span
                                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                    {{ $sale->payment_method === 'cash' ? 'bg-green-100 text-green-800' : '' }}
                                                    {{ $sale->payment_method === 'mobile_money' ? 'bg-blue-100 text-blue-800' : '' }}
                                                    {{ $sale->payment_method === 'credit' ? 'bg-yellow-100 text-yellow-800' : '' }}">
                                                            @switch($sale->payment_method)
                                                                @case('cash')
                                                                    Espèces
                                                                @break

                                                                @case('mobile_money')
                                                                    Mobile Money
                                                                @break

                                                                @case('credit')
                                                                    Crédit
                                                                @break

                                                                @default
                                                                    {{ ucfirst($sale->payment_method) }}
                                                            @endswitch
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <a href="{{ route('sales.show', $sale) }}"
                                                            class="text-blue-600 hover:text-blue-900">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                @if ($customer->sales->count() > 10)
                                    <div class="mt-4 text-center">
                                        <a href="{{ route('sales.index', ['customer' => $customer->id]) }}"
                                            class="text-blue-600 hover:text-blue-800 text-sm">
                                            Voir tous les achats ({{ $customer->sales->count() }})
                                        </a>
                                    </div>
                                @endif
                            @else
                                <div class="text-center py-8">
                                    <i class="fas fa-shopping-cart text-gray-300 text-4xl mb-4"></i>
                                    <p class="text-gray-500">Aucun achat enregistré</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Actions rapides -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Actions rapides</h3>

                            <div class="space-y-3">
                                <a href="{{ route('pos.index', ['customer' => $customer->id]) }}"
                                    class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-cash-register mr-2"></i>Nouvelle vente
                                </a>

                                <a href="{{ route('customers.edit', $customer) }}"
                                    class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-edit mr-2"></i>Modifier le client
                                </a>
                                @if (Auth::user()->roles->whereIn('name', ['superAdmin', 'admin'])->count() > 0)
                                    @if ($customer->credits->where('status', 'active')->count() > 0)
                                        <a href="{{ route('customers.credits', $customer) }}"
                                            class="w-full flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                                            <i class="fas fa-credit-card mr-2"></i>Gérer les crédits
                                        </a>
                                    @endif
                                @endif

                                {{-- <button
                                    class="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                    <i class="fas fa-envelope mr-2"></i>Envoyer message
                                </button> --}}
                            </div>
                        </div>
                    </div>

                    <!-- Informations crédit -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Informations crédit</h3>

                            <div class="space-y-4">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Limite de crédit</span>
                                    <span class="text-sm font-medium text-gray-900">
                                        {{ number_format($customer->credit_limit, 0, ',', ' ') }} FCFA
                                    </span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Solde actuel</span>
                                    <span
                                        class="text-sm font-medium {{ $customer->current_balance > 0 ? 'text-red-600' : 'text-green-600' }}">
                                        {{ number_format($customer->current_balance, 0, ',', ' ') }} FCFA
                                    </span>
                                </div>

                                <div class="flex justify-between border-t pt-2">
                                    <span class="text-sm text-gray-500">Crédit disponible</span>
                                    <span class="text-sm font-medium text-green-600">
                                        {{ number_format($customer->credit_limit - $customer->current_balance, 0, ',', ' ') }}
                                        FCFA
                                    </span>
                                </div>

                                @php
                                    $percentage =
                                        $customer->credit_limit > 0
                                            ? ($customer->current_balance / $customer->credit_limit) * 100
                                            : 0;
                                @endphp

                                <div class="mt-4">
                                    <div class="flex justify-between text-xs text-gray-600 mb-1">
                                        <span>Utilisation du crédit</span>
                                        <span>{{ number_format($percentage, 1) }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full {{ $percentage > 80 ? 'bg-red-600' : ($percentage > 60 ? 'bg-yellow-600' : 'bg-green-600') }}"
                                            style="width: {{ min($percentage, 100) }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Crédits actifs -->
                    @if ($customer->credits->where('status', 'active')->count() > 0)
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    Crédits actifs ({{ $customer->credits->where('status', 'active')->count() }})
                                </h3>

                                <div class="space-y-3">
                                    @foreach ($customer->credits->where('status', 'active')->take(3) as $credit)
                                        <div class="border border-gray-200 rounded-lg p-3">
                                            <div class="flex justify-between items-start mb-2">
                                                <span
                                                    class="text-sm font-medium text-gray-900">{{ $credit->sale->sale_number }}</span>
                                                @if ($credit->isOverdue())
                                                    <span
                                                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        En retard
                                                    </span>
                                                @endif
                                            </div>
                                            <div class="text-xs text-gray-500 mb-1">
                                                Reste: {{ number_format($credit->remaining_balance, 0, ',', ' ') }}
                                                FCFA
                                            </div>
                                            @if ($credit->due_date)
                                                <div class="text-xs text-gray-500">
                                                    Échéance: {{ $credit->due_date->format('d/m/Y') }}
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach

                                    @if ($customer->credits->where('status', 'active')->count() > 3)
                                        <div class="text-center">
                                            <a href="{{ route('customers.credits', $customer) }}"
                                                class="text-blue-600 hover:text-blue-800 text-sm">
                                                Voir tous les crédits
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
