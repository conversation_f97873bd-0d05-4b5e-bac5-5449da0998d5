<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class ShowRoles extends Command
{
    protected $signature = 'show:roles';
    protected $description = 'Afficher tous les rôles et leurs permissions';

    public function handle()
    {
        $roles = Role::with('permissions')->get();

        foreach ($roles as $role) {
            $this->info("\nRôle : " . $role->name);
            $this->info("Permissions :");
            
            foreach ($role->permissions as $permission) {
                $this->line("- " . $permission->name);
            }
        }
    }
}
