<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Store;
use App\Models\StoreStock;

class StoreStockSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::all();
        $stores = Store::all();

        // Emplacements possibles dans un magasin
        $locations = [
            'Rayon A - Étagère 1',
            'Rayon A - Étagère 2',
            'Rayon B - Étagère 1',
            'Rayon B - Étagère 2',
            'Rayon C - Étagère 1',
            'Réserve - Zone 1',
            'Réserve - Zone 2',
            'Comptoir principal',
            'Vitrine',
            'Entrepôt'
        ];

        foreach ($products as $product) {
            foreach ($stores as $store) {
                // Générer des quantités différentes selon le magasin
                $baseQuantity = rand(10, 100);
                
                // Le magasin principal a généralement plus de stock
                if ($store->code === 'CENTRE') {
                    $quantity = $baseQuantity * rand(150, 200) / 100; // 150% à 200% du stock de base
                } elseif ($store->code === 'ZONE4') {
                    $quantity = $baseQuantity * rand(80, 120) / 100; // 80% à 120% du stock de base
                } else {
                    $quantity = $baseQuantity * rand(50, 90) / 100; // 50% à 90% du stock de base
                }

                $quantity = (int) $quantity;

                // Définir les seuils de stock
                $minStockLevel = max(5, (int) ($quantity * 0.2)); // 20% du stock actuel, minimum 5
                $maxStockLevel = $quantity * 3; // 3 fois le stock actuel
                $reorderPoint = max(10, (int) ($quantity * 0.3)); // 30% du stock actuel, minimum 10

                // Simuler quelques produits en rupture ou stock faible
                if (rand(1, 10) === 1) { // 10% de chance d'être en rupture
                    $quantity = 0;
                } elseif (rand(1, 5) === 1) { // 20% de chance d'être en stock faible
                    $quantity = rand(1, $minStockLevel);
                }

                StoreStock::create([
                    'product_id' => $product->id,
                    'store_id' => $store->id,
                    'quantity' => $quantity,
                    'min_stock_level' => $minStockLevel,
                    'max_stock_level' => $maxStockLevel,
                    'reorder_point' => $reorderPoint,
                    'location' => $locations[array_rand($locations)],
                    'is_active' => true,
                    'last_counted_at' => now()->subDays(rand(1, 30)), // Dernier inventaire entre 1 et 30 jours
                ]);
            }
        }
    }
}
