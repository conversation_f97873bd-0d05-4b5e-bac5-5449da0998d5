<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SaleItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_id',
        'product_id',
        'product_name',
        'unit_price',
        'cost_price',
        'quantity',
        'discount_amount',
        'total_price',
        'profit_amount',
        'profit_margin',
    ];

    protected function casts(): array
    {
        return [
            'unit_price' => 'decimal:2',
            'cost_price' => 'decimal:2',
            'discount_amount' => 'decimal:2',
            'total_price' => 'decimal:2',
            'profit_amount' => 'decimal:2',
            'profit_margin' => 'decimal:2',
        ];
    }

    /**
     * Calculate final price with discount
     */
    public function calculateTotalPrice()
    {
        return ($this->unit_price * $this->quantity) - $this->discount_amount;
    }

    /**
     * Calculate and set profit information
     */
    public function calculateProfit()
    {
        if ($this->cost_price) {
            $totalCost = $this->cost_price * $this->quantity;
            $totalRevenue = $this->unit_price * $this->quantity;

            $this->profit_amount = $totalRevenue - $totalCost;
            $this->profit_margin = $totalRevenue > 0 ? (($totalRevenue - $totalCost) / $totalRevenue) * 100 : 0;
        }
    }

    /**
     * Get profit per unit
     */
    public function getProfitPerUnitAttribute()
    {
        if (!$this->cost_price) {
            return null;
        }

        return $this->unit_price - $this->cost_price;
    }

    /**
     * Check if this item is profitable
     */
    public function isProfitable()
    {
        return $this->profit_amount && $this->profit_amount > 0;
    }

    /**
     * Relations
     */
    public function sale()
    {
        return $this->belongsTo(Sale::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Calculate total price before tax and discount
     */
    public function getSubtotalAttribute()
    {
        return $this->unit_price * $this->quantity;
    }

    /**
     * Calculate final price with tax and discount
     */
    public function getFinalPriceAttribute()
    {
        return $this->subtotal + $this->tax_amount - $this->discount_amount;
    }
}
