@props([
    'title',
    'value',
    'icon',
    'color' => 'blue',
    'trend' => null,
    'trendDirection' => null,
    'description' => null,
    'link' => null
])

@php
$colorClasses = [
    'blue' => 'from-blue-500 to-blue-600',
    'green' => 'from-green-500 to-green-600',
    'yellow' => 'from-yellow-500 to-yellow-600',
    'red' => 'from-red-500 to-red-600',
    'purple' => 'from-purple-500 to-purple-600',
    'indigo' => 'from-indigo-500 to-indigo-600',
    'pink' => 'from-pink-500 to-pink-600',
    'orange' => 'from-orange-500 to-orange-600',
    'teal' => 'from-teal-500 to-teal-600',
    'gray' => 'from-gray-500 to-gray-600',
];

$bgColorClasses = [
    'blue' => 'bg-blue-50 border-blue-100',
    'green' => 'bg-green-50 border-green-100',
    'yellow' => 'bg-yellow-50 border-yellow-100',
    'red' => 'bg-red-50 border-red-100',
    'purple' => 'bg-purple-50 border-purple-100',
    'indigo' => 'bg-indigo-50 border-indigo-100',
    'pink' => 'bg-pink-50 border-pink-100',
    'orange' => 'bg-orange-50 border-orange-100',
    'teal' => 'bg-teal-50 border-teal-100',
    'gray' => 'bg-gray-50 border-gray-100',
];

$gradientClass = $colorClasses[$color] ?? $colorClasses['blue'];
$bgClass = $bgColorClasses[$color] ?? $bgColorClasses['blue'];
@endphp

<div {{ $attributes->merge([
    'class' => "
        bg-white 
        rounded-xl 
        shadow-soft 
        border 
        border-gray-100 
        overflow-hidden 
        hover:shadow-medium 
        transition-all 
        duration-200 
        hover:-translate-y-1
        " . ($link ? 'cursor-pointer' : '')
]) }}
@if($link) onclick="window.location.href='{{ $link }}'" @endif>
    
    <div class="p-6">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center space-x-4">
                    <!-- Icon -->
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-r {{ $gradientClass }} rounded-xl flex items-center justify-center shadow-lg">
                            <i class="{{ $icon }} text-white text-lg"></i>
                        </div>
                    </div>
                    
                    <!-- Content -->
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-600 truncate">{{ $title }}</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">{{ $value }}</p>
                        
                        @if($description)
                        <p class="text-xs text-gray-500 mt-1">{{ $description }}</p>
                        @endif
                    </div>
                </div>
                
                <!-- Trend -->
                @if($trend && $trendDirection)
                <div class="mt-4 flex items-center">
                    <div class="flex items-center space-x-1">
                        @if($trendDirection === 'up')
                        <i class="fas fa-arrow-up text-green-500 text-xs"></i>
                        <span class="text-sm font-medium text-green-600">{{ $trend }}</span>
                        @elseif($trendDirection === 'down')
                        <i class="fas fa-arrow-down text-red-500 text-xs"></i>
                        <span class="text-sm font-medium text-red-600">{{ $trend }}</span>
                        @else
                        <i class="fas fa-minus text-gray-500 text-xs"></i>
                        <span class="text-sm font-medium text-gray-600">{{ $trend }}</span>
                        @endif
                    </div>
                    <span class="text-xs text-gray-500 ml-2">vs période précédente</span>
                </div>
                @endif
            </div>
            
            @if($link)
            <div class="flex-shrink-0 ml-4">
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            @endif
        </div>
    </div>
    
    <!-- Progress bar (optional) -->
    @isset($progress)
    <div class="px-6 pb-4">
        <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-gradient-to-r {{ $gradientClass }} h-2 rounded-full transition-all duration-500" 
                 style="width: {{ $progress }}%"></div>
        </div>
        <div class="flex justify-between text-xs text-gray-500 mt-1">
            <span>0%</span>
            <span>{{ $progress }}%</span>
            <span>100%</span>
        </div>
    </div>
    @endisset
</div>
