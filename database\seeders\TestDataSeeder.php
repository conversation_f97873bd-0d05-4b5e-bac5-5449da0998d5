<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Customer;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer des produits de test
        $products = [
            [
                'name' => 'Coca Cola 33cl',
                'description' => 'Boisson gazeuse',
                'barcode' => '1234567890123',
                'unit' => 'pcs',
                'is_active' => true,
            ],
            [
                'name' => 'Pain de mie',
                'description' => 'Pain de mie complet',
                'barcode' => '2345678901234',
                'unit' => 'pcs',
                'is_active' => true,
            ],
            [
                'name' => 'Riz 1kg',
                'description' => 'Riz blanc de qualité',
                'barcode' => '3456789012345',
                'unit' => 'kg',
                'is_active' => true,
            ],
            [
                'name' => 'Huile végétale 1L',
                'description' => 'Huile de tournesol',
                'barcode' => '4567890123456',
                'unit' => 'L',
                'is_active' => true,
            ],
            [
                'name' => 'Savon de toilette',
                'description' => 'Savon parfumé',
                'barcode' => '5678901234567',
                'unit' => 'pcs',
                'is_active' => true,
            ],
        ];

        foreach ($products as $product) {
            Product::firstOrCreate(
                ['barcode' => $product['barcode']],
                $product
            );
        }

        // Créer des clients de test
        $customers = [
            [
                'name' => 'Jean Dupont',
                'email' => '<EMAIL>',
                'phone' => '+33123456789',
                'address' => '123 Rue de la Paix, Paris',
                'is_active' => true,
            ],
            [
                'name' => 'Marie Martin',
                'email' => '<EMAIL>',
                'phone' => '+33987654321',
                'address' => '456 Avenue des Champs, Lyon',
                'is_active' => true,
            ],
            [
                'name' => 'Pierre Durand',
                'email' => '<EMAIL>',
                'phone' => '+33555666777',
                'address' => '789 Boulevard Saint-Michel, Marseille',
                'is_active' => true,
            ],
            [
                'name' => 'Sophie Leroy',
                'email' => '<EMAIL>',
                'phone' => '+33444555666',
                'address' => '321 Rue Victor Hugo, Toulouse',
                'is_active' => true,
            ],
            [
                'name' => 'Client Anonyme',
                'email' => null,
                'phone' => null,
                'address' => null,
                'is_active' => true,
            ],
        ];

        foreach ($customers as $customer) {
            Customer::firstOrCreate(
                ['name' => $customer['name']],
                $customer
            );
        }
    }
}
