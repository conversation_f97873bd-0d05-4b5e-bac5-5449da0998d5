<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Store;
use App\Models\StoreStock;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportsController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $userStore = $user->store;

        // Vérifier si l'utilisateur a accès aux rapports
        $canViewAllStores = $user->roles->pluck('name')->intersect(['superAdmin', 'admin'])->isNotEmpty();

        if (!$canViewAllStores && !$userStore) {
            return redirect()->back()->with('error', 'Accès non autorisé aux rapports.');
        }

        // Obtenir les magasins accessibles
        $stores = $canViewAllStores ? Store::active()->get() : collect([$userStore]);

        return view('reports.index', compact('stores', 'userStore', 'canViewAllStores'));
    }

    public function profitAnalytics(Request $request)
    {
        $user = Auth::user();
        $userStore = $user->store;
        $canViewAllStores = $user->roles->pluck('name')->intersect(['superAdmin', 'admin'])->isNotEmpty();

        // Paramètres de filtrage
        $storeId = $request->get('store_id');
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $period = $request->get('period', 'daily'); // daily, weekly, monthly

        // Validation des permissions
        if (!$canViewAllStores) {
            $storeId = $userStore->id;
        }

        // Construction de la requête de base
        $query = Sale::with(['store', 'items.product'])
            ->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->where('status', 'completed');

        if ($storeId) {
            $query->where('store_id', $storeId);
        } elseif (!$canViewAllStores) {
            $query->where('store_id', $userStore->id);
        }

        $sales = $query->get();

        // Calculs des métriques principales
        $totalSales = $sales->sum('total_amount');
        $totalProfit = $sales->sum('profit_amount');
        $totalCost = $totalSales - $totalProfit;
        $averageMargin = $totalSales > 0 ? ($totalProfit / $totalSales * 100) : 0;
        $salesCount = $sales->count();
        $averageSaleValue = $salesCount > 0 ? ($totalSales / $salesCount) : 0;

        // Données pour les graphiques par période
        $periodData = $this->getPeriodData($sales, $period, $startDate, $endDate);

        // Top produits les plus rentables
        $topProducts = $this->getTopProfitableProducts($storeId, $startDate, $endDate, $canViewAllStores, $userStore);

        // Performance par magasin (si admin)
        $storePerformance = [];
        if ($canViewAllStores && !$storeId) {
            $storePerformance = $this->getStorePerformance($startDate, $endDate);
        }

        // Analyse des marges par catégorie
        $categoryMargins = $this->getCategoryMargins($storeId, $startDate, $endDate, $canViewAllStores, $userStore);

        // Obtenir les magasins pour le filtre
        $stores = $canViewAllStores ? Store::active()->get() : collect([$userStore]);

        return view('reports.profit-analytics', compact(
            'totalSales', 'totalProfit', 'totalCost', 'averageMargin', 'salesCount', 'averageSaleValue',
            'periodData', 'topProducts', 'storePerformance', 'categoryMargins',
            'stores', 'userStore', 'canViewAllStores',
            'storeId', 'startDate', 'endDate', 'period'
        ));
    }

    private function getPeriodData($sales, $period, $startDate, $endDate)
    {
        $groupBy = match($period) {
            'weekly' => 'YEARWEEK(created_at)',
            'monthly' => 'YEAR(created_at), MONTH(created_at)',
            default => 'DATE(created_at)'
        };

        $periodSales = $sales->groupBy(function($sale) use ($period) {
            return match($period) {
                'weekly' => $sale->created_at->format('Y-W'),
                'monthly' => $sale->created_at->format('Y-m'),
                default => $sale->created_at->format('Y-m-d')
            };
        });

        $data = [];
        foreach ($periodSales as $date => $periodSalesGroup) {
            $data[] = [
                'date' => $date,
                'sales' => $periodSalesGroup->sum('total_amount'),
                'profit' => $periodSalesGroup->sum('profit_amount'),
                'count' => $periodSalesGroup->count(),
                'margin' => $periodSalesGroup->sum('total_amount') > 0 
                    ? ($periodSalesGroup->sum('profit_amount') / $periodSalesGroup->sum('total_amount') * 100) 
                    : 0
            ];
        }

        return collect($data)->sortBy('date')->values();
    }

    private function getTopProfitableProducts($storeId, $startDate, $endDate, $canViewAllStores, $userStore)
    {
        $query = SaleItem::select([
                'product_id',
                DB::raw('SUM(quantity) as total_quantity'),
                DB::raw('SUM(unit_price * quantity) as total_sales'),
                DB::raw('SUM(profit_amount) as total_profit'),
                DB::raw('AVG(profit_margin) as avg_margin')
            ])
            ->with('product')
            ->whereHas('sale', function($q) use ($startDate, $endDate, $storeId, $canViewAllStores, $userStore) {
                $q->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
                  ->where('status', 'completed');
                
                if ($storeId) {
                    $q->where('store_id', $storeId);
                } elseif (!$canViewAllStores) {
                    $q->where('store_id', $userStore->id);
                }
            })
            ->groupBy('product_id')
            ->orderBy('total_profit', 'desc')
            ->limit(10);

        return $query->get();
    }

    private function getStorePerformance($startDate, $endDate)
    {
        return Store::select([
                'stores.id',
                'stores.name',
                'stores.code',
                DB::raw('COUNT(sales.id) as sales_count'),
                DB::raw('COALESCE(SUM(sales.total_amount), 0) as total_sales'),
                DB::raw('COALESCE(SUM(sales.profit_amount), 0) as total_profit'),
                DB::raw('CASE WHEN SUM(sales.total_amount) > 0 THEN (SUM(sales.profit_amount) / SUM(sales.total_amount) * 100) ELSE 0 END as avg_margin')
            ])
            ->leftJoin('sales', function($join) use ($startDate, $endDate) {
                $join->on('stores.id', '=', 'sales.store_id')
                     ->whereBetween('sales.created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
                     ->where('sales.status', 'completed');
            })
            ->where('stores.status', 'active')
            ->groupBy('stores.id', 'stores.name', 'stores.code')
            ->orderBy('total_profit', 'desc')
            ->get();
    }

    private function getCategoryMargins($storeId, $startDate, $endDate, $canViewAllStores, $userStore)
    {
        $query = SaleItem::select([
                'products.category',
                DB::raw('COUNT(sale_items.id) as items_sold'),
                DB::raw('SUM(sale_items.quantity) as total_quantity'),
                DB::raw('SUM(sale_items.unit_price * sale_items.quantity) as total_sales'),
                DB::raw('SUM(sale_items.profit_amount) as total_profit'),
                DB::raw('AVG(sale_items.profit_margin) as avg_margin')
            ])
            ->join('products', 'sale_items.product_id', '=', 'products.id')
            ->whereHas('sale', function($q) use ($startDate, $endDate, $storeId, $canViewAllStores, $userStore) {
                $q->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
                  ->where('status', 'completed');
                
                if ($storeId) {
                    $q->where('store_id', $storeId);
                } elseif (!$canViewAllStores) {
                    $q->where('store_id', $userStore->id);
                }
            })
            ->whereNotNull('products.category')
            ->groupBy('products.category')
            ->orderBy('total_profit', 'desc');

        return $query->get();
    }

    public function exportProfitReport(Request $request)
    {
        // Cette méthode sera implémentée pour l'export Excel/PDF
        return response()->json(['message' => 'Export en cours de développement']);
    }

    public function stockAnalytics(Request $request)
    {
        $user = Auth::user();
        $userStore = $user->store;
        $canViewAllStores = $user->roles->pluck('name')->intersect(['superAdmin', 'admin'])->isNotEmpty();

        $storeId = $request->get('store_id');
        
        if (!$canViewAllStores) {
            $storeId = $userStore->id;
        }

        // Analyse des stocks et rotations
        $stockAnalysis = $this->getStockAnalysis($storeId, $canViewAllStores, $userStore);
        
        $stores = $canViewAllStores ? Store::active()->get() : collect([$userStore]);

        return view('reports.stock-analytics', compact(
            'stockAnalysis', 'stores', 'userStore', 'canViewAllStores', 'storeId'
        ));
    }

    private function getStockAnalysis($storeId, $canViewAllStores, $userStore)
    {
        $query = StoreStock::select([
                'store_stocks.*',
                'products.name as product_name',
                'products.category',
                'stores.name as store_name',
                DB::raw('(store_stocks.quantity * COALESCE(pp.cost_price, 0)) as stock_value'),
                DB::raw('CASE WHEN store_stocks.quantity <= store_stocks.low_stock_threshold THEN "low" 
                         WHEN store_stocks.quantity = 0 THEN "out" 
                         ELSE "normal" END as stock_status')
            ])
            ->join('products', 'store_stocks.product_id', '=', 'products.id')
            ->join('stores', 'store_stocks.store_id', '=', 'stores.id')
            ->leftJoin('product_prices as pp', function($join) {
                $join->on('store_stocks.product_id', '=', 'pp.product_id')
                     ->on('store_stocks.store_id', '=', 'pp.store_id')
                     ->where('pp.status', 'active')
                     ->whereNull('pp.effective_until');
            })
            ->where('store_stocks.status', 'active');

        if ($storeId) {
            $query->where('store_stocks.store_id', $storeId);
        } elseif (!$canViewAllStores) {
            $query->where('store_stocks.store_id', $userStore->id);
        }

        return $query->orderBy('stock_value', 'desc')->get();
    }
}
