<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-edit mr-2"></i>{{ __('Modifier la Promotion') }} - {{ $promotion->name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('promotions.show', $promotion) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-eye mr-2"></i>Voir
                </a>
                <a href="{{ route('promotions.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form action="{{ route('promotions.update', $promotion) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Informations de base -->
                            <div class="md:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Informations de base</h3>
                            </div>

                            <!-- Nom -->
                            <div class="md:col-span-2">
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nom de la promotion <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       name="name" 
                                       id="name" 
                                       value="{{ old('name', $promotion->name) }}"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="md:col-span-2">
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                    Description
                                </label>
                                <textarea name="description" 
                                          id="description" 
                                          rows="3"
                                          placeholder="Description de la promotion..."
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('description') border-red-500 @enderror">{{ old('description', $promotion->description) }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Type de promotion -->
                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                                    Type de promotion <span class="text-red-500">*</span>
                                </label>
                                <select name="type" 
                                        id="type" 
                                        required
                                        onchange="togglePromotionFields()"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('type') border-red-500 @enderror">
                                    <option value="">Sélectionner un type</option>
                                    <option value="percentage" {{ old('type', $promotion->type) === 'percentage' ? 'selected' : '' }}>Pourcentage de remise</option>
                                    <option value="fixed_amount" {{ old('type', $promotion->type) === 'fixed_amount' ? 'selected' : '' }}>Montant fixe de remise</option>
                                    <option value="buy_x_get_y" {{ old('type', $promotion->type) === 'buy_x_get_y' ? 'selected' : '' }}>Achetez X obtenez Y gratuit</option>
                                </select>
                                @error('type')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Valeur -->
                            <div id="value-field">
                                <label for="value" class="block text-sm font-medium text-gray-700 mb-2">
                                    <span id="value-label">Valeur</span> <span class="text-red-500">*</span>
                                </label>
                                <input type="number" 
                                       name="value" 
                                       id="value" 
                                       value="{{ old('value', $promotion->value) }}"
                                       min="0"
                                       step="0.01"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('value') border-red-500 @enderror">
                                @error('value')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Quantité minimum -->
                            <div>
                                <label for="min_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                                    Quantité minimum <span class="text-red-500">*</span>
                                </label>
                                <input type="number" 
                                       name="min_quantity" 
                                       id="min_quantity" 
                                       value="{{ old('min_quantity', $promotion->min_quantity) }}"
                                       min="1"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('min_quantity') border-red-500 @enderror">
                                @error('min_quantity')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Quantité gratuite (pour buy_x_get_y) -->
                            <div id="free-quantity-field">
                                <label for="free_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                                    Quantité gratuite
                                </label>
                                <input type="number" 
                                       name="free_quantity" 
                                       id="free_quantity" 
                                       value="{{ old('free_quantity', $promotion->free_quantity) }}"
                                       min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('free_quantity') border-red-500 @enderror">
                                @error('free_quantity')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Montant minimum d'achat -->
                            <div>
                                <label for="min_amount" class="block text-sm font-medium text-gray-700 mb-2">
                                    Montant minimum d'achat (FCFA)
                                </label>
                                <input type="number" 
                                       name="min_amount" 
                                       id="min_amount" 
                                       value="{{ old('min_amount', $promotion->min_amount) }}"
                                       min="0"
                                       step="0.01"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('min_amount') border-red-500 @enderror">
                                @error('min_amount')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Limite d'utilisation -->
                            <div>
                                <label for="usage_limit" class="block text-sm font-medium text-gray-700 mb-2">
                                    Limite d'utilisation
                                </label>
                                <input type="number" 
                                       name="usage_limit" 
                                       id="usage_limit" 
                                       value="{{ old('usage_limit', $promotion->usage_limit) }}"
                                       min="1"
                                       placeholder="Illimité si vide"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('usage_limit') border-red-500 @enderror">
                                @error('usage_limit')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">
                                    Utilisations actuelles: {{ $promotion->usage_count }}
                                </p>
                            </div>

                            <!-- Période de validité -->
                            <div class="md:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Période de validité</h3>
                            </div>

                            <!-- Date de début -->
                            <div>
                                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">
                                    Date de début <span class="text-red-500">*</span>
                                </label>
                                <input type="date" 
                                       name="start_date" 
                                       id="start_date" 
                                       value="{{ old('start_date', $promotion->start_date->format('Y-m-d')) }}"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('start_date') border-red-500 @enderror">
                                @error('start_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Date de fin -->
                            <div>
                                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">
                                    Date de fin <span class="text-red-500">*</span>
                                </label>
                                <input type="date" 
                                       name="end_date" 
                                       id="end_date" 
                                       value="{{ old('end_date', $promotion->end_date->format('Y-m-d')) }}"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('end_date') border-red-500 @enderror">
                                @error('end_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Statut -->
                            <div class="md:col-span-2">
                                <div class="flex items-center">
                                    <input type="checkbox" 
                                           name="is_active" 
                                           id="is_active" 
                                           value="1"
                                           {{ old('is_active', $promotion->is_active) ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                        Promotion active
                                    </label>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">
                                    Les promotions inactives ne seront pas appliquées lors des ventes.
                                </p>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="mt-8 flex justify-end space-x-4">
                            <a href="{{ route('promotions.show', $promotion) }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition-colors">
                                Annuler
                            </a>
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-save mr-2"></i>Mettre à jour
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function togglePromotionFields() {
            const type = document.getElementById('type').value;
            const valueLabel = document.getElementById('value-label');
            const valueField = document.getElementById('value');
            const freeQuantityField = document.getElementById('free-quantity-field');

            // Reset
            freeQuantityField.style.display = 'none';
            valueField.style.display = 'block';

            switch(type) {
                case 'percentage':
                    valueLabel.textContent = 'Pourcentage de remise (%)';
                    valueField.setAttribute('max', '100');
                    break;
                case 'fixed_amount':
                    valueLabel.textContent = 'Montant de remise (FCFA)';
                    valueField.removeAttribute('max');
                    break;
                case 'buy_x_get_y':
                    valueLabel.textContent = 'Valeur (non utilisée pour ce type)';
                    valueField.style.display = 'none';
                    freeQuantityField.style.display = 'block';
                    break;
                default:
                    valueLabel.textContent = 'Valeur';
                    valueField.removeAttribute('max');
            }
        }

        // Initialiser les champs au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            togglePromotionFields();
        });
    </script>
    @endpush
</x-app-layout>
