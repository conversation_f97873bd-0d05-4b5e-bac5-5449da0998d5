<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Store;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\StoreStock;
use App\Models\User;
use App\Models\Sale;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;

class ValidateMultiStoreSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'multistore:validate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Valide que le système multi-magasins fonctionne correctement';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Validation du système multi-magasins...');
        $this->newLine();

        $errors = 0;
        $warnings = 0;

        // 1. Vérifier les magasins
        $errors += $this->validateStores();
        
        // 2. Vérifier les utilisateurs
        $warnings += $this->validateUsers();
        
        // 3. Vérifier les produits et prix
        $errors += $this->validateProductPrices();
        
        // 4. Vérifier les stocks
        $errors += $this->validateStocks();
        
        // 5. Vérifier les ventes
        $errors += $this->validateSales();
        
        // 6. Vérifier les clients
        $warnings += $this->validateCustomers();
        
        // 7. Vérifier l'intégrité des données
        $errors += $this->validateDataIntegrity();

        $this->newLine();
        
        if ($errors === 0 && $warnings === 0) {
            $this->info('✅ Système multi-magasins validé avec succès !');
        } else {
            if ($errors > 0) {
                $this->error("❌ {$errors} erreur(s) détectée(s)");
            }
            if ($warnings > 0) {
                $this->warn("⚠️  {$warnings} avertissement(s)");
            }
        }

        return $errors === 0 ? 0 : 1;
    }

    private function validateStores()
    {
        $this->info('📍 Validation des magasins...');
        $errors = 0;

        $stores = Store::all();
        
        if ($stores->count() === 0) {
            $this->error('  ❌ Aucun magasin trouvé');
            $errors++;
        } else {
            $this->info("  ✅ {$stores->count()} magasin(s) trouvé(s)");
            
            foreach ($stores as $store) {
                if (empty($store->name) || empty($store->code)) {
                    $this->error("  ❌ Magasin {$store->id} : nom ou code manquant");
                    $errors++;
                }
                
                if ($store->default_tax_rate < 0 || $store->default_tax_rate > 100) {
                    $this->error("  ❌ Magasin {$store->name} : taux de taxe invalide ({$store->default_tax_rate}%)");
                    $errors++;
                }
            }
        }

        return $errors;
    }

    private function validateUsers()
    {
        $this->info('👥 Validation des utilisateurs...');
        $warnings = 0;

        $totalUsers = User::count();
        $usersWithStore = User::whereNotNull('store_id')->count();
        $usersWithoutStore = $totalUsers - $usersWithStore;

        $this->info("  ✅ {$totalUsers} utilisateur(s) au total");
        $this->info("  ✅ {$usersWithStore} utilisateur(s) assigné(s) à un magasin");
        
        if ($usersWithoutStore > 0) {
            $this->warn("  ⚠️  {$usersWithoutStore} utilisateur(s) sans magasin assigné");
            $warnings++;
        }

        // Vérifier les assignations invalides
        $invalidAssignments = User::whereNotNull('store_id')
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                    ->from('stores')
                    ->whereColumn('stores.id', 'users.store_id');
            })->count();

        if ($invalidAssignments > 0) {
            $this->error("  ❌ {$invalidAssignments} utilisateur(s) assigné(s) à un magasin inexistant");
            $warnings++;
        }

        return $warnings;
    }

    private function validateProductPrices()
    {
        $this->info('💰 Validation des prix...');
        $errors = 0;

        $totalProducts = Product::active()->count();
        $storesCount = Store::count();
        $expectedPrices = $totalProducts * $storesCount;
        $actualPrices = ProductPrice::active()->current()->count();

        $this->info("  ✅ {$totalProducts} produit(s) actif(s)");
        $this->info("  ✅ {$actualPrices} prix configuré(s)");
        
        if ($actualPrices < $expectedPrices) {
            $missing = $expectedPrices - $actualPrices;
            $this->warn("  ⚠️  {$missing} prix manquant(s) (attendu: {$expectedPrices})");
        }

        // Vérifier les prix invalides
        $invalidPrices = ProductPrice::where('cost_price', '<', 0)
            ->orWhere('selling_price', '<=', 0)
            ->count();

        if ($invalidPrices > 0) {
            $this->error("  ❌ {$invalidPrices} prix invalide(s) (négatifs ou nuls)");
            $errors++;
        }

        return $errors;
    }

    private function validateStocks()
    {
        $this->info('📦 Validation des stocks...');
        $errors = 0;

        $totalProducts = Product::active()->count();
        $storesCount = Store::count();
        $expectedStocks = $totalProducts * $storesCount;
        $actualStocks = StoreStock::active()->count();

        $this->info("  ✅ {$actualStocks} stock(s) configuré(s)");
        
        if ($actualStocks < $expectedStocks) {
            $missing = $expectedStocks - $actualStocks;
            $this->warn("  ⚠️  {$missing} stock(s) manquant(s) (attendu: {$expectedStocks})");
        }

        // Vérifier les stocks négatifs
        $negativeStocks = StoreStock::where('quantity', '<', 0)->count();
        if ($negativeStocks > 0) {
            $this->error("  ❌ {$negativeStocks} stock(s) négatif(s)");
            $errors++;
        }

        // Vérifier les seuils incohérents
        $invalidThresholds = StoreStock::whereColumn('min_stock_level', '>', 'max_stock_level')
            ->whereNotNull('max_stock_level')
            ->count();
        
        if ($invalidThresholds > 0) {
            $this->error("  ❌ {$invalidThresholds} seuil(s) de stock incohérent(s)");
            $errors++;
        }

        return $errors;
    }

    private function validateSales()
    {
        $this->info('🛒 Validation des ventes...');
        $errors = 0;

        $totalSales = Sale::count();
        $salesWithStore = Sale::whereNotNull('store_id')->count();
        $salesWithoutStore = $totalSales - $salesWithStore;

        $this->info("  ✅ {$totalSales} vente(s) au total");
        $this->info("  ✅ {$salesWithStore} vente(s) avec magasin");
        
        if ($salesWithoutStore > 0) {
            $this->error("  ❌ {$salesWithoutStore} vente(s) sans magasin assigné");
            $errors++;
        }

        // Vérifier les ventes avec magasin inexistant
        $invalidSales = Sale::whereNotNull('store_id')
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                    ->from('stores')
                    ->whereColumn('stores.id', 'sales.store_id');
            })->count();

        if ($invalidSales > 0) {
            $this->error("  ❌ {$invalidSales} vente(s) avec magasin inexistant");
            $errors++;
        }

        return $errors;
    }

    private function validateCustomers()
    {
        $this->info('👤 Validation des clients...');
        $warnings = 0;

        $totalCustomers = Customer::count();
        $customersWithStore = Customer::whereNotNull('store_id')->count();
        $customersWithoutStore = $totalCustomers - $customersWithStore;

        $this->info("  ✅ {$totalCustomers} client(s) au total");
        $this->info("  ✅ {$customersWithStore} client(s) avec magasin");
        
        if ($customersWithoutStore > 0) {
            $this->warn("  ⚠️  {$customersWithoutStore} client(s) sans magasin assigné");
            $warnings++;
        }

        return $warnings;
    }

    private function validateDataIntegrity()
    {
        $this->info('🔗 Validation de l\'intégrité des données...');
        $errors = 0;

        // Vérifier que tous les produits vendus ont un prix dans le magasin
        $salesWithoutPrice = DB::table('sale_items')
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->leftJoin('product_prices', function($join) {
                $join->on('sale_items.product_id', '=', 'product_prices.product_id')
                     ->on('sales.store_id', '=', 'product_prices.store_id')
                     ->where('product_prices.is_active', true);
            })
            ->whereNull('product_prices.id')
            ->count();

        if ($salesWithoutPrice > 0) {
            $this->error("  ❌ {$salesWithoutPrice} vente(s) de produits sans prix configuré");
            $errors++;
        }

        // Vérifier que tous les produits vendus ont un stock dans le magasin
        $salesWithoutStock = DB::table('sale_items')
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->leftJoin('store_stocks', function($join) {
                $join->on('sale_items.product_id', '=', 'store_stocks.product_id')
                     ->on('sales.store_id', '=', 'store_stocks.store_id')
                     ->where('store_stocks.is_active', true);
            })
            ->whereNull('store_stocks.id')
            ->count();

        if ($salesWithoutStock > 0) {
            $this->error("  ❌ {$salesWithoutStock} vente(s) de produits sans stock configuré");
            $errors++;
        }

        if ($errors === 0) {
            $this->info("  ✅ Intégrité des données validée");
        }

        return $errors;
    }
}
