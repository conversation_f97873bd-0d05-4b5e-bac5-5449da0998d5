# Guide POS Multi-Magasin

## Vue d'ensemble

L'interface POS a été mise à jour pour supporter la gestion multi-magasin avec calcul automatique des bénéfices. Chaque utilisateur est assigné à un magasin spécifique et ne peut voir que les produits, stocks et prix de son magasin.

## Nouvelles Fonctionnalités

### 1. Contexte du Magasin

- **En-tête enrichi** : Affiche le nom, code et adresse du magasin actuel
- **Statistiques en temps réel** : Nombre de produits en stock, alertes stock faible/rupture
- **Identification visuelle** : Code du magasin affiché dans le panier

### 2. Gestion des Produits par Magasin

#### Affichage des Produits
- **Stock par magasin** : Chaque produit affiche son stock spécifique au magasin
- **Prix par magasin** : Utilisation des prix définis pour le magasin actuel
- **Emplacement** : Affichage de l'emplacement du produit dans le magasin
- **Statuts visuels** :
  - 🔴 Rupture de stock (produit grisé, non sélectionnable)
  - 🟡 Stock faible (badge jaune)
  - 🟠 Prix non défini (badge orange)

#### Validation des Stocks
- **Contrôle automatique** : Vérification du stock avant ajout au panier
- **Messages d'erreur** : Alertes pour rupture de stock ou prix manquant
- **Limites de quantité** : Impossible de dépasser le stock disponible

### 3. Modal de Prix Améliorée

#### Informations Contextuelles
- **Prix du magasin** : Affichage du prix de vente et coût définis
- **Bouton "Utiliser ce prix"** : Pré-remplissage automatique
- **Calcul de bénéfice en temps réel** :
  - Bénéfice estimé par article
  - Marge bénéficiaire en pourcentage
  - Mise à jour automatique lors des modifications

#### Validation Avancée
- **Stock maximum** : Contrôle de la quantité disponible
- **Prix minimum** : Validation des prix positifs
- **Emplacement** : Affichage de l'emplacement du produit

### 4. Panier Multi-Magasin

#### Affichage Enrichi
- **Code magasin** : Identification du magasin dans l'en-tête du panier
- **Bénéfice par article** : Calcul et affichage du bénéfice pour chaque ligne
- **Marge par article** : Pourcentage de marge affiché

#### Totaux Avancés
- **Résumé des bénéfices** : 
  - Bénéfice total de la vente
  - Marge bénéficiaire globale
  - Prise en compte des remises dans le calcul

### 5. Reçu Multi-Magasin

#### Informations du Magasin
- **En-tête personnalisé** : Nom, adresse, téléphone du magasin
- **Code magasin** : Identification claire du point de vente
- **Contact** : Email et téléphone si définis

#### Données de Bénéfice
- **Bénéfice réalisé** : Montant total du bénéfice (discret)
- **Traçabilité** : Lien avec le magasin pour les rapports

## Utilisation

### Pour les Vendeurs

1. **Connexion** : Se connecter avec un compte assigné à un magasin
2. **Sélection produit** : Cliquer sur un produit disponible (non grisé)
3. **Prix automatique** : Le prix du magasin est pré-rempli
4. **Validation stock** : Le système vérifie automatiquement la disponibilité
5. **Finalisation** : Procéder au paiement normalement

### Pour les Gestionnaires

1. **Vue d'ensemble** : Statistiques du magasin en en-tête
2. **Alertes visuelles** : Identification rapide des problèmes de stock
3. **Suivi bénéfices** : Visualisation en temps réel des marges

## Sécurité et Contrôles

### Isolation des Données
- **Produits** : Seuls les produits avec stock et prix dans le magasin sont visibles
- **Clients** : Accès limité aux clients du magasin
- **Stocks** : Validation stricte des quantités disponibles

### Validation des Transactions
- **Store ID** : Chaque vente est liée au magasin de l'utilisateur
- **Prix de revient** : Transmission pour calcul automatique du bénéfice
- **Intégrité** : Vérifications côté serveur et client

## Dépannage

### Problèmes Courants

1. **Aucun produit visible**
   - Vérifier l'assignation du magasin à l'utilisateur
   - Contrôler que des produits ont des stocks et prix définis

2. **Prix non défini**
   - Aller dans Gestion → Prix par magasin
   - Définir les prix pour les produits concernés

3. **Stock incorrect**
   - Vérifier dans Gestion → Stocks par magasin
   - Ajuster les quantités si nécessaire

### Messages d'Erreur

- **"Ce produit est en rupture de stock"** : Stock = 0 dans le magasin
- **"Aucun prix défini"** : Pas de prix actif pour ce produit/magasin
- **"Stock insuffisant"** : Quantité demandée > stock disponible

## Configuration Requise

### Prérequis
- Utilisateur assigné à un magasin via `users.store_id`
- Produits avec stocks définis dans `store_stocks`
- Prix définis dans `product_prices` pour le magasin
- Rôles et permissions appropriés

### Données Nécessaires
- **StoreStock** : Quantités et emplacements par magasin
- **ProductPrice** : Prix de vente et coût par magasin
- **Store** : Informations complètes du magasin

Cette mise à jour transforme le POS en un système véritablement multi-magasin avec gestion avancée des bénéfices et contrôles de sécurité renforcés.
