@props([
    'headers' => [],
    'searchable' => false,
    'sortable' => false,
    'pagination' => null,
    'emptyMessage' => 'Aucune donnée disponible',
    'emptyIcon' => 'fas fa-inbox'
])

<div {{ $attributes->merge(['class' => 'bg-white rounded-xl shadow-soft border border-gray-100 overflow-hidden']) }}>
    
    @if($searchable || isset($filters))
    <!-- Search and Filters -->
    <div class="p-4 border-b border-gray-100 bg-gray-50">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            @if($searchable)
            <div class="relative flex-1 max-w-md">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="text" 
                       placeholder="Rechercher..." 
                       class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
            </div>
            @endif
            
            @isset($filters)
            <div class="flex items-center space-x-3">
                {{ $filters }}
            </div>
            @endisset
        </div>
    </div>
    @endif

    <!-- Table -->
    <div class="overflow-x-auto scrollbar-thin">
        <table class="min-w-full divide-y divide-gray-200">
            @if(!empty($headers))
            <thead class="bg-gray-50">
                <tr>
                    @foreach($headers as $header)
                    <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                        @if(is_array($header))
                            <div class="flex items-center space-x-1">
                                <span>{{ $header['label'] }}</span>
                                @if($sortable && isset($header['sortable']) && $header['sortable'])
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-sort text-xs"></i>
                                </button>
                                @endif
                            </div>
                        @else
                            {{ $header }}
                        @endif
                    </th>
                    @endforeach
                </tr>
            </thead>
            @endif
            
            <tbody class="bg-white divide-y divide-gray-200">
                {{ $slot }}
            </tbody>
        </table>
    </div>

    <!-- Empty State -->
    @isset($empty)
    <div class="text-center py-12">
        <i class="{{ $emptyIcon }} text-gray-300 text-6xl mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $emptyMessage }}</h3>
        {{ $empty }}
    </div>
    @endisset

    <!-- Pagination -->
    @if($pagination)
    <div class="px-6 py-4 border-t border-gray-100 bg-gray-50">
        {{ $pagination }}
    </div>
    @endif
</div>
