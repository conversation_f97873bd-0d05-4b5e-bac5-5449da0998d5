<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Store;
use Carbon\Carbon;

class SecurityAuditService
{
    const MAX_FAILED_ATTEMPTS = 5;
    const LOCKOUT_DURATION = 900; // 15 minutes
    const SUSPICIOUS_ACTIVITY_THRESHOLD = 10;

    /**
     * Enregistrer une tentative d'accès
     */
    public function logAccessAttempt($userId, $storeId, $action, $success = true, $details = [])
    {
        $logData = [
            'user_id' => $userId,
            'store_id' => $storeId,
            'action' => $action,
            'success' => $success,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
            'details' => $details
        ];

        // Enregistrer dans les logs
        if ($success) {
            Log::info('Store access successful', $logData);
        } else {
            Log::warning('Store access failed', $logData);
        }

        // Enregistrer en base de données pour analyse
        DB::table('security_audit_logs')->insert([
            'user_id' => $userId,
            'store_id' => $storeId,
            'action' => $action,
            'success' => $success,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'details' => json_encode($details),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Vérifier les activités suspectes
        if (!$success) {
            $this->checkSuspiciousActivity($userId, request()->ip());
        }
    }

    /**
     * Vérifier les activités suspectes
     */
    public function checkSuspiciousActivity($userId, $ipAddress)
    {
        $cacheKey = "failed_attempts_{$userId}_{$ipAddress}";
        $attempts = Cache::get($cacheKey, 0);
        $attempts++;

        Cache::put($cacheKey, $attempts, self::LOCKOUT_DURATION);

        if ($attempts >= self::MAX_FAILED_ATTEMPTS) {
            $this->lockoutUser($userId, $ipAddress);
            
            // Notifier les administrateurs
            $this->notifyAdministrators($userId, $ipAddress, $attempts);
        }

        return $attempts;
    }

    /**
     * Verrouiller un utilisateur temporairement
     */
    public function lockoutUser($userId, $ipAddress)
    {
        $lockoutKey = "lockout_{$userId}_{$ipAddress}";
        Cache::put($lockoutKey, true, self::LOCKOUT_DURATION);

        Log::alert('User locked out due to suspicious activity', [
            'user_id' => $userId,
            'ip_address' => $ipAddress,
            'lockout_duration' => self::LOCKOUT_DURATION
        ]);
    }

    /**
     * Vérifier si un utilisateur est verrouillé
     */
    public function isUserLockedOut($userId, $ipAddress)
    {
        $lockoutKey = "lockout_{$userId}_{$ipAddress}";
        return Cache::has($lockoutKey);
    }

    /**
     * Analyser les patterns d'accès suspects
     */
    public function analyzeSuspiciousPatterns($userId, $timeframe = 24)
    {
        $since = Carbon::now()->subHours($timeframe);
        
        $activities = DB::table('security_audit_logs')
            ->where('user_id', $userId)
            ->where('created_at', '>=', $since)
            ->orderBy('created_at', 'desc')
            ->get();

        $patterns = [
            'multiple_store_access' => $this->detectMultipleStoreAccess($activities),
            'rapid_requests' => $this->detectRapidRequests($activities),
            'unusual_hours' => $this->detectUnusualHours($activities),
            'multiple_ip_addresses' => $this->detectMultipleIPs($activities),
            'failed_attempts_pattern' => $this->detectFailedAttemptsPattern($activities)
        ];

        return $patterns;
    }

    /**
     * Détecter l'accès à plusieurs magasins
     */
    private function detectMultipleStoreAccess($activities)
    {
        $stores = $activities->pluck('store_id')->unique();
        return [
            'detected' => $stores->count() > 3,
            'store_count' => $stores->count(),
            'stores' => $stores->toArray()
        ];
    }

    /**
     * Détecter les requêtes rapides
     */
    private function detectRapidRequests($activities)
    {
        $rapidRequests = 0;
        $previousTime = null;

        foreach ($activities as $activity) {
            $currentTime = Carbon::parse($activity->created_at);
            
            if ($previousTime && $currentTime->diffInSeconds($previousTime) < 2) {
                $rapidRequests++;
            }
            
            $previousTime = $currentTime;
        }

        return [
            'detected' => $rapidRequests > 20,
            'rapid_request_count' => $rapidRequests
        ];
    }

    /**
     * Détecter les accès à des heures inhabituelles
     */
    private function detectUnusualHours($activities)
    {
        $unusualHours = 0;
        
        foreach ($activities as $activity) {
            $hour = Carbon::parse($activity->created_at)->hour;
            
            // Considérer 22h-6h comme heures inhabituelles
            if ($hour >= 22 || $hour <= 6) {
                $unusualHours++;
            }
        }

        return [
            'detected' => $unusualHours > 5,
            'unusual_hour_count' => $unusualHours
        ];
    }

    /**
     * Détecter plusieurs adresses IP
     */
    private function detectMultipleIPs($activities)
    {
        $ips = $activities->pluck('ip_address')->unique();
        
        return [
            'detected' => $ips->count() > 3,
            'ip_count' => $ips->count(),
            'ips' => $ips->toArray()
        ];
    }

    /**
     * Détecter les patterns d'échecs
     */
    private function detectFailedAttemptsPattern($activities)
    {
        $failedAttempts = $activities->where('success', false)->count();
        $totalAttempts = $activities->count();
        $failureRate = $totalAttempts > 0 ? ($failedAttempts / $totalAttempts) * 100 : 0;

        return [
            'detected' => $failureRate > 30,
            'failed_attempts' => $failedAttempts,
            'total_attempts' => $totalAttempts,
            'failure_rate' => round($failureRate, 2)
        ];
    }

    /**
     * Notifier les administrateurs
     */
    public function notifyAdministrators($userId, $ipAddress, $attempts)
    {
        $user = User::find($userId);
        $message = "Activité suspecte détectée pour l'utilisateur {$user->name} (ID: {$userId}) depuis l'IP {$ipAddress}. {$attempts} tentatives échouées.";

        // Envoyer notification aux super admins
        $admins = User::role('superAdmin')->get();
        
        foreach ($admins as $admin) {
            // Ici vous pouvez implémenter l'envoi d'email, SMS, etc.
            Log::alert('Security alert sent to admin', [
                'admin_id' => $admin->id,
                'message' => $message
            ]);
        }
    }

    /**
     * Générer un rapport de sécurité
     */
    public function generateSecurityReport($storeId = null, $days = 7)
    {
        $since = Carbon::now()->subDays($days);
        
        $query = DB::table('security_audit_logs')
            ->where('created_at', '>=', $since);
            
        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        $logs = $query->get();

        return [
            'total_attempts' => $logs->count(),
            'failed_attempts' => $logs->where('success', false)->count(),
            'unique_users' => $logs->pluck('user_id')->unique()->count(),
            'unique_ips' => $logs->pluck('ip_address')->unique()->count(),
            'most_active_users' => $this->getMostActiveUsers($logs),
            'most_common_failures' => $this->getMostCommonFailures($logs),
            'hourly_distribution' => $this->getHourlyDistribution($logs),
            'suspicious_activities' => $this->getSuspiciousActivities($logs)
        ];
    }

    /**
     * Obtenir les utilisateurs les plus actifs
     */
    private function getMostActiveUsers($logs)
    {
        return $logs->groupBy('user_id')
            ->map(function($userLogs) {
                return [
                    'user_id' => $userLogs->first()->user_id,
                    'total_attempts' => $userLogs->count(),
                    'failed_attempts' => $userLogs->where('success', false)->count()
                ];
            })
            ->sortByDesc('total_attempts')
            ->take(10)
            ->values();
    }

    /**
     * Obtenir les échecs les plus communs
     */
    private function getMostCommonFailures($logs)
    {
        return $logs->where('success', false)
            ->groupBy('action')
            ->map(function($actionLogs, $action) {
                return [
                    'action' => $action,
                    'count' => $actionLogs->count()
                ];
            })
            ->sortByDesc('count')
            ->take(5)
            ->values();
    }

    /**
     * Obtenir la distribution horaire
     */
    private function getHourlyDistribution($logs)
    {
        $distribution = [];
        
        for ($hour = 0; $hour < 24; $hour++) {
            $distribution[$hour] = 0;
        }

        foreach ($logs as $log) {
            $hour = Carbon::parse($log->created_at)->hour;
            $distribution[$hour]++;
        }

        return $distribution;
    }

    /**
     * Obtenir les activités suspectes
     */
    private function getSuspiciousActivities($logs)
    {
        $suspicious = [];
        
        $userGroups = $logs->groupBy('user_id');
        
        foreach ($userGroups as $userId => $userLogs) {
            $patterns = $this->analyzeSuspiciousPatterns($userId, 7);
            
            $suspiciousCount = collect($patterns)->filter(function($pattern) {
                return $pattern['detected'] ?? false;
            })->count();
            
            if ($suspiciousCount > 0) {
                $suspicious[] = [
                    'user_id' => $userId,
                    'suspicious_patterns' => $suspiciousCount,
                    'patterns' => $patterns
                ];
            }
        }

        return collect($suspicious)->sortByDesc('suspicious_patterns')->values();
    }
}
