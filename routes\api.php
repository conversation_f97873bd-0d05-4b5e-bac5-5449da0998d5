<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ReportsApiController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Routes API pour les rapports (avec authentification web)
Route::middleware(['web', 'auth'])->prefix('reports')->group(function () {
    Route::get('/quick-metrics', [ReportsApiController::class, 'quickMetrics']);
    Route::get('/sales-trend', [ReportsApiController::class, 'salesTrend']);
    Route::get('/profit-by-category', [ReportsApiController::class, 'profitByCategory']);
});
