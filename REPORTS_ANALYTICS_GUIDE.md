# Guide Complet - Rapports et Analytics Multi-Magasin

## Vue d'ensemble

Le système de rapports et analytics a été entièrement repensé pour supporter l'architecture multi-magasin avec des fonctionnalités avancées de suivi des bénéfices, marges, et performances.

## Architecture du Système

### 1. Contrôleurs

#### ReportsController
- **Localisation** : `app/Http/Controllers/ReportsController.php`
- **Fonctions principales** :
  - `index()` : Page d'accueil des rapports
  - `profitAnalytics()` : Analytics détaillées des bénéfices
  - `stockAnalytics()` : Analytics des stocks et rotations
  - `exportProfitReport()` : Export des rapports (à implémenter)

#### ReportsApiController
- **Localisation** : `app/Http/Controllers/Api/ReportsApiController.php`
- **Endpoints API** :
  - `/api/reports/quick-metrics` : Métriques rapides du jour
  - `/api/reports/sales-trend` : Tendances des ventes (7 jours)
  - `/api/reports/profit-by-category` : Bénéfices par catégorie

### 2. Vues

#### Page d'accueil des rapports
- **Fichier** : `resources/views/reports/index.blade.php`
- **Fonctionnalités** :
  - Navigation vers les différents types de rapports
  - Aperçu rapide des métriques du jour
  - Graphiques de tendances en temps réel
  - Actions rapides pour les rapports fréquents

#### Analytics de bénéfices
- **Fichier** : `resources/views/reports/profit-analytics.blade.php`
- **Fonctionnalités** :
  - Filtres avancés (magasin, période, dates)
  - Métriques principales (CA, bénéfices, marges, transactions)
  - Graphiques d'évolution temporelle
  - Top 10 des produits les plus rentables
  - Performance par magasin (pour les admins)
  - Analyse des marges par catégorie

#### Analytics de stock
- **Fichier** : `resources/views/reports/stock-analytics.blade.php`
- **Fonctionnalités** :
  - Filtres par magasin, statut, catégorie
  - Métriques de stock (normal, faible, rupture, valeur)
  - Graphiques de répartition
  - Vue tableau et cartes pour le détail des stocks
  - Alertes visuelles par statut

## Fonctionnalités Principales

### 1. Gestion Multi-Magasin

#### Permissions et Accès
- **Super Admin / Admin** : Accès à tous les magasins
- **Manager / Vendeur** : Accès limité à leur magasin assigné
- **Filtrage automatique** : Les données sont filtrées selon les permissions

#### Sélection de Magasin
- Dropdown de sélection pour les admins
- Affichage du contexte du magasin actuel
- Isolation complète des données par magasin

### 2. Analytics de Bénéfices

#### Métriques Calculées
- **Chiffre d'affaires** : Somme des ventes sur la période
- **Bénéfices totaux** : Somme des profits réalisés
- **Marge moyenne** : Pourcentage de profit sur le CA
- **Nombre de transactions** : Compteur des ventes
- **Panier moyen** : Valeur moyenne par transaction

#### Analyses Temporelles
- **Périodes** : Quotidien, hebdomadaire, mensuel
- **Graphiques d'évolution** : CA, bénéfices, marges
- **Comparaisons** : Tendances et variations

#### Top Produits
- **Classement** : Par bénéfice total généré
- **Métriques** : Quantité vendue, CA, profit, marge
- **Indicateurs visuels** : Codes couleur selon la marge

### 3. Analytics de Stock

#### Statuts de Stock
- **Normal** : Stock au-dessus du seuil
- **Faible** : Stock en dessous du seuil d'alerte
- **Rupture** : Stock à zéro

#### Analyses de Valeur
- **Valeur totale** : Coût du stock en magasin
- **Répartition par catégorie** : Graphiques de distribution
- **Alertes** : Identification des problèmes de stock

#### Vues Multiples
- **Tableau détaillé** : Vue complète avec tous les champs
- **Vue cartes** : Affichage visuel par produit
- **Filtres avancés** : Par statut, catégorie, magasin

### 4. Graphiques et Visualisations

#### Technologies Utilisées
- **Chart.js** : Bibliothèque de graphiques JavaScript
- **Types de graphiques** :
  - Lignes : Évolutions temporelles
  - Barres : Comparaisons par catégorie
  - Donuts : Répartitions et pourcentages

#### Graphiques Disponibles
- **Évolution des bénéfices** : Ligne temporelle avec CA et profits
- **Bénéfices par catégorie** : Donut de répartition
- **Tendances des ventes** : Ligne sur 7 jours
- **Statuts de stock** : Donut de répartition
- **Valeur par catégorie** : Barres de comparaison

### 5. Exports et Rapports

#### Formats Supportés
- **PDF** : Rapports formatés pour impression
- **Excel** : Données pour analyse approfondie
- **Vue en ligne** : Consultation directe

#### Types de Rapports
- **Rapports de bénéfices** : Analytics complètes
- **Rapports de stock** : État des inventaires
- **Rapports classiques** : Ventes, crédits (existants)

## Configuration et Utilisation

### 1. Prérequis

#### Base de Données
- Tables multi-magasin configurées
- Données de bénéfices dans les ventes
- Stocks par magasin définis
- Prix par magasin configurés

#### Permissions Utilisateur
- Rôles définis (superAdmin, admin, manager)
- Assignation des utilisateurs aux magasins
- Middleware de contrôle d'accès

### 2. Navigation

#### Accès aux Rapports
1. Menu principal → Rapports
2. Sélection du type d'analytics
3. Configuration des filtres
4. Consultation des résultats

#### Filtres Disponibles
- **Magasin** : Sélection pour les admins
- **Période** : Dates de début et fin
- **Granularité** : Jour, semaine, mois
- **Catégorie** : Filtrage par type de produit
- **Statut** : Pour les analyses de stock

### 3. Interprétation des Données

#### Indicateurs de Performance
- **Marge > 30%** : Excellente rentabilité (vert)
- **Marge 15-30%** : Rentabilité correcte (jaune)
- **Marge < 15%** : Rentabilité faible (rouge)

#### Alertes de Stock
- **Stock normal** : Quantité > seuil (vert)
- **Stock faible** : Quantité ≤ seuil (jaune)
- **Rupture** : Quantité = 0 (rouge)

## API et Intégrations

### 1. Endpoints API

#### Métriques Rapides
```
GET /api/reports/quick-metrics
Response: {
  "todaySales": 150000,
  "todayProfit": 45000,
  "todayTransactions": 25,
  "todayMargin": 30.0
}
```

#### Tendances des Ventes
```
GET /api/reports/sales-trend
Response: {
  "labels": ["24/07", "25/07", ...],
  "sales": [120000, 135000, ...],
  "profits": [36000, 40500, ...]
}
```

#### Bénéfices par Catégorie
```
GET /api/reports/profit-by-category
Response: {
  "labels": ["Électronique", "Vêtements", ...],
  "profits": [85000, 65000, ...]
}
```

### 2. Authentification
- **Middleware web** : Session Laravel
- **Contrôle d'accès** : Vérification des rôles
- **Filtrage automatique** : Selon le magasin de l'utilisateur

## Développements Futurs

### 1. Fonctionnalités Prévues
- **Exports avancés** : Excel avec graphiques
- **Rapports programmés** : Envoi automatique par email
- **Comparaisons temporelles** : Année précédente, mois précédent
- **Prévisions** : Tendances et projections
- **Alertes automatiques** : Notifications de seuils

### 2. Améliorations Techniques
- **Cache des rapports** : Optimisation des performances
- **Rapports en temps réel** : WebSockets pour les mises à jour
- **API REST complète** : Pour intégrations externes
- **Mobile responsive** : Optimisation pour tablettes/mobiles

## Maintenance et Support

### 1. Monitoring
- **Logs des accès** : Traçabilité des consultations
- **Performance** : Temps de génération des rapports
- **Erreurs** : Gestion des cas d'échec

### 2. Optimisations
- **Index de base de données** : Sur les champs de filtrage
- **Requêtes optimisées** : Éviter les N+1 queries
- **Pagination** : Pour les gros volumes de données

Ce système de rapports offre une vision complète et détaillée des performances de chaque magasin, permettant une prise de décision éclairée basée sur des données précises et actualisées.
