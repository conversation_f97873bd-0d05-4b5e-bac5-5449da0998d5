<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Credit;
use App\Models\Supplier;
use App\Models\Promotion;
use App\Models\SaleItem;
use App\Models\Store;
use App\Models\StoreStock;
use Spatie\Permission\Traits\HasRoles;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class DashboardController extends Controller
{
    public function __construct()
    {
        // Middleware pour authentification et restriction aux admins
        $this->middleware(['auth']);
    }

    public function index(Request $request)
    {
        try {
            Log::info('Début du chargement du dashboard', ['user_id' => Auth::id()]);

            $user = Auth::user();
            $userStore = $user->store;

            // Vérifier que l'utilisateur est assigné à un magasin ou est super admin
            if (!$userStore && !$user->hasRole('superAdmin')) {
                return redirect()->route('profile.edit')
                    ->with('error', 'Vous devez être assigné à un magasin pour accéder au dashboard.');
            }

            // Période sélectionnée pour les filtres (par défaut : mois en cours)
            $period = $request->input('period', 'month');

            // Initialiser le tableau de données
            $data = [
                'stats' => [],
                'advancedStats' => [],
                'charts' => [],
                'widgets' => [],
                'alerts' => [],
                'trends' => [],
                'employeeStats' => [],
                'userStore' => $userStore
            ];
            $data = [
                'stats' => [],
                'advancedStats' => [],
                'charts' => [],
                'widgets' => [],
                'alerts' => [],
                'trends' => [],
                'employeeStats' => [],
                'userStore' => $userStore
            ];

            try {
                $data['stats'] = $this->getMainStats($period, $userStore);
                $data['advancedStats'] = $this->getAdvancedStats($userStore);
                $data['charts'] = $this->getCharts($period);
                $data['widgets'] = $this->getWidgets($userStore);
                $data['alerts'] = $this->getAlerts();
                $data['trends'] = $this->getTrends();
                $data['employeeStats'] = $this->getEmployeeStats($userStore);
            } catch (\Exception $e) {
                Log::error('Erreur lors de la récupération des données: ' . $e->getMessage());
            }

            Log::info('Chargement du dashboard terminé avec succès');

            return view('dashboard', $data);
        } catch (Exception $e) {
            Log::error('Erreur lors du chargement du dashboard', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            if (config('app.debug')) {
                throw $e;
            }

            return redirect()->back()->with('error', __('dashboard.error_loading'));
        }
    }

    private function getMainStats($period = 'month', $store = null)
    {
        try {
            Log::info('Début du calcul des statistiques principales', ['period' => $period, 'store_id' => $store?->id]);

            // Clé de cache basée sur la période et le magasin
            $cacheKey = "dashboard_main_stats_{$period}_" . ($store?->id ?? 'all');

            return Cache::remember($cacheKey, now()->addMinutes(15), function () use ($period, $store) {
                $query = Sale::completed();

                // Filtrer par magasin si spécifié
                if ($store) {
                    $query->where('store_id', $store->id);
                }

                // Appliquer le filtre de période
                switch ($period) {
                    case 'today':
                        $query->today();
                        $yesterdayQuery = Sale::whereDate('created_at', Carbon::yesterday())->completed();
                        if ($store) $yesterdayQuery->where('store_id', $store->id);
                        $yesterdaySales = $yesterdayQuery->sum('total_amount');
                        break;
                    case 'month':
                        $query->thisMonth();
                        $lastMonthQuery = Sale::whereMonth('created_at', Carbon::now()->subMonth()->month)
                            ->whereYear('created_at', Carbon::now()->subMonth()->year)
                            ->completed();
                        if ($store) $lastMonthQuery->where('store_id', $store->id);
                        $yesterdaySales = $lastMonthQuery->sum('total_amount');
                        break;
                    case 'year':
                        $query->whereYear('created_at', Carbon::now()->year);
                        $lastYearQuery = Sale::whereYear('created_at', Carbon::now()->subYear()->year)
                            ->completed();
                        if ($store) $lastYearQuery->where('store_id', $store->id);
                        $yesterdaySales = $lastYearQuery->sum('total_amount');
                        break;
                }

                // Crédits du jour filtrés par magasin
                $creditsQuery = Credit::whereDate('created_at', Carbon::today());
                if ($store) {
                    $creditsQuery->whereHas('sale', function($q) use ($store) {
                        $q->where('store_id', $store->id);
                    });
                }

                $stats = [
                    'sales' => $query->sum('total_amount'),
                    'salesCount' => $query->count(),
                    'credits' => $creditsQuery->sum('total_amount'),
                    'creditsCount' => $creditsQuery->count(),
                    'yesterdaySales' => $yesterdaySales,
                    'averageOrderValue' => $query->avg('total_amount') ?? 0,
                ];

                Log::info('Statistiques principales calculées avec succès', ['stats' => $stats]);
                return $stats;
            });
        } catch (Exception $e) {
            Log::error('Erreur lors du calcul des statistiques principales', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'store_id' => $store?->id
            ]);
            throw $e;
        }
    }

    private function getAdvancedStats($store = null)
    {
        $cacheKey = 'dashboard_advanced_stats_' . ($store?->id ?? 'all');

        return Cache::remember($cacheKey, now()->addMinutes(30), function () use ($store) {
            // Filtrer les crédits par magasin
            $creditsQuery = Credit::active();
            $overdueCreditsQuery = Credit::overdue();
            $paidCreditsQuery = Credit::where('status', 'paid');

            if ($store) {
                $creditsQuery->whereHas('sale', function($q) use ($store) {
                    $q->where('store_id', $store->id);
                });
                $overdueCreditsQuery->whereHas('sale', function($q) use ($store) {
                    $q->where('store_id', $store->id);
                });
                $paidCreditsQuery->whereHas('sale', function($q) use ($store) {
                    $q->where('store_id', $store->id);
                });
            }

            // Filtrer les clients par magasin
            $customersQuery = Customer::active();
            $newCustomersQuery = Customer::whereMonth('created_at', Carbon::now()->month);

            if ($store) {
                $customersQuery->where('store_id', $store->id);
                $newCustomersQuery->where('store_id', $store->id);
            }

            // Statistiques de stock par magasin
            if ($store) {
                $lowStockProducts = $store->getLowStockProducts()->count();
                $outOfStockProducts = $store->storeStocks()->where('quantity', 0)->count();
                $totalProducts = $store->getTotalProducts();
            } else {
                $lowStockProducts = StoreStock::whereColumn('quantity', '<=', 'min_stock_level')->count();
                $outOfStockProducts = StoreStock::where('quantity', 0)->count();
                $totalProducts = Product::active()->count();
            }

            return [
                'totalCredits' => $creditsQuery->sum('remaining_balance'),
                'customersWithCredit' => Customer::withDebt()->count(),
                'overdueCredits' => $overdueCreditsQuery->count(),
                'totalCreditsPaid' => $paidCreditsQuery->sum('total_amount'),
                'lowStockProducts' => $lowStockProducts,
                'outOfStockProducts' => $outOfStockProducts,
                'totalProducts' => $totalProducts,
                'totalCustomers' => $customersQuery->count(),
                'newCustomersThisMonth' => $newCustomersQuery->count(),
                'totalSuppliers' => Supplier::active()->count(),
                'activePromotions' => Promotion::where('is_active', true)
                    ->where('start_date', '<=', Carbon::now())
                    ->where('end_date', '>=', Carbon::now())
                    ->count(),
            ];
        });
    }

    private function getCharts($period = '7days')
    {
        // Graphique des ventes par période
        $days = match ($period) {
            '30days' => 30,
            '90days' => 90,
            default => 7
        };

        $salesChart = Cache::remember("dashboard_sales_chart_{$period}", now()->addMinutes(15), function () use ($days) {
            $salesData = Sale::whereBetween('created_at', [
                Carbon::now()->subDays($days)->startOfDay(),
                Carbon::now()->endOfDay()
            ])
                ->completed()
                ->selectRaw('DATE(created_at) as sale_date, SUM(total_amount) as amount, COUNT(*) as count')
                ->groupBy('sale_date')
                ->get()
                ->keyBy('sale_date');

            $chart = [];
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $dateKey = $date->toDateString();
                $chart[] = [
                    'date' => $date->format('d/m'),
                    'amount' => $salesData[$dateKey]->amount ?? 0,
                    'count' => $salesData[$dateKey]->count ?? 0
                ];
            }
            return $chart;
        });

        // Graphique des ventes par mode de paiement
        $paymentMethodsChart = Cache::remember('dashboard_payment_methods_chart', now()->addMinutes(15), function () {
            return Sale::thisMonth()->completed()
                ->select('payment_method', DB::raw('SUM(total_amount) as total'), DB::raw('COUNT(*) as count'))
                ->groupBy('payment_method')
                ->get()
                ->map(function ($item) {
                    return [
                        'method' => $this->getPaymentMethodLabel($item->payment_method),
                        'total' => $item->total,
                        'count' => $item->count,
                        'color' => $this->getPaymentMethodColor($item->payment_method)
                    ];
                });
        });

        // Top 5 des produits
        $topProducts = Cache::remember('dashboard_top_products', now()->addMinutes(15), function () {
            return DB::table('sale_items')
                ->join('products', 'sale_items.product_id', '=', 'products.id')
                ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
                ->where('sales.status', 'completed')
                ->whereMonth('sales.created_at', Carbon::now()->month)
                ->select('products.name', DB::raw('SUM(sale_items.quantity) as total_quantity'), DB::raw('SUM(sale_items.total_price) as total_amount'))
                ->groupBy('products.id', 'products.name')
                ->orderBy('total_quantity', 'desc')
                ->take(5)
                ->get();
        });

        // Ventes par catégorie
        // $categorySalesChart = Cache::remember('dashboard_category_sales_chart', now()->addMinutes(15), function () {
        //     return Product::join('sale_items', 'products.id', '=', 'sale_items.product_id')
        //         ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
        //         ->where('sales.status', 'completed')
        //         ->whereMonth('sales.created_at', Carbon::now()->month)
        //         ->select('products.category', DB::raw('SUM(sale_items.total_price) as total'))
        //         ->groupBy('products.category')
        //         ->get()
        //         ->map(function ($item, $index) {
        //             return [
        //                 'category' => $item->category,
        //                 'total' => $item->total,
        //                 'color' => $this->getCategoryColor($index)
        //             ];
        //         });
        // });

        return [
            'salesChart' => $salesChart,
            'paymentMethodsChart' => $paymentMethodsChart,
            'topProducts' => $topProducts,
            //'categorySalesChart' => $categorySalesChart,
        ];
    }

    private function getWidgets($store = null)
    {
        // Ventes récentes filtrées par magasin
        $recentSalesQuery = Sale::with(['customer', 'user'])->latest();
        if ($store) {
            $recentSalesQuery->where('store_id', $store->id);
        }

        // Produits en rupture de stock filtrés par magasin
        $lowStockProducts = [];
        if ($store) {
            $lowStockProducts = $store->getLowStockProducts()->take(5);
        } else {
            $lowStockProducts = StoreStock::whereColumn('quantity', '<=', 'min_stock_level')
                ->with('product')
                ->take(5)
                ->get();
        }

        // Crédits en retard filtrés par magasin
        $overdueCreditsQuery = Credit::overdue()->with(['customer', 'sale']);
        if ($store) {
            $overdueCreditsQuery->whereHas('sale', function($q) use ($store) {
                $q->where('store_id', $store->id);
            });
        }

        // Top clients filtrés par magasin
        $topCustomersQuery = Customer::select('customers.*', DB::raw('SUM(sales.total_amount) as total_spent'))
            ->join('sales', 'customers.id', '=', 'sales.customer_id')
            ->where('sales.status', 'completed')
            ->whereMonth('sales.created_at', Carbon::now()->month);

        if ($store) {
            $topCustomersQuery->where('sales.store_id', $store->id)
                ->where('customers.store_id', $store->id);
        }

        return [
            'recentSales' => $recentSalesQuery->take(5)->get(),
            'lowStockProductsList' => $lowStockProducts,
            'overdueCreditsDetails' => $overdueCreditsQuery->take(5)->get(),
            'topCustomers' => $topCustomersQuery
                ->groupBy('customers.id')
                ->orderBy('total_spent', 'desc')
                ->take(5)
                ->get(),
        ];
    }

    public function getAlerts()
    {
        try {
            $alerts = [];

            $lowStockCount = Product::where('stock_quantity', '<=', 10)->count(); // Correction : utiliser 'stock_quantity'
            if ($lowStockCount > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'icon' => 'fas fa-exclamation-triangle',
                    'title' => __('dashboard.alerts.low_stock'),
                    'message' => trans_choice('dashboard.alerts.low_stock_message', $lowStockCount, ['count' => $lowStockCount]),
                    'action' => route('products.index'),
                    'action_text' => __('dashboard.alerts.view_products')
                ];
            }

            $overdueCount = Credit::where('due_date', '<', now())->count();
            if ($overdueCount > 0) {
                $alerts[] = [
                    'type' => 'danger',
                    'icon' => 'fas fa-clock',
                    'title' => __('dashboard.alerts.overdue_credits'),
                    'message' => trans_choice('dashboard.alerts.overdue_credits_message', $overdueCount, ['count' => $overdueCount]),
                    'action' => route('credits.index'),
                    'action_text' => __('dashboard.alerts.view_credits')
                ];
            }

            $expiringPromotions = Promotion::where('end_date', '>=', now())
                ->where('end_date', '<=', now()->addDays(3))
                ->count();
            if ($expiringPromotions > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'icon' => 'fas fa-info-circle',
                    'title' => __('dashboard.alerts.expiring_promotions'),
                    'message' => trans_choice('dashboard.alerts.expiring_promotions_message', $expiringPromotions, ['count' => $expiringPromotions]),
                    'action' => route('promotions.index'),
                    'action_text' => __('dashboard.alerts.view_promotions')
                ];
            }

            Log::info('Alertes générées : ' . json_encode($alerts)); // Log pour déboguer
            return [
                'alerts' => $alerts,
            ];
        } catch (\Exception $e) {
            Log::error('Erreur dans getAlerts: ' . $e->getMessage());
            return [
                'alerts' => []
            ];
        }
    }

    private function getTrends()
    {
        $lastWeekSales = Sale::whereBetween('created_at', [
            Carbon::now()->subWeeks(2)->startOfWeek(),
            Carbon::now()->subWeek()->endOfWeek()
        ])->completed()->sum('total_amount');

        $thisWeekSales = Sale::whereBetween('created_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ])->completed()->sum('total_amount');

        $salesTrend = $lastWeekSales > 0 ? (($thisWeekSales - $lastWeekSales) / $lastWeekSales) * 100 : 0;

        $last7DaysAvg = Sale::whereBetween('created_at', [
            Carbon::now()->subDays(7),
            Carbon::now()
        ])->completed()->avg('total_amount') ?? 0;

        $bestDayOfWeek = Sale::selectRaw('DAYOFWEEK(created_at) as day_of_week, AVG(total_amount) as avg_sales')
            ->where('created_at', '>=', Carbon::now()->subMonth())
            ->completed()
            ->groupBy('day_of_week')
            ->orderBy('avg_sales', 'desc')
            ->first();

        $bestDayName = $bestDayOfWeek ? $this->getDayName($bestDayOfWeek->day_of_week) : __('days.monday');

        $peakHour = Sale::selectRaw('HOUR(created_at) as hour, COUNT(*) as sales_count')
            ->where('created_at', '>=', Carbon::now()->subMonth())
            ->completed()
            ->groupBy('hour')
            ->orderBy('sales_count', 'desc')
            ->first();

        $peakHourFormatted = $peakHour ? $peakHour->hour . 'h' : '14h';

        return [
            'salesTrend' => $salesTrend,
            'tomorrowPrediction' => $last7DaysAvg,
            'bestDayOfWeek' => $bestDayName,
            'peakHour' => $peakHourFormatted,
            'weekComparison' => [
                'lastWeek' => $lastWeekSales,
                'thisWeek' => $thisWeekSales
            ]
        ];
    }

    public function getEmployeeStats($store = null)
    {
        try {
            if (!$store && !Auth::user()->hasRole('superAdmin')) {
                return ['employeeStats' => []];
            }

            $cacheKey = 'dashboard_employee_stats_' . ($store?->id ?? 'all');

            $employeeStats = Cache::remember($cacheKey, now()->addMinutes(15), function () use ($store) {
                $query = DB::table('sales')
                    ->join('users', 'sales.user_id', '=', 'users.id')
                    ->whereMonth('sales.created_at', Carbon::now()->month)
                    ->whereYear('sales.created_at', Carbon::now()->year)
                    ->where('sales.status', 'completed');

                // Filtrer par magasin si spécifié
                if ($store) {
                    $query->where('sales.store_id', $store->id);
                }

                return $query
                    ->select(
                        'users.name',
                        DB::raw('SUM(sales.total_amount) as total_sales'),
                        DB::raw('COUNT(*) as sales_count'),
                        DB::raw('SUM(sale_items.profit_amount) as total_profit')
                    )
                    ->leftJoin('sale_items', 'sales.id', '=', 'sale_items.sale_id')
                    ->groupBy('users.id', 'users.name')
                    ->orderBy('total_sales', 'desc')
                    ->take(5)
                    ->get()
                    ->map(function ($item) {
                        return [
                            'name' => $item->name,
                            'sales_count' => $item->sales_count,
                            'total_sales' => $item->total_sales,
                            'total_profit' => $item->total_profit ?? 0,
                        ];
                    })
                    ->toArray();
            });

            Log::info('Employee stats générés : ' . json_encode($employeeStats)); // Log pour déboguer

            return $employeeStats;
        } catch (\Exception $e) {
            Log::error('Erreur dans getEmployeeStats: ' . $e->getMessage());
            return [
                'employeeStats' => []
            ];
        }
    }

    private function getDayName($dayOfWeek)
    {
        $days = [
            1 => __('days.sunday'),
            2 => __('days.monday'),
            3 => __('days.tuesday'),
            4 => __('days.wednesday'),
            5 => __('days.thursday'),
            6 => __('days.friday'),
            7 => __('days.saturday')
        ];

        return $days[$dayOfWeek] ?? __('days.monday');
    }

    private function getPaymentMethodLabel($method)
    {
        return match ($method) {
            'cash' => __('payment_methods.cash'),
            'credit' => __('payment_methods.credit'),
            default => ucfirst($method)
        };
    }

    private function getPaymentMethodColor($method)
    {
        return match ($method) {
            'cash' => '#10B981', // green
            'mobile_money' => '#3B82F6', // blue
            'credit' => '#F59E0B', // yellow
            default => '#6B7280' // gray
        };
    }


    public function getStats(Request $request)
    {
        try {
            $period = $request->query('period', 'month');
            $today = now()->startOfDay();
            $startDate = match ($period) {
                'today' => $today,
                'month' => now()->startOfMonth(),
                'year' => now()->startOfYear(),
                default => now()->startOfMonth(),
            };

            $sales = Sale::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->selectRaw('SUM(total_amount) as salesValue, COUNT(*) as completedOrders')
                ->first();

            $pendingOrders = Sale::where('created_at', '>=', $startDate)
                ->where('status', 'pending')
                ->selectRaw('SUM(total_amount) as dueAmount, COUNT(*) as pendingOrders')
                ->first();

            $lowStockCount = Product::where('stock_quantity', '<=', 10)->count();
            $totalProducts = Product::count();

            $todaySales = Sale::where('created_at', '>=', $today)
                ->where('status', 'completed')
                ->selectRaw('SUM(total_amount) as todaySales, COUNT(*) as todaySalesCount')
                ->first();

            $todayCredits = Sale::where('created_at', '>=', $today)
                ->where('status', 'pending')
                ->selectRaw('SUM(total_amount) as todayCredits, COUNT(*) as todayCreditsCount')
                ->first();

            $averageOrderValue = Sale::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->count() > 0
                ? Sale::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->avg('total_amount')
                : 0;

            Log::info('averageOrderValue calculé : ' . $averageOrderValue);

            $bestSellers = SaleItem::whereHas('sale', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)->where('status', 'completed');
            })
                ->groupBy('product_id')
                ->selectRaw('products.name, SUM(sale_items.quantity) as total_quantity, SUM(sale_items.quantity * sale_items.unit_price) as total_amount')
                ->leftJoin('products', 'sale_items.product_id', '=', 'products.id')
                ->orderByDesc('total_quantity')
                ->take(5)
                ->get();

            $recentSales = Sale::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->with('customer')
                ->orderByDesc('created_at')
                ->take(5)
                ->get();

            return response()->json([
                'success' => true,
                'stats' => [
                    'salesValue' => round($sales->salesValue ?? 0, 2),
                    'completedOrders' => $sales->completedOrders ?? 0,
                    'dueAmount' => round($pendingOrders->dueAmount ?? 0, 2),
                    'pendingOrders' => $pendingOrders->pendingOrders ?? 0,
                    'lowStockCount' => $lowStockCount,
                    'totalProducts' => $totalProducts,
                    'todaySales' => round($todaySales->todaySales ?? 0, 2),
                    'todaySalesCount' => $todaySales->todaySalesCount ?? 0,
                    'todayCredits' => round($todayCredits->todayCredits ?? 0, 2),
                    'todayCreditsCount' => $todayCredits->todayCreditsCount ?? 0,
                    'averageOrderValue' => round($averageOrderValue ?? 0, 2),
                    'bestSellers' => $bestSellers,
                    'recentSales' => $recentSales,
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur dans getStats: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'error' => __('dashboard.error_stats'),
                'details' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    public function getChartData(Request $request, $type)
    {
        try {
            $period = $request->input('period', '7days');

            switch ($type) {
                case 'sales':
                    $days = match ($period) {
                        '30days' => 30,
                        '90days' => 90,
                        default => 7
                    };

                    $salesChart = Cache::remember("dashboard_sales_chart_{$period}", now()->addMinutes(15), function () use ($days) {
                        $salesData = Sale::whereBetween('created_at', [
                            Carbon::now()->subDays($days)->startOfDay(),
                            Carbon::now()->endOfDay()
                        ])
                            ->completed()
                            ->selectRaw('DATE(created_at) as sale_date, SUM(total_amount) as amount, COUNT(*) as count')
                            ->groupBy('sale_date')
                            ->get()
                            ->keyBy('sale_date');

                        $chart = [];
                        for ($i = $days - 1; $i >= 0; $i--) {
                            $date = Carbon::now()->subDays($i);
                            $dateKey = $date->toDateString();
                            $chart[] = [
                                'date' => $date->format('d/m'),
                                'amount' => $salesData[$dateKey]->amount ?? 0,
                                'count' => $salesData[$dateKey]->count ?? 0
                            ];
                        }
                        return $chart;
                    });
                    return response()->json($salesChart);

                case 'payment-methods':
                    $paymentMethodsChart = Cache::remember('dashboard_payment_methods_chart', now()->addMinutes(15), function () {
                        return Sale::thisMonth()->completed()
                            ->select('payment_method', DB::raw('SUM(total_amount) as total'), DB::raw('COUNT(*) as count'))
                            ->groupBy('payment_method')
                            ->get()
                            ->map(function ($item) {
                                return [
                                    'method' => $this->getPaymentMethodLabel($item->payment_method),
                                    'total' => $item->total,
                                    'count' => $item->count,
                                    'color' => $this->getPaymentMethodColor($item->payment_method)
                                ];
                            });
                    });
                    return response()->json($paymentMethodsChart);

                case 'top-products':
                    $topProducts = Cache::remember('dashboard_top_products', now()->addMinutes(15), function () {
                        return DB::table('sale_items')
                            ->join('products', 'sale_items.product_id', '=', 'products.id')
                            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
                            ->where('sales.status', 'completed')
                            ->whereMonth('sales.created_at', Carbon::now()->month)
                            ->select('products.name', DB::raw('SUM(sale_items.quantity) as total_quantity'), DB::raw('SUM(sale_items.total_price) as total_amount'))
                            ->groupBy('products.id', 'products.name')
                            ->orderBy('total_quantity', 'desc')
                            ->take(5)
                            ->get();
                    });
                    return response()->json($topProducts);

                    // case 'category-sales':
                    //     $categorySalesChart = Cache::remember('dashboard_category_sales_chart', now()->addMinutes(15), function () {
                    //         return Product::join('sale_items', 'products.id', '=', 'sale_items.product_id')
                    //             ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
                    //             ->where('sales.status', 'completed')
                    //             ->whereMonth('sales.created_at', Carbon::now()->month)
                    //             ->select('products.category', DB::raw('SUM(sale_items.total_price) as total'))
                    //             ->groupBy('products.category')
                    //             ->get()
                    //             ->map(function ($item, $index) {
                    //                 return [
                    //                     'category' => $item->category,
                    //                     'total' => $item->total,
                    //                     'color' => $this->getCategoryColor($index)
                    //                 ];
                    //             });
                    //     });
                    //     return response()->json($categorySalesChart);

                default:
                    return response()->json(['success' => false, 'error' => __('dashboard.error_chart_type')], 400);
            }
        } catch (Exception $e) {
            Log::error('Erreur lors du chargement des données de graphique', [
                'type' => $type,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return response()->json([
                'success' => false,
                'error' => __('dashboard.error_chart_data'),
                'details' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}
