<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('store_stocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('store_id')->constrained()->onDelete('cascade');
            $table->integer('quantity')->default(0);
            $table->integer('min_stock_level')->default(0);
            $table->integer('max_stock_level')->nullable(); // Stock maximum recommandé
            $table->integer('reorder_point')->nullable(); // Point de réapprovisionnement
            $table->string('location')->nullable(); // Emplacement dans le magasin (rayon, étagère, etc.)
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_counted_at')->nullable(); // Dernier inventaire
            $table->timestamps();

            // Contrainte d'unicité : un produit ne peut avoir qu'un seul stock par magasin
            $table->unique(['product_id', 'store_id']);
            
            // Index pour optimiser les requêtes
            $table->index(['store_id', 'is_active']);
            $table->index(['product_id', 'is_active']);
            $table->index(['store_id', 'quantity']); // Pour les requêtes de stock faible
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('store_stocks');
    }
};
