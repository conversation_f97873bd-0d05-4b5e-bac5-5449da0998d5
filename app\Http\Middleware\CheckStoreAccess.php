<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckStoreAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Les super admins ont accès à tout
        if ($user->roles->pluck('name')->contains('superAdmin')) {
            return $next($request);
        }

        // Vérifier que l'utilisateur est assigné à un magasin
        if (!$user->store_id) {
            return redirect()->route('dashboard')
                ->with('error', 'Vous devez être assigné à un magasin pour accéder à cette fonctionnalité.');
        }

        return $next($request);
    }
}
