<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-edit mr-3 text-blue-600"></i>Modifier le Stock
                </h2>
                <p class="text-sm text-gray-600 mt-1">
                    {{ $storeStock->product->name }} dans {{ $storeStock->store->name }}
                </p>
            </div>
            <div class="flex space-x-2">
                <x-button href="{{ route('store-stocks.show', $storeStock) }}" variant="outline" icon="fas fa-eye">
                    Voir détails
                </x-button>
                <x-button href="{{ route('store-stocks.index', ['store_id' => $storeStock->store_id]) }}" 
                         variant="outline" icon="fas fa-arrow-left">
                    Retour aux stocks
                </x-button>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <form method="POST" action="{{ route('store-stocks.update', $storeStock) }}" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Informations actuelles -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>Informations actuelles
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-store text-blue-600 mr-2"></i>
                                <div>
                                    <div class="font-medium text-gray-900">{{ $storeStock->store->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $storeStock->store->code }}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-box text-purple-600 mr-2"></i>
                                <div>
                                    <div class="font-medium text-gray-900">{{ $storeStock->product->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $storeStock->product->barcode }}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-layer-group text-green-600 mr-2"></i>
                                <div>
                                    <div class="text-2xl font-bold text-gray-900">{{ $storeStock->quantity }}</div>
                                    <div class="text-sm text-gray-500">Stock actuel</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Seuils de stock -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-sliders-h mr-2 text-orange-600"></i>Seuils de stock
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <x-form-field>
                            <x-input-label for="min_stock_level" value="Seuil minimum *" />
                            <x-text-input id="min_stock_level" name="min_stock_level" type="number" 
                                         min="0" step="1" value="{{ old('min_stock_level', $storeStock->min_stock_level) }}" required />
                            <x-input-error :messages="$errors->get('min_stock_level')" />
                            <p class="text-xs text-gray-500 mt-1">Alerte de stock faible</p>
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="max_stock_level" value="Seuil maximum" />
                            <x-text-input id="max_stock_level" name="max_stock_level" type="number" 
                                         min="0" step="1" value="{{ old('max_stock_level', $storeStock->max_stock_level) }}" />
                            <x-input-error :messages="$errors->get('max_stock_level')" />
                            <p class="text-xs text-gray-500 mt-1">Alerte de surstock</p>
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="reorder_point" value="Point de commande" />
                            <x-text-input id="reorder_point" name="reorder_point" type="number" 
                                         min="0" step="1" value="{{ old('reorder_point', $storeStock->reorder_point) }}" />
                            <x-input-error :messages="$errors->get('reorder_point')" />
                            <p class="text-xs text-gray-500 mt-1">Seuil de réapprovisionnement</p>
                        </x-form-field>
                    </div>

                    <!-- Calculateur automatique -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h4 class="font-medium text-gray-900 mb-3">
                            <i class="fas fa-calculator mr-2 text-gray-600"></i>Calculateur automatique de seuils
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Ventes moyennes/jour</label>
                                <input type="number" id="avg_daily_sales" min="0" step="0.1" 
                                       class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Ex: 2.5">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Délai de livraison (jours)</label>
                                <input type="number" id="lead_time" min="1" step="1" value="7"
                                       class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div class="flex items-end">
                                <button type="button" onclick="calculateThresholds()" 
                                       class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                    <i class="fas fa-calculator mr-2"></i>Calculer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Emplacement et organisation -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-map-marker-alt mr-2 text-purple-600"></i>Emplacement et organisation
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field>
                            <x-input-label for="location" value="Emplacement dans le magasin" />
                            <x-text-input id="location" name="location" type="text" 
                                         value="{{ old('location', $storeStock->location) }}" 
                                         placeholder="Ex: Rayon A, Étagère 3, Niveau 2" />
                            <x-input-error :messages="$errors->get('location')" />
                            <p class="text-xs text-gray-500 mt-1">Aide à localiser le produit rapidement</p>
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="supplier_reference" value="Référence fournisseur" />
                            <x-text-input id="supplier_reference" name="supplier_reference" type="text" 
                                         value="{{ old('supplier_reference', $storeStock->supplier_reference) }}" 
                                         placeholder="Référence du fournisseur" />
                            <x-input-error :messages="$errors->get('supplier_reference')" />
                        </x-form-field>
                    </div>
                </div>

                <!-- Ajustement de stock -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-plus-minus mr-2 text-green-600"></i>Ajustement de stock (optionnel)
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <x-form-field>
                            <x-input-label for="adjustment_type" value="Type d'ajustement" />
                            <select id="adjustment_type" name="adjustment_type" 
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Aucun ajustement</option>
                                <option value="add" {{ old('adjustment_type') === 'add' ? 'selected' : '' }}>
                                    Ajouter au stock
                                </option>
                                <option value="remove" {{ old('adjustment_type') === 'remove' ? 'selected' : '' }}>
                                    Retirer du stock
                                </option>
                                <option value="set" {{ old('adjustment_type') === 'set' ? 'selected' : '' }}>
                                    Définir le stock
                                </option>
                            </select>
                            <x-input-error :messages="$errors->get('adjustment_type')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="adjustment_quantity" value="Quantité" />
                            <x-text-input id="adjustment_quantity" name="adjustment_quantity" type="number" 
                                         min="0" step="1" value="{{ old('adjustment_quantity') }}" />
                            <x-input-error :messages="$errors->get('adjustment_quantity')" />
                            <p class="text-xs text-gray-500 mt-1">Quantité à ajuster</p>
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="adjustment_reason" value="Motif" />
                            <select id="adjustment_reason" name="adjustment_reason" 
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Sélectionnez un motif</option>
                                <option value="restock" {{ old('adjustment_reason') === 'restock' ? 'selected' : '' }}>
                                    Réapprovisionnement
                                </option>
                                <option value="damage" {{ old('adjustment_reason') === 'damage' ? 'selected' : '' }}>
                                    Produit endommagé
                                </option>
                                <option value="theft" {{ old('adjustment_reason') === 'theft' ? 'selected' : '' }}>
                                    Vol
                                </option>
                                <option value="inventory" {{ old('adjustment_reason') === 'inventory' ? 'selected' : '' }}>
                                    Inventaire
                                </option>
                                <option value="other" {{ old('adjustment_reason') === 'other' ? 'selected' : '' }}>
                                    Autre
                                </option>
                            </select>
                            <x-input-error :messages="$errors->get('adjustment_reason')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="unit_cost" value="Coût unitaire" />
                            <x-text-input id="unit_cost" name="unit_cost" type="number" 
                                         min="0" step="0.01" value="{{ old('unit_cost') }}" 
                                         placeholder="Pour les entrées" />
                            <x-input-error :messages="$errors->get('unit_cost')" />
                            <p class="text-xs text-gray-500 mt-1">Si ajout de stock</p>
                        </x-form-field>

                        <x-form-field class="lg:col-span-4">
                            <x-input-label for="adjustment_notes" value="Notes sur l'ajustement" />
                            <textarea id="adjustment_notes" name="adjustment_notes" rows="2"
                                     class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                     placeholder="Notes sur cet ajustement...">{{ old('adjustment_notes') }}</textarea>
                            <x-input-error :messages="$errors->get('adjustment_notes')" />
                        </x-form-field>
                    </div>

                    <!-- Aperçu de l'ajustement -->
                    <div id="adjustmentPreview" class="mt-4 p-4 bg-blue-50 rounded-lg hidden">
                        <h4 class="font-medium text-blue-900 mb-2">Aperçu de l'ajustement</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-blue-700 font-medium">Stock actuel:</span>
                                <span class="text-blue-900 ml-2">{{ $storeStock->quantity }}</span>
                            </div>
                            <div>
                                <span class="text-blue-700 font-medium">Ajustement:</span>
                                <span id="adjustmentDisplay" class="text-blue-900 ml-2"></span>
                            </div>
                            <div>
                                <span class="text-blue-700 font-medium">Nouveau stock:</span>
                                <span id="newStockDisplay" class="text-blue-900 ml-2 font-bold"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Résumé des modifications -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-check-circle mr-2 text-green-600"></i>Résumé des modifications
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Seuil minimum:</span>
                            <span class="text-gray-900 ml-2">{{ $storeStock->min_stock_level }}</span>
                            <span id="newMinThreshold" class="text-blue-600 ml-2"></span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Seuil maximum:</span>
                            <span class="text-gray-900 ml-2">{{ $storeStock->max_stock_level ?: 'Non défini' }}</span>
                            <span id="newMaxThreshold" class="text-blue-600 ml-2"></span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Point de commande:</span>
                            <span class="text-gray-900 ml-2">{{ $storeStock->reorder_point ?: 'Non défini' }}</span>
                            <span id="newReorderPoint" class="text-blue-600 ml-2"></span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-4">
                    <x-button href="{{ route('store-stocks.show', $storeStock) }}" variant="outline">
                        Annuler
                    </x-button>
                    <x-button type="submit" variant="primary" icon="fas fa-save">
                        Enregistrer les modifications
                    </x-button>
                </div>
            </form>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const adjustmentType = document.getElementById('adjustment_type');
                const adjustmentQuantity = document.getElementById('adjustment_quantity');
                const adjustmentPreview = document.getElementById('adjustmentPreview');
                const currentStock = {{ $storeStock->quantity }};

                // Surveillance des champs de seuils
                ['min_stock_level', 'max_stock_level', 'reorder_point'].forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    const originalValue = field.value;
                    
                    field.addEventListener('input', function() {
                        const displayElement = document.getElementById('new' + fieldId.split('_').map(word => 
                            word.charAt(0).toUpperCase() + word.slice(1)).join('').replace('Level', 'Threshold').replace('Point', 'Point'));
                        
                        if (this.value !== originalValue) {
                            displayElement.textContent = `→ ${this.value || 'Non défini'}`;
                            displayElement.classList.remove('hidden');
                        } else {
                            displayElement.textContent = '';
                            displayElement.classList.add('hidden');
                        }
                    });
                });

                // Surveillance de l'ajustement de stock
                [adjustmentType, adjustmentQuantity].forEach(element => {
                    element.addEventListener('input', updateAdjustmentPreview);
                });

                function updateAdjustmentPreview() {
                    const type = adjustmentType.value;
                    const quantity = parseFloat(adjustmentQuantity.value) || 0;
                    
                    if (type && quantity > 0) {
                        let newStock = currentStock;
                        let adjustmentText = '';
                        
                        switch(type) {
                            case 'add':
                                newStock = currentStock + quantity;
                                adjustmentText = `+${quantity}`;
                                break;
                            case 'remove':
                                newStock = Math.max(0, currentStock - quantity);
                                adjustmentText = `-${quantity}`;
                                break;
                            case 'set':
                                newStock = quantity;
                                adjustmentText = `= ${quantity}`;
                                break;
                        }
                        
                        document.getElementById('adjustmentDisplay').textContent = adjustmentText;
                        document.getElementById('newStockDisplay').textContent = newStock;
                        adjustmentPreview.classList.remove('hidden');
                    } else {
                        adjustmentPreview.classList.add('hidden');
                    }
                }
            });

            function calculateThresholds() {
                const avgDailySales = parseFloat(document.getElementById('avg_daily_sales').value) || 0;
                const leadTime = parseFloat(document.getElementById('lead_time').value) || 7;
                
                if (avgDailySales > 0) {
                    // Calcul des seuils recommandés
                    const reorderPoint = Math.ceil(avgDailySales * leadTime * 1.2); // 20% de marge de sécurité
                    const minStock = Math.ceil(avgDailySales * leadTime * 0.5);
                    const maxStock = Math.ceil(avgDailySales * leadTime * 3);
                    
                    document.getElementById('reorder_point').value = reorderPoint;
                    document.getElementById('min_stock_level').value = minStock;
                    document.getElementById('max_stock_level').value = maxStock;
                    
                    // Déclencher les événements pour mettre à jour l'aperçu
                    ['min_stock_level', 'max_stock_level', 'reorder_point'].forEach(id => {
                        document.getElementById(id).dispatchEvent(new Event('input'));
                    });
                    
                    alert(`Seuils calculés:\n- Point de commande: ${reorderPoint}\n- Stock minimum: ${minStock}\n- Stock maximum: ${maxStock}`);
                } else {
                    alert('Veuillez saisir les ventes moyennes par jour');
                }
            }
        </script>
    @endpush
</x-app-layout>
