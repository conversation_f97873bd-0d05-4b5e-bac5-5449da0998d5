<?php

namespace App\Exports;

use App\Models\Sale;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class SalesExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $dateFrom;
    protected $dateTo;

    public function __construct($dateFrom, $dateTo)
    {
        $this->dateFrom = Carbon::parse($dateFrom)->startOfDay();
        $this->dateTo = Carbon::parse($dateTo)->endOfDay();
    }

    public function collection()
    {
        return Sale::with(['customer', 'user', 'saleItems.product'])
            ->whereBetween('created_at', [$this->dateFrom, $this->dateTo])
            ->completed()
            ->get();
    }

    public function headings(): array
    {
        return [
            'N° Vente',
            'Date',
            'Heure',
            'Client',
            'Caissier',
            'Sous-total (FCFA)',
            'TVA (FCFA)',
            'Remise (FCFA)',
            'Total (FCFA)',
            'Mode de paiement',
            'Montant reçu (FCFA)',
            'Monnaie (FCFA)',
            'Nb articles',
            'Statut',
            'Notes'
        ];
    }

    public function map($sale): array
    {
        return [
            $sale->sale_number,
            $sale->created_at->format('d/m/Y'),
            $sale->created_at->format('H:i:s'),
            $sale->customer ? $sale->customer->name : 'Client anonyme',
            $sale->user->name,
            $sale->subtotal,
            $sale->tax_amount,
            $sale->discount_amount,
            $sale->total_amount,
            $this->getPaymentMethodLabel($sale->payment_method),
            $sale->amount_paid,
            $sale->change_amount,
            $sale->saleItems->count(),
            ucfirst($sale->status),
            $sale->notes
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style pour l'en-tête
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4F46E5'],
                ],
            ],
        ];
    }

    private function getPaymentMethodLabel($method)
    {
        return match($method) {
            'cash' => 'Espèces',
            'mobile_money' => 'Mobile Money',
            'credit' => 'Crédit',
            default => ucfirst($method)
        };
    }
}
