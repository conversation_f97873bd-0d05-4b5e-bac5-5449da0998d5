<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use App\Models\Store;
use App\Models\StoreStock;
use App\Models\Sale;
use Carbon\Carbon;

class CacheService
{
    const CACHE_TTL = 3600; // 1 heure
    const CACHE_PREFIX = 'pos_multistore_';

    /**
     * Cache des statistiques de magasin
     */
    public function getStoreStats($storeId, $period = 'today')
    {
        $cacheKey = self::CACHE_PREFIX . "store_stats_{$storeId}_{$period}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($storeId, $period) {
            $store = Store::find($storeId);
            if (!$store) return null;

            $dateRange = $this->getDateRange($period);
            
            return [
                'sales_count' => $store->sales()
                    ->whereBetween('created_at', $dateRange)
                    ->count(),
                'sales_total' => $store->sales()
                    ->whereBetween('created_at', $dateRange)
                    ->sum('total_amount'),
                'profit_total' => $store->sales()
                    ->whereBetween('created_at', $dateRange)
                    ->with('saleItems')
                    ->get()
                    ->sum(function($sale) {
                        return $sale->saleItems->sum('profit_amount');
                    }),
                'low_stock_count' => $store->getLowStockProducts()->count(),
                'out_of_stock_count' => $store->getOutOfStockProducts()->count(),
                'active_customers' => $store->customers()->where('is_active', true)->count(),
            ];
        });
    }

    /**
     * Cache des produits les plus vendus par magasin
     */
    public function getTopSellingProducts($storeId, $limit = 10, $period = 'month')
    {
        $cacheKey = self::CACHE_PREFIX . "top_products_{$storeId}_{$period}_{$limit}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($storeId, $limit, $period) {
            $dateRange = $this->getDateRange($period);
            
            return \DB::table('sale_items')
                ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
                ->join('products', 'sale_items.product_id', '=', 'products.id')
                ->where('sales.store_id', $storeId)
                ->whereBetween('sales.created_at', $dateRange)
                ->select(
                    'products.name',
                    \DB::raw('SUM(sale_items.quantity) as total_quantity'),
                    \DB::raw('SUM(sale_items.total_price) as total_revenue'),
                    \DB::raw('SUM(sale_items.profit_amount) as total_profit')
                )
                ->groupBy('products.id', 'products.name')
                ->orderBy('total_quantity', 'desc')
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Cache des stocks critiques par magasin
     */
    public function getCriticalStocks($storeId)
    {
        $cacheKey = self::CACHE_PREFIX . "critical_stocks_{$storeId}";
        
        return Cache::remember($cacheKey, 1800, function () use ($storeId) { // 30 minutes
            return StoreStock::with(['product'])
                ->where('store_id', $storeId)
                ->where(function($query) {
                    $query->whereColumn('quantity', '<=', 'min_stock_level')
                          ->orWhere('quantity', '<=', 0);
                })
                ->orderBy('quantity', 'asc')
                ->get();
        });
    }

    /**
     * Cache des comparaisons inter-magasins
     */
    public function getStoresComparison($period = 'month')
    {
        $cacheKey = self::CACHE_PREFIX . "stores_comparison_{$period}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($period) {
            $dateRange = $this->getDateRange($period);
            
            return Store::with(['sales' => function($query) use ($dateRange) {
                    $query->whereBetween('created_at', $dateRange);
                }])
                ->get()
                ->map(function($store) {
                    $sales = $store->sales;
                    return [
                        'store_id' => $store->id,
                        'store_name' => $store->name,
                        'sales_count' => $sales->count(),
                        'total_revenue' => $sales->sum('total_amount'),
                        'average_sale' => $sales->count() > 0 ? $sales->avg('total_amount') : 0,
                        'total_profit' => $sales->sum(function($sale) {
                            return $sale->saleItems->sum('profit_amount');
                        }),
                    ];
                });
        });
    }

    /**
     * Invalider le cache d'un magasin
     */
    public function invalidateStoreCache($storeId)
    {
        $patterns = [
            self::CACHE_PREFIX . "store_stats_{$storeId}_*",
            self::CACHE_PREFIX . "top_products_{$storeId}_*",
            self::CACHE_PREFIX . "critical_stocks_{$storeId}",
            self::CACHE_PREFIX . "stores_comparison_*"
        ];

        foreach ($patterns as $pattern) {
            $keys = Cache::getRedis()->keys($pattern);
            if (!empty($keys)) {
                Cache::getRedis()->del($keys);
            }
        }
    }

    /**
     * Invalider tout le cache multi-store
     */
    public function invalidateAllCache()
    {
        $keys = Cache::getRedis()->keys(self::CACHE_PREFIX . '*');
        if (!empty($keys)) {
            Cache::getRedis()->del($keys);
        }
    }

    /**
     * Obtenir la plage de dates selon la période
     */
    private function getDateRange($period)
    {
        switch ($period) {
            case 'today':
                return [Carbon::today(), Carbon::tomorrow()];
            case 'week':
                return [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()];
            case 'month':
                return [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
            case 'year':
                return [Carbon::now()->startOfYear(), Carbon::now()->endOfYear()];
            default:
                return [Carbon::today(), Carbon::tomorrow()];
        }
    }

    /**
     * Précharger les données critiques
     */
    public function warmUpCache()
    {
        $stores = Store::active()->get();
        
        foreach ($stores as $store) {
            // Précharger les stats principales
            $this->getStoreStats($store->id, 'today');
            $this->getStoreStats($store->id, 'month');
            
            // Précharger les produits top
            $this->getTopSellingProducts($store->id);
            
            // Précharger les stocks critiques
            $this->getCriticalStocks($store->id);
        }
        
        // Précharger la comparaison
        $this->getStoresComparison();
    }
}
