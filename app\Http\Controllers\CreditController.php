<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Credit;
use App\Models\CreditPayment;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class CreditController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $query = Credit::with(['customer', 'sale']);

        // Filtrage par rôle : managers et caissiers ne voient que les crédits de leurs propres ventes
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if (in_array('manager', $userRoles) || in_array('caissier', $userRoles)) {
            $query->whereHas('sale', function($saleQuery) use ($user) {
                $saleQuery->where('user_id', $user->id);
            });
        }
        // SuperAdmin et Admin voient tous les crédits (pas de filtre supplémentaire)

        $credits = $query->latest()->paginate(20);

        // Statistiques filtrées selon le rôle
        $statsQuery = Credit::query();
        if (in_array('manager', $userRoles) || in_array('caissier', $userRoles)) {
            $statsQuery->whereHas('sale', function($saleQuery) use ($user) {
                $saleQuery->where('user_id', $user->id);
            });
        }

        // Calculer les statistiques
        $totalCredits = $statsQuery->where('status', 'active')->sum('remaining_balance');
        $overdueCredits = $statsQuery->where('status', 'active')
            ->where('due_date', '<', now())
            ->count();
        $paidCredits = $statsQuery->where('status', 'paid')->count();

        return view('credits.index', compact('credits', 'totalCredits', 'overdueCredits', 'paidCredits'));

        $totalCredits = $statsQuery->where('status', 'active')->sum('remaining_balance');
        $overdueCredits = $statsQuery->where('status', 'overdue')->count();
        $paidCredits = $statsQuery->where('status', 'paid')->count();

        return view('credits.index', compact('credits', 'totalCredits', 'overdueCredits', 'paidCredits'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Credit $credit)
    {
        // Vérification des permissions : managers et caissiers ne peuvent voir que les crédits de leurs propres ventes
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if ((in_array('manager', $userRoles) || in_array('caissier', $userRoles)) && $credit->sale->user_id !== $user->id) {
            abort(403, 'Vous ne pouvez consulter que les crédits de vos propres ventes.');
        }

        $credit->load(['customer', 'sale.saleItems', 'payments.user']);

        return view('credits.show', compact('credit'));
    }

    /**
     * Add payment to credit
     */
    public function addPayment(Request $request, Credit $credit)
    {
        // Vérification des permissions : managers et caissiers ne peuvent ajouter des paiements que sur leurs propres crédits
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if ((in_array('manager', $userRoles) || in_array('caissier', $userRoles)) && $credit->sale->user_id !== $user->id) {
            abort(403, 'Vous ne pouvez ajouter des paiements que sur les crédits de vos propres ventes.');
        }

        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $credit->remaining_balance,
            'payment_method' => 'required|in:cash,mobile_money,bank_transfer',
            'reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Créer le paiement
            $payment = CreditPayment::create([
                'credit_id' => $credit->id,
                'user_id' => Auth::id(),
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'reference' => $request->reference,
                'notes' => $request->notes,
            ]);

            // Mettre à jour le crédit
            $credit->amount_paid += $request->amount;
            $credit->remaining_balance -= $request->amount;

            // Marquer comme payé si le solde est nul
            if ($credit->remaining_balance <= 0) {
                $credit->status = 'paid';
                $credit->remaining_balance = 0; // S'assurer qu'il n'y a pas de valeur négative
            }

            $credit->save();

            // Mettre à jour le solde du client
            $credit->customer->decrement('current_balance', $request->amount);

            DB::commit();

            return back()->with('success', 'Paiement enregistré avec succès.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de l\'enregistrement du paiement: ' . $e->getMessage());
        }
    }

    /**
     * Show payments for a credit
     */
    public function payments(Credit $credit)
    {
        // Vérification des permissions : managers et caissiers ne peuvent voir que les paiements de leurs propres crédits
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if ((in_array('manager', $userRoles) || in_array('caissier', $userRoles)) && $credit->sale->user_id !== $user->id) {
            abort(403, 'Vous ne pouvez consulter que les paiements de vos propres crédits.');
        }

        $payments = $credit->payments()
            ->with('user')
            ->latest()
            ->paginate(10);

        return view('credits.payments', compact('credit', 'payments'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Credit $credit)
    {
        // Vérification des permissions : managers et caissiers ne peuvent modifier que leurs propres crédits
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if ((in_array('manager', $userRoles) || in_array('caissier', $userRoles)) && $credit->sale->user_id !== $user->id) {
            abort(403, 'Vous ne pouvez modifier que les crédits de vos propres ventes.');
        }

        $request->validate([
            'due_date' => 'nullable|date|after:today',
            'status' => 'required|in:active,paid,overdue,cancelled',
            'notes' => 'nullable|string',
        ]);

        $credit->update($request->only(['due_date', 'status', 'notes']));

        return back()->with('success', 'Crédit mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Credit $credit)
    {
        // Vérification des permissions : managers et caissiers ne peuvent supprimer que leurs propres crédits
        $user = Auth::user();
        $userRoles = $user->roles->pluck('name')->toArray();

        if ((in_array('manager', $userRoles) || in_array('caissier', $userRoles)) && $credit->sale->user_id !== $user->id) {
            abort(403, 'Vous ne pouvez supprimer que les crédits de vos propres ventes.');
        }

        if ($credit->payments()->exists()) {
            return back()->with('error', 'Impossible de supprimer un crédit avec des paiements.');
        }

        try {
            DB::beginTransaction();

            // Remettre le montant au solde du client
            $credit->customer->decrement('current_balance', $credit->remaining_balance);

            $credit->delete();

            DB::commit();

            return redirect()->route('credits.index')
                ->with('success', 'Crédit supprimé avec succès.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de la suppression: ' . $e->getMessage());
        }
    }
}
