<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Credit;
use App\Models\StockMovement;

use App\Models\ProductPrice;
use App\Models\StoreStock;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class POSController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $userStore = $user->store;

        if (!$userStore) {
            return redirect()->back()->with('error', 'Vous devez être assigné à un magasin pour accéder au POS.');
        }

        // Charger les produits avec leurs stocks et prix pour ce magasin
        $products = Product::active()
            ->with(['storeStocks' => function($query) use ($userStore) {
                $query->where('store_id', $userStore->id)->active();
            }, 'productPrices' => function($query) use ($userStore) {
                $query->where('store_id', $userStore->id)->active()->current();
            }])
            ->get()
            ->filter(function($product) {
                // Ne garder que les produits qui ont un stock et un prix dans ce magasin
                return $product->storeStocks->isNotEmpty() && $product->productPrices->isNotEmpty();
            });

        // Charger les clients accessibles pour ce magasin
        $clients = $user->roles->pluck('name')->intersect(['superAdmin', 'admin'])->isNotEmpty()
            ? Customer::where('store_id', $userStore->id)->active()->get(['id', 'name', 'current_balance', 'phone'])
            : Customer::where('created_by', $user->id)->where('store_id', $userStore->id)->active()->get(['id', 'name', 'current_balance', 'phone']);

        // Calculer les statistiques du magasin pour l'en-tête
        $storeStats = [
            'products_in_stock' => StoreStock::where('store_id', $userStore->id)
                ->where('quantity', '>', 0)
                ->count(),
            'low_stock_count' => StoreStock::where('store_id', $userStore->id)
                ->whereColumn('quantity', '<=', 'low_stock_threshold')
                ->where('quantity', '>', 0)
                ->count(),
            'out_of_stock_count' => StoreStock::where('store_id', $userStore->id)
                ->where('quantity', '<=', 0)
                ->count(),
        ];

        return view('pos.index', compact('products', 'clients', 'userStore', 'storeStats'));
    }

    // Recherche dynamique de clients pour l'autocomplete
    public function searchCustomers(Request $request)
    {
        $query = $request->get('query');
        $user = Auth::user();
        $userStore = $user->store;

        if (!$userStore) {
            return response()->json([]);
        }

        $customersQuery = $user->roles->pluck('name')->intersect(['superAdmin', 'admin'])->isNotEmpty()
            ? Customer::where('store_id', $userStore->id)->active()
            : Customer::where('created_by', $user->id)->where('store_id', $userStore->id)->active();

        // Recherche multi-mots : chaque mot doit être présent dans le nom ou le téléphone
        $words = preg_split('/\s+/', trim($query));
        $customersQuery = $customersQuery->where(function($q) use ($words) {
            foreach ($words as $word) {
                $q->where(function($subQ) use ($word) {
                    $subQ->where('name', 'LIKE', "%{$word}%")
                         ->orWhere('phone', 'LIKE', "%{$word}%");
                });
            }
        });

        $customers = $customersQuery
            ->orderByDesc('id')
            ->limit(10)
            ->get(['id', 'name', 'current_balance']);

        // On force le formatage pour éviter tout souci de typage côté JS
        $result = $customers->map(function($c) {
            return [
                'id' => $c->id,
                'name' => $c->name,
                'current_balance' => (float) $c->current_balance,
            ];
        });

        return response()->json($result);
    }

    public function processSale(Request $request)
    {
        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'customer_id' => 'nullable|exists:customers,id',
            'payment_method' => 'required|in:cash,credit',
            'amount_paid' => 'required_unless:payment_method,credit|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'due_date' => 'required_if:payment_method,credit|nullable|date|after:today',
        ]);

        try {
            DB::beginTransaction();

            $user = Auth::user();
            $userStore = $user->store;

            if (!$userStore) {
                throw new \Exception("Vous devez être assigné à un magasin pour effectuer une vente.");
            }

            // Vérifier le stock pour tous les produits dans ce magasin
            foreach ($request->items as $item) {
                $storeStock = StoreStock::where('product_id', $item['product_id'])
                    ->where('store_id', $userStore->id)
                    ->first();

                if (!$storeStock || !$storeStock->hasStock($item['quantity'])) {
                    $product = Product::find($item['product_id']);
                    throw new \Exception("Stock insuffisant pour le produit: {$product->name} dans ce magasin");
                }
            }

            // Vérifier que l'utilisateur a accès au client sélectionné et qu'il appartient au même magasin
            if ($request->customer_id) {
                $isAdmin = $user->roles->pluck('name')->intersect(['superAdmin', 'admin'])->isNotEmpty();
                $customer = Customer::find($request->customer_id);

                if (!$customer || $customer->store_id !== $userStore->id) {
                    throw new \Exception("Ce client n'appartient pas à votre magasin");
                }

                if (!$isAdmin && $customer->created_by !== Auth::id()) {
                    throw new \Exception("Vous n'avez pas accès à ce client");
                }
            }

            // Calculer les totaux
            $subtotal = collect($request->items)->sum(function ($item) {
                return $item['quantity'] * $item['unit_price'];
            });

            $discountAmount = $request->discount_amount ?? 0;
            $totalAmount = $subtotal - $discountAmount;

            // Vérifier le crédit client
            if ($request->payment_method === 'credit') {
                if (!$request->customer_id) {
                    throw new \Exception("Un client doit être sélectionné pour une vente à crédit");
                }

                $customer = Customer::find($request->customer_id);
                if (!$customer->is_active) {
                    throw new \Exception("Ce client n'est pas actif");
                }
            }

            // Créer la vente
            $sale = Sale::create([
                'user_id' => Auth::id(),
                'store_id' => $userStore->id,
                'customer_id' => $request->customer_id,
                'subtotal' => $subtotal,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'amount_paid' => $request->payment_method === 'credit' ? 0 : $request->amount_paid,
                'change_amount' => $request->payment_method === 'credit' ? 0 : max(0, $request->amount_paid - $totalAmount),
                'status' => 'completed',
                'notes' => $request->notes,
            ]);

            // Créer les articles de vente et mettre à jour le stock
            foreach ($request->items as $item) {
                $product = Product::find($item['product_id']);

                // Récupérer le prix actuel pour ce produit dans ce magasin
                $productPrice = ProductPrice::getCurrentPrice($product->id, $userStore->id);
                $costPrice = $productPrice ? $productPrice->cost_price : null;

                // Créer l'article de vente
                $saleItem = SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'unit_price' => $item['unit_price'],
                    'cost_price' => $costPrice,
                    'quantity' => $item['quantity'],
                    'total_price' => $item['quantity'] * $item['unit_price'],
                ]);

                // Calculer et sauvegarder le bénéfice
                $saleItem->calculateProfit();
                $saleItem->save();

                // Mettre à jour le stock du magasin
                $storeStock = StoreStock::where('product_id', $product->id)
                    ->where('store_id', $userStore->id)
                    ->first();

                $storeStock->removeStock($item['quantity'], 'Vente #' . $sale->sale_number);

                // Le mouvement de stock est automatiquement créé par la méthode removeStock()
                // mais nous devons nous assurer qu'il inclut la référence de vente
                $lastMovement = StockMovement::where('product_id', $product->id)
                    ->where('store_id', $userStore->id)
                    ->latest()
                    ->first();

                if ($lastMovement) {
                    $lastMovement->update(['reference' => $sale->sale_number]);
                }
            }

            // Créer le crédit si nécessaire
            if ($request->payment_method === 'credit') {
                $customer = Customer::find($request->customer_id);

                Credit::create([
                    'sale_id' => $sale->id,
                    'customer_id' => $customer->id,
                    'total_amount' => $totalAmount,
                    'amount_paid' => 0,
                    'remaining_balance' => $totalAmount,
                    'due_date' => $request->due_date,
                    'status' => 'active',
                ]);

                // Mettre à jour le solde du client
                $customer->increment('current_balance', $totalAmount);
            }

            DB::commit();

            // Préparer les données de réponse
            $responseData = [
                'success' => true,
                'message' => 'Vente enregistrée avec succès',
                'sale_id' => $sale->id,
                'sale_number' => $sale->sale_number,
                'total_amount' => $totalAmount,
                'change_amount' => $sale->change_amount,
            ];

            // Ajouter les informations de crédit si applicable
            if ($request->payment_method === 'credit') {
                $responseData['credit_info'] = [
                    'due_date' => $request->due_date,
                    'amount' => $totalAmount
                ];
            }

            return response()->json($responseData);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    public function receipt(Sale $sale)
    {
        $sale->load(['saleItems.product', 'customer', 'user', 'credit']);
        return view('pos.receipt', compact('sale'));
    }
}
