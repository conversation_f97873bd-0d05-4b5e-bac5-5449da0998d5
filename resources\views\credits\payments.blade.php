<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-history mr-2"></i>Historique des Paiements - Crédit #{{ $credit->id }}
            </h2>
            <div class="flex space-x-2">
                @if($credit->status === 'active')
                <button onclick="openPaymentModal()" 
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>Nouveau Paiement
                </button>
                @endif
                <a href="{{ route('credits.show', $credit) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-eye mr-2"></i>Voir Crédit
                </a>
                <a href="{{ route('credits.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Résumé du crédit -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900">{{ $credit->sale->sale_number }}</div>
                            <div class="text-sm text-gray-500">N° Vente</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">{{ $credit->customer->name }}</div>
                            <div class="text-sm text-gray-500">Client</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900">
                                {{ number_format($credit->total_amount, 0, ',', ' ') }} FCFA
                            </div>
                            <div class="text-sm text-gray-500">Montant total</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">
                                {{ number_format($credit->amount_paid, 0, ',', ' ') }} FCFA
                            </div>
                            <div class="text-sm text-gray-500">Montant payé</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-red-600">
                                {{ number_format($credit->remaining_balance, 0, ',', ' ') }} FCFA
                            </div>
                            <div class="text-sm text-gray-500">Reste à payer</div>
                        </div>
                    </div>

                    <!-- Barre de progression -->
                    @php
                        $percentage = $credit->total_amount > 0 ? ($credit->amount_paid / $credit->total_amount) * 100 : 0;
                    @endphp
                    
                    <div class="mt-6">
                        <div class="flex justify-between text-sm text-gray-600 mb-2">
                            <span>Progression du paiement</span>
                            <span>{{ number_format($percentage, 1) }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-green-600 h-3 rounded-full transition-all duration-300" style="width: {{ $percentage }}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistiques des paiements -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-list text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total paiements</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $credit->payments->count() }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-calculator text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Paiement moyen</p>
                                <p class="text-2xl font-semibold text-gray-900">
                                    @if($credit->payments->count() > 0)
                                        {{ number_format($credit->payments->avg('amount'), 0, ',', ' ') }} FCFA
                                    @else
                                        0 FCFA
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-arrow-up text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Plus gros paiement</p>
                                <p class="text-2xl font-semibold text-gray-900">
                                    @if($credit->payments->count() > 0)
                                        {{ number_format($credit->payments->max('amount'), 0, ',', ' ') }} FCFA
                                    @else
                                        0 FCFA
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-calendar text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Dernier paiement</p>
                                <p class="text-lg font-semibold text-gray-900">
                                    @if($credit->payments->count() > 0)
                                        {{ $credit->payments->sortByDesc('created_at')->first()->created_at->format('d/m/Y') }}
                                    @else
                                        Aucun
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des paiements -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">
                        <i class="fas fa-list mr-2"></i>Historique détaillé des paiements
                    </h3>
                    
                    @if($credit->payments->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        #
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date & Heure
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Montant
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Mode de paiement
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Solde après paiement
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Notes
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Utilisateur
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($credit->payments->sortByDesc('created_at') as $index => $payment)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $credit->payments->count() - $index }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $payment->created_at->format('d/m/Y') }}</div>
                                        <div class="text-xs text-gray-500">{{ $payment->created_at->format('H:i:s') }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-green-600">
                                            {{ number_format($payment->amount, 0, ',', ' ') }} FCFA
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $payment->payment_method === 'cash' ? 'bg-green-100 text-green-800' : '' }}
                                            {{ $payment->payment_method === 'mobile_money' ? 'bg-blue-100 text-blue-800' : '' }}
                                            {{ $payment->payment_method === 'bank_transfer' ? 'bg-purple-100 text-purple-800' : '' }}">
                                            @switch($payment->payment_method)
                                                @case('cash')
                                                    <i class="fas fa-money-bill mr-1"></i>Espèces
                                                    @break
                                                @case('mobile_money')
                                                    <i class="fas fa-mobile-alt mr-1"></i>Mobile Money
                                                    @break
                                                @case('bank_transfer')
                                                    <i class="fas fa-university mr-1"></i>Virement
                                                    @break
                                                @default
                                                    {{ ucfirst($payment->payment_method) }}
                                            @endswitch
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @php
                                            // Calculer le solde après ce paiement
                                            $paymentsAfter = $credit->payments->where('created_at', '<=', $payment->created_at);
                                            $balanceAfter = $credit->total_amount - $paymentsAfter->sum('amount');
                                        @endphp
                                        {{ number_format($balanceAfter, 0, ',', ' ') }} FCFA
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500 max-w-xs">
                                        <div class="truncate" title="{{ $payment->notes }}">
                                            {{ $payment->notes ?? '-' }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $payment->user->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $payment->user->role ?? 'Utilisateur' }}</div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Résumé par mode de paiement -->
                    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                        @php
                            $paymentsByMethod = $credit->payments->groupBy('payment_method');
                        @endphp
                        
                        @foreach($paymentsByMethod as $method => $payments)
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">
                                    @switch($method)
                                        @case('cash') Espèces @break
                                        @case('mobile_money') Mobile Money @break
                                        @case('bank_transfer') Virement bancaire @break
                                        @default {{ ucfirst($method) }}
                                    @endswitch
                                </span>
                                <span class="text-xs text-gray-500">{{ $payments->count() }} paiement(s)</span>
                            </div>
                            <div class="text-lg font-semibold text-gray-900">
                                {{ number_format($payments->sum('amount'), 0, ',', ' ') }} FCFA
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @else
                    <div class="text-center py-12">
                        <i class="fas fa-receipt text-gray-300 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun paiement enregistré</h3>
                        <p class="text-gray-500 mb-6">Ce crédit n'a encore reçu aucun paiement.</p>
                        @if($credit->status === 'active')
                        <button onclick="openPaymentModal()" 
                                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                            <i class="fas fa-plus mr-2"></i>Ajouter le premier paiement
                        </button>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de paiement -->
    @if($credit->status === 'active')
    <div id="payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Ajouter un paiement</h3>
                <form action="{{ route('credits.payment', $credit) }}" method="POST">
                    @csrf
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Montant restant</label>
                        <p class="text-lg font-semibold text-red-600">{{ number_format($credit->remaining_balance, 0, ',', ' ') }} FCFA</p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Montant du paiement</label>
                        <input type="number" name="amount" required min="1" max="{{ $credit->remaining_balance }}" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mode de paiement</label>
                        <select name="payment_method" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                            <option value="cash">Espèces</option>
                            <option value="mobile_money">Mobile Money</option>
                            <option value="bank_transfer">Virement bancaire</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notes (optionnel)</label>
                        <textarea name="notes" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                                  placeholder="Notes sur le paiement..."></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closePaymentModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                            Annuler
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                            <i class="fas fa-save mr-2"></i>Enregistrer le paiement
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endif

    @push('scripts')
    <script>
        function openPaymentModal() {
            document.getElementById('payment-modal').classList.remove('hidden');
        }

        function closePaymentModal() {
            document.getElementById('payment-modal').classList.add('hidden');
        }

        // Fermer le modal en cliquant à l'extérieur
        document.getElementById('payment-modal')?.addEventListener('click', function(e) {
            if (e.target === this) {
                closePaymentModal();
            }
        });

        // Fermer le modal avec la touche Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePaymentModal();
            }
        });
    </script>
    @endpush
</x-app-layout>
