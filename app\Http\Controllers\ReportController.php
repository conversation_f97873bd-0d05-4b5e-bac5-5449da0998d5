<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Credit;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\SalesExport;
use App\Exports\CreditsExport;
use App\Exports\StockExport;

class ReportController extends Controller
{
    public function index()
    {
        return view('reports.index');
    }

    public function salesReport(Request $request)
    {
        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'format' => 'required|in:pdf,excel,view',
        ]);

        $dateFrom = Carbon::parse($request->date_from)->startOfDay();
        $dateTo = Carbon::parse($request->date_to)->endOfDay();

        $sales = Sale::with(['customer', 'user', 'saleItems.product'])
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->completed()
            ->get();

        // Statistiques
        $totalSales = $sales->sum('total_amount');
        $totalTax = $sales->sum('tax_amount');
        $totalDiscount = $sales->sum('discount_amount');
        $salesByPaymentMethod = $sales->groupBy('payment_method')->map(function ($group) {
            return [
                'count' => $group->count(),
                'total' => $group->sum('total_amount')
            ];
        });

        $data = compact('sales', 'totalSales', 'totalTax', 'totalDiscount', 'salesByPaymentMethod', 'dateFrom', 'dateTo');

        switch ($request->format) {
            case 'pdf':
                $pdf = Pdf::loadView('reports.sales-pdf', $data);
                return $pdf->download('rapport-ventes-' . $dateFrom->format('Y-m-d') . '-' . $dateTo->format('Y-m-d') . '.pdf');

            case 'excel':
                return Excel::download(
                    new SalesExport($dateFrom, $dateTo),
                    'rapport-ventes-' . $dateFrom->format('Y-m-d') . '-' . $dateTo->format('Y-m-d') . '.xlsx'
                );

            default:
                return view('reports.sales', $data);
        }
    }

    public function creditsReport(Request $request)
    {
        $request->validate([
            'status' => 'nullable|in:active,paid,overdue,cancelled',
            'format' => 'nullable|in:pdf,excel,view',
        ]);

        // Valeurs par défaut
        $status = $request->get('status');
        $format = $request->get('format', 'view');

        $query = Credit::with(['customer', 'sale']);

        if ($status) {
            if ($status === 'overdue') {
                $query->overdue();
            } else {
                $query->where('status', $status);
            }
        }

        $credits = $query->get();

        // Statistiques
        $totalCredits = $credits->sum('total_amount');
        $totalPaid = $credits->sum('amount_paid');
        $totalRemaining = $credits->sum('remaining_balance');
        $creditsByStatus = $credits->groupBy('status')->map(function ($group) {
            return [
                'count' => $group->count(),
                'total' => $group->sum('remaining_balance')
            ];
        });

        $data = compact('credits', 'totalCredits', 'totalPaid', 'totalRemaining', 'creditsByStatus');

        switch ($format) {
            case 'pdf':
                $pdf = Pdf::loadView('reports.credits-pdf', $data);
                return $pdf->download('rapport-credits-' . now()->format('Y-m-d') . '.pdf');

            case 'excel':
                return Excel::download(
                    new CreditsExport($status),
                    'rapport-credits-' . now()->format('Y-m-d') . '.xlsx'
                );

            default:
                return view('reports.credits', $data);
        }
    }

    public function stockReport(Request $request)
    {
        $request->validate([
            'filter' => 'nullable|in:low_stock,out_of_stock,all',
            'format' => 'nullable|in:pdf,excel,view',
        ]);

        // Valeurs par défaut
        $filter = $request->get('filter', 'all');
        $format = $request->get('format', 'view');

        $query = Product::with(['stockMovements' => function($q) {
            $q->latest()->take(5);
        }]);

        switch ($filter) {
            case 'low_stock':
                $query->lowStock();
                break;
            case 'out_of_stock':
                $query->where('stock_quantity', 0);
                break;
        }

        $products = $query->get();

        // Statistiques
        $totalProducts = $products->count();
        $totalValue = $products->sum(function($product) {
            return $product->stock_quantity * $product->cost_price;
        });
        $lowStockCount = $products->filter(fn($p) => $p->isLowStock())->count();
        $outOfStockCount = $products->where('stock_quantity', 0)->count();

        $data = compact('products', 'totalProducts', 'totalValue', 'lowStockCount', 'outOfStockCount');

        switch ($format) {
            case 'pdf':
                $pdf = Pdf::loadView('reports.stock-pdf', $data);
                return $pdf->download('rapport-stock-' . now()->format('Y-m-d') . '.pdf');

            case 'excel':
                return Excel::download(
                    new StockExport($filter),
                    'rapport-stock-' . now()->format('Y-m-d') . '.xlsx'
                );

            default:
                return view('reports.stock', $data);
        }
    }
}
