<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Créer un magasin par défaut
        $defaultStoreId = DB::table('stores')->insertGetId([
            'name' => 'Magasin Principal',
            'code' => 'MAIN',
            'description' => 'Magasin principal créé lors de la migration',
            'address' => 'Adresse à définir',
            'city' => 'Ville à définir',
            'phone' => 'Téléphone à définir',
            'email' => '<EMAIL>',
            'manager_name' => 'Manager à définir',
            'default_tax_rate' => 0,
            'currency' => 'XOF',
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Migrer les stocks existants vers store_stocks
        $products = DB::table('products')->get();
        foreach ($products as $product) {
            DB::table('store_stocks')->insert([
                'product_id' => $product->id,
                'store_id' => $defaultStoreId,
                'quantity' => $product->stock_quantity ?? 0,
                'min_stock_level' => $product->min_stock_level ?? 0,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Créer des prix par défaut pour tous les produits (prix de vente à 0 - à définir manuellement)
        foreach ($products as $product) {
            DB::table('product_prices')->insert([
                'product_id' => $product->id,
                'store_id' => $defaultStoreId,
                'cost_price' => null, // À définir manuellement
                'selling_price' => 0, // À définir manuellement
                'min_selling_price' => null,
                'wholesale_price' => null,
                'tax_rate' => 0,
                'is_active' => true,
                'effective_from' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Associer tous les utilisateurs existants au magasin par défaut
        DB::table('users')->update(['store_id' => $defaultStoreId]);

        // Associer toutes les ventes existantes au magasin par défaut
        DB::table('sales')->update(['store_id' => $defaultStoreId]);

        // Associer tous les mouvements de stock existants au magasin par défaut
        DB::table('stock_movements')->update(['store_id' => $defaultStoreId]);

        // Associer tous les clients existants au magasin par défaut
        DB::table('customers')->update(['store_id' => $defaultStoreId]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restaurer les données dans la table products
        $storeStocks = DB::table('store_stocks')
            ->join('stores', 'store_stocks.store_id', '=', 'stores.id')
            ->where('stores.code', 'MAIN')
            ->get();

        foreach ($storeStocks as $stock) {
            DB::table('products')
                ->where('id', $stock->product_id)
                ->update([
                    'stock_quantity' => $stock->quantity,
                    'min_stock_level' => $stock->min_stock_level,
                ]);
        }

        // Supprimer les données créées
        DB::table('product_prices')->where('store_id', function($query) {
            $query->select('id')->from('stores')->where('code', 'MAIN');
        })->delete();

        DB::table('store_stocks')->where('store_id', function($query) {
            $query->select('id')->from('stores')->where('code', 'MAIN');
        })->delete();

        // Remettre les store_id à null
        DB::table('users')->update(['store_id' => null]);
        DB::table('sales')->update(['store_id' => null]);
        DB::table('stock_movements')->update(['store_id' => null]);
        DB::table('customers')->update(['store_id' => null]);

        // Supprimer le magasin par défaut
        DB::table('stores')->where('code', 'MAIN')->delete();
    }
};
