<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class FixCustomersTableCreditLimit extends Migration
{
    public function up()
    {
        Schema::table('customers', function (Blueprint $table) {
            // D'abord vérifier si la colonne existe avant de la supprimer
            if (Schema::hasColumn('customers', 'credit_limit')) {
                $table->dropColumn('credit_limit');
            }
        });
    }

    public function down()
    {
        Schema::table('customers', function (Blueprint $table) {
            // Ajouter la colonne back si elle n'existe pas
            if (!Schema::hasColumn('customers', 'credit_limit')) {
                $table->decimal('credit_limit', 10, 2)->default(0);
            }
        });
    }
}
