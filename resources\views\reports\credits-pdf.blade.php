<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport des Crédits</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        .stats-grid {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .stats-row {
            display: table-row;
        }
        .stats-cell {
            display: table-cell;
            width: 25%;
            padding: 15px;
            text-align: center;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .stats-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .stats-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin: 30px 0 15px 0;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 11px;
            text-transform: uppercase;
            color: #333;
        }
        td {
            font-size: 11px;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        .badge-active {
            background-color: #fff3cd;
            color: #856404;
        }
        .badge-overdue {
            background-color: #f8d7da;
            color: #721c24;
        }
        .badge-paid {
            background-color: #d4edda;
            color: #155724;
        }
        .badge-cancelled {
            background-color: #e2e3e5;
            color: #383d41;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #888;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Système POS') }}</div>
        <div class="report-title">Rapport des Crédits</div>
        <div class="period">
            Généré le {{ now()->format('d/m/Y à H:i') }}
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="stats-grid">
        <div class="stats-row">
            <div class="stats-cell">
                <div class="stats-value">{{ number_format($totalCredits, 0, ',', ' ') }} FCFA</div>
                <div class="stats-label">Total crédits</div>
            </div>
            <div class="stats-cell">
                <div class="stats-value">{{ number_format($totalPaid, 0, ',', ' ') }} FCFA</div>
                <div class="stats-label">Total payé</div>
            </div>
            <div class="stats-cell">
                <div class="stats-value">{{ number_format($totalRemaining, 0, ',', ' ') }} FCFA</div>
                <div class="stats-label">Reste à payer</div>
            </div>
            <div class="stats-cell">
                <div class="stats-value">{{ $credits->count() }}</div>
                <div class="stats-label">Nombre de crédits</div>
            </div>
        </div>
    </div>

    <!-- Répartition par statut -->
    <div class="section-title">Répartition par statut</div>
    <table style="width: 60%; margin: 0 auto 30px auto;">
        @foreach($creditsByStatus as $status => $data)
        <tr>
            <td>
                @switch($status)
                    @case('active') Crédits actifs @break
                    @case('paid') Crédits payés @break
                    @case('cancelled') Crédits annulés @break
                    @default {{ ucfirst($status) }}
                @endswitch
            </td>
            <td class="text-right">{{ $data['count'] }} crédit(s)</td>
            <td class="text-right">{{ number_format($data['total'], 0, ',', ' ') }} FCFA</td>
        </tr>
        @endforeach
    </table>

    <!-- Détail des crédits -->
    <div class="section-title">Détail des crédits</div>
    @if($credits->count() > 0)
    <table>
        <thead>
            <tr>
                <th>N° Vente</th>
                <th>Date</th>
                <th>Client</th>
                <th>Téléphone</th>
                <th class="text-right">Montant total</th>
                <th class="text-right">Payé</th>
                <th class="text-right">Reste</th>
                <th>Échéance</th>
                <th class="text-center">Statut</th>
            </tr>
        </thead>
        <tbody>
            @foreach($credits as $credit)
            <tr>
                <td>{{ $credit->sale->sale_number }}</td>
                <td>{{ $credit->created_at->format('d/m/Y') }}</td>
                <td>{{ $credit->customer->name }}</td>
                <td>{{ $credit->customer->phone ?? 'Non renseigné' }}</td>
                <td class="text-right">{{ number_format($credit->total_amount, 0, ',', ' ') }} FCFA</td>
                <td class="text-right">{{ number_format($credit->amount_paid, 0, ',', ' ') }} FCFA</td>
                <td class="text-right">{{ number_format($credit->remaining_balance, 0, ',', ' ') }} FCFA</td>
                <td>
                    @if($credit->due_date)
                        {{ $credit->due_date->format('d/m/Y') }}
                        @if($credit->isOverdue())
                            <br><small style="color: #dc3545;">({{ $credit->due_date->diffInDays(now()) }} jours de retard)</small>
                        @endif
                    @else
                        Non définie
                    @endif
                </td>
                <td class="text-center">
                    <span class="badge 
                        @if($credit->status === 'active' && $credit->isOverdue()) badge-overdue
                        @elseif($credit->status === 'active') badge-active
                        @elseif($credit->status === 'paid') badge-paid
                        @elseif($credit->status === 'cancelled') badge-cancelled
                        @endif">
                        @if($credit->status === 'active' && $credit->isOverdue())
                            En retard
                        @else
                            @switch($credit->status)
                                @case('active') Actif @break
                                @case('paid') Payé @break
                                @case('cancelled') Annulé @break
                                @default {{ ucfirst($credit->status) }}
                            @endswitch
                        @endif
                    </span>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @else
    <p style="text-align: center; color: #888; padding: 40px;">Aucun crédit trouvé</p>
    @endif

    <!-- Résumé des crédits en retard -->
    @php
        $overdueCredits = $credits->filter(function($credit) {
            return $credit->status === 'active' && $credit->isOverdue();
        });
    @endphp
    
    @if($overdueCredits->count() > 0)
    <div class="section-title" style="color: #dc3545;">Crédits en retard ({{ $overdueCredits->count() }})</div>
    <table>
        <thead>
            <tr>
                <th>Client</th>
                <th>N° Vente</th>
                <th>Échéance</th>
                <th>Jours de retard</th>
                <th class="text-right">Montant dû</th>
            </tr>
        </thead>
        <tbody>
            @foreach($overdueCredits->sortBy(function($credit) { return $credit->due_date; }) as $credit)
            <tr>
                <td>{{ $credit->customer->name }}</td>
                <td>{{ $credit->sale->sale_number }}</td>
                <td>{{ $credit->due_date->format('d/m/Y') }}</td>
                <td style="color: #dc3545; font-weight: bold;">{{ $credit->due_date->diffInDays(now()) }} jours</td>
                <td class="text-right" style="color: #dc3545; font-weight: bold;">{{ number_format($credit->remaining_balance, 0, ',', ' ') }} FCFA</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <div class="footer">
        <p>Rapport généré le {{ now()->format('d/m/Y à H:i') }} par {{ auth()->user()->name }}</p>
        <p>{{ config('app.name') }} - Système de Point de Vente</p>
    </div>
</body>
</html>
