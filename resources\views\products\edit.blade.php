<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-edit mr-2"></i>{{ __('Modifier le Produit') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('products.show', $product) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-eye mr-2"></i>Voir
                </a>
                <a href="{{ route('products.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form action="{{ route('products.update', $product) }}" method="POST" class="space-y-6">
                        @csrf
                        @method('PUT')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Informations de base -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900 border-b pb-2">
                                    <i class="fas fa-info-circle mr-2"></i>Informations de base
                                </h3>
                                
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nom du produit <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" 
                                           name="name" 
                                           id="name" 
                                           value="{{ old('name', $product->name) }}"
                                           required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                                    @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                        Description
                                    </label>
                                    <textarea name="description" 
                                              id="description" 
                                              rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('description') border-red-500 @enderror">{{ old('description', $product->description) }}</textarea>
                                    @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="barcode" class="block text-sm font-medium text-gray-700 mb-2">
                                        Code-barres
                                    </label>
                                    <div class="flex">
                                        <input type="text" 
                                               name="barcode" 
                                               id="barcode" 
                                               value="{{ old('barcode', $product->barcode) }}"
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('barcode') border-red-500 @enderror">
                                        <button type="button" 
                                                onclick="generateBarcode()"
                                                class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700">
                                            <i class="fas fa-random"></i>
                                        </button>
                                    </div>
                                    @error('barcode')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="unit" class="block text-sm font-medium text-gray-700 mb-2">
                                        Unité <span class="text-red-500">*</span>
                                    </label>
                                    <select name="unit"
                                            id="unit"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('unit') border-red-500 @enderror">
                                        <option value="">Sélectionner une unité</option>
                                        <option value="pcs" {{ old('unit', $product->unit) == 'pcs' ? 'selected' : '' }}>Pièces (pcs)</option>
                                        <option value="kg" {{ old('unit', $product->unit) == 'kg' ? 'selected' : '' }}>Kilogrammes (kg)</option>
                                        <option value="g" {{ old('unit', $product->unit) == 'g' ? 'selected' : '' }}>Grammes (g)</option>
                                        <option value="L" {{ old('unit', $product->unit) == 'L' ? 'selected' : '' }}>Litres (L)</option>
                                        <option value="ml" {{ old('unit', $product->unit) == 'ml' ? 'selected' : '' }}>Millilitres (ml)</option>
                                        <option value="m" {{ old('unit', $product->unit) == 'm' ? 'selected' : '' }}>Mètres (m)</option>
                                        <option value="cm" {{ old('unit', $product->unit) == 'cm' ? 'selected' : '' }}>Centimètres (cm)</option>
                                        <option value="boîte" {{ old('unit', $product->unit) == 'boîte' ? 'selected' : '' }}>Boîtes</option>
                                        <option value="carton" {{ old('unit', $product->unit) == 'carton' ? 'selected' : '' }}>Cartons</option>
                                    </select>
                                    @error('unit')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Stock -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900 border-b pb-2">
                                    <i class="fas fa-warehouse mr-2"></i>Gestion du stock
                                </h3>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Stock actuel
                                    </label>
                                    <div class="flex items-center space-x-2">
                                        <input type="text" 
                                               value="{{ $product->stock_quantity }} {{ $product->unit }}"
                                               readonly
                                               class="flex-1 px-3 py-2 bg-gray-100 border border-gray-300 rounded-md">
                                        @can('manage_stock')
                                        <button type="button" 
                                                onclick="openStockModal({{ $product->id }}, '{{ $product->name }}', {{ $product->stock_quantity }})"
                                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                            <i class="fas fa-warehouse mr-2"></i>Ajuster
                                        </button>
                                        @endcan
                                    </div>
                                    <p class="mt-1 text-sm text-gray-500">
                                        Utilisez le bouton "Ajuster" pour modifier le stock
                                    </p>
                                </div>

                                <div>
                                    <label for="min_stock_level" class="block text-sm font-medium text-gray-700 mb-2">
                                        Seuil d'alerte stock <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number" 
                                           name="min_stock_level" 
                                           id="min_stock_level" 
                                           value="{{ old('min_stock_level', $product->min_stock_level) }}"
                                           min="0"
                                           required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('min_stock_level') border-red-500 @enderror">
                                    @error('min_stock_level')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Statut -->
                                <div class="pt-4">
                                    <h3 class="text-sm font-medium text-gray-900 mb-4">
                                        <i class="fas fa-toggle-on mr-2"></i>Disponibilité
                                    </h3>
                                    <div class="flex items-center">
                                        <input type="hidden" name="is_active" value="0">
                                        <input type="checkbox" 
                                               name="is_active" 
                                               id="is_active" 
                                               value="1"
                                               {{ old('is_active', $product->is_active) ? 'checked' : '' }}
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                            Produit actif (disponible à la vente)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="flex justify-end space-x-4 border-t pt-6">
                            <a href="{{ route('products.show', $product) }}" 
                               class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                                Annuler
                            </a>
                            <button type="submit" 
                                    class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-save mr-2"></i>Mettre à jour
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'ajustement de stock -->
    <div id="stock-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Ajuster le stock</h3>
                <form id="stock-form" method="POST">
                    @csrf
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Produit</label>
                        <p id="product-name" class="text-sm text-gray-900"></p>
                        <p class="text-sm text-gray-500">Stock actuel: <span id="current-stock"></span></p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Type d'ajustement</label>
                        <select name="adjustment_type" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="in">Entrée de stock</option>
                            <option value="out">Sortie de stock</option>
                            <option value="adjustment">Ajustement (nouveau stock)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quantité</label>
                        <input type="number" name="quantity" required min="1" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Raison</label>
                        <input type="text" name="reason" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md"
                               placeholder="Ex: Réapprovisionnement, Inventaire...">
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closeStockModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Ajuster
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Générer un code-barres aléatoire
        function generateBarcode() {
            const barcode = Date.now().toString() + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            document.getElementById('barcode').value = barcode;
        }

        // Gestion du modal de stock
        function openStockModal(productId, productName, currentStock) {
            document.getElementById('product-name').textContent = productName;
            document.getElementById('current-stock').textContent = currentStock;
            document.getElementById('stock-form').action = `/products/${productId}/adjust-stock`;
            document.getElementById('stock-modal').classList.remove('hidden');
        }

        function closeStockModal() {
            document.getElementById('stock-modal').classList.add('hidden');
            document.getElementById('stock-form').reset();
        }

        // Fermer le modal en cliquant à l'extérieur
        document.getElementById('stock-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeStockModal();
            }
        });
    </script>
    @endpush
</x-app-layout>
