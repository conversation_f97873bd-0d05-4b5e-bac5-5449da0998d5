<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-credit-card mr-2"></i>{{ __('Gestion des Crédits') }}
            </h2>
            {{-- p  --}}
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <!-- Statistiques -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-yellow-50 p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-credit-card text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Crédits Actifs</p>
                            <p class="text-2xl font-semibold text-gray-900">
                                {{ number_format($totalCredits, 0, ',', ' ') }} FCFA</p>
                        </div>
                    </div>
                </div>

                <div class="bg-red-50 p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Crédits en Retard</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $overdueCredits }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Crédits Payés</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $paidCredits }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-list text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Crédits</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $credits->total() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">

                    <!-- Filtres -->
                    <div class="mb-6 flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" placeholder="Rechercher par client ou numéro de vente..."
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex gap-2">
                            <select
                                class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Tous les statuts</option>
                                <option value="active">Actifs</option>
                                <option value="overdue">En retard</option>
                                <option value="paid">Payés</option>
                                <option value="cancelled">Annulés</option>
                            </select>
                        </div>
                    </div>

                    <!-- Table des crédits -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Client
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Vente
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Montant
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Échéance
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Statut
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($credits as $credit)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div
                                                        class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                                                        <i class="fas fa-user text-gray-400"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        {{ $credit->customer->name }}</div>
                                                    <div class="text-sm text-gray-500">
                                                        {{ $credit->customer->phone ?? 'Pas de téléphone' }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $credit->sale->sale_number }}</div>
                                            <div class="text-sm text-gray-500">
                                                {{ $credit->created_at->format('d/m/Y H:i') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                Total: {{ number_format($credit->total_amount, 0, ',', ' ') }} FCFA
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                Payé: {{ number_format($credit->amount_paid, 0, ',', ' ') }} FCFA
                                            </div>
                                            <div
                                                class="text-sm font-medium 
                                            @if ($credit->remaining_balance > 0) text-red-600 
                                            @else text-green-600 @endif">
                                                Reste: {{ number_format($credit->remaining_balance, 0, ',', ' ') }}
                                                FCFA
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if ($credit->due_date)
                                                <div class="text-sm text-gray-900">
                                                    {{ $credit->due_date->format('d/m/Y') }}</div>
                                                @if ($credit->isOverdue())
                                                    <div class="text-sm text-red-600">
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                                        En retard de {{ $credit->due_date->diffInDays(now()) }} jour(s)
                                                    </div>
                                                @else
                                                    <div class="text-sm text-gray-500">
                                                        Dans {{ now()->diffInDays($credit->due_date) }} jour(s)
                                                    </div>
                                                @endif
                                            @else
                                                <span class="text-sm text-gray-400">Non définie</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @switch($credit->status)
                                                @case('active')
                                                    @if ($credit->isOverdue())
                                                        <span
                                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                            <i class="fas fa-exclamation-triangle mr-1"></i>En retard
                                                        </span>
                                                    @else
                                                        <span
                                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                            <i class="fas fa-clock mr-1"></i>Actif
                                                        </span>
                                                    @endif
                                                @break

                                                @case('paid')
                                                    <span
                                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <i class="fas fa-check-circle mr-1"></i>Payé
                                                    </span>
                                                @break

                                                @case('cancelled')
                                                    <span
                                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        <i class="fas fa-times-circle mr-1"></i>Annulé
                                                    </span>
                                                @break
                                            @endswitch
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                <a href="{{ route('credits.show', $credit) }}"
                                    class="text-blue-600 hover:text-blue-900 transition-all duration-200"
                                    title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </a>

                                <a href="{{ route('pos.index', ['customer' => $credit->customer->id]) }}"
                                    class="text-green-600 hover:text-green-900 transition-all duration-200"
                                    title="Nouvelle vente">
                                    <i class="fas fa-cash-register"></i>
                                </a>                                                @if ($credit->status === 'active' && $credit->remaining_balance > 0)
                                                    @can('manage_credit_payments')
                                                        <button
                                                            onclick="openPaymentModal({{ $credit->id }}, '{{ $credit->customer->name }}', {{ $credit->remaining_balance }})"
                                                            class="text-green-600 hover:text-green-900 transition-all duration-200"
                                                            title="Ajouter paiement">
                                                            <i class="fas fa-money-bill-wave"></i>
                                                        </button>
                                                    @endcan
                                                @endif

                                                <a href="{{ route('credits.payments', $credit) }}"
                                                    class="text-purple-600 hover:text-purple-900 transition-all duration-200"
                                                    title="Historique paiements">
                                                    <i class="fas fa-history"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                                Aucun crédit trouvé
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="mt-6">
                            {{ $credits->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal de paiement -->
        <div id="payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Ajouter un paiement</h3>
                    <form id="payment-form" method="POST">
                        @csrf
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Client</label>
                            <p id="customer-name" class="text-sm text-gray-900"></p>
                            <p class="text-sm text-gray-500">Montant dû: <span id="remaining-balance"></span> FCFA</p>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Montant du paiement (FCFA)</label>
                            <input type="number" name="amount" required min="0.01" step="0.01"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Mode de paiement</label>
                            <select name="payment_method" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                <option value="cash">Espèces</option>
                                <option value="mobile_money">Mobile Money</option>
                                <option value="bank_transfer">Virement bancaire</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Référence (optionnel)</label>
                            <input type="text" name="reference"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md"
                                placeholder="Ex: Numéro de transaction">
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Notes (optionnel)</label>
                            <textarea name="notes" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md"></textarea>
                        </div>

                        <div class="flex justify-end space-x-2">
                            <button type="button" onclick="closePaymentModal()"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                                Annuler
                            </button>
                            <button type="submit"
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                Enregistrer le paiement
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        @push('scripts')
            <script>
                // Fonction pour ouvrir le modal de paiement
                function openPaymentModal(creditId, customerName, remainingBalance) {
                    document.getElementById('customer-name').textContent = customerName;
                    document.getElementById('remaining-balance').textContent = remainingBalance.toLocaleString();
                    document.getElementById('payment-form').action = `/credits/${creditId}/payment`;
                    document.querySelector('input[name="amount"]').max = remainingBalance;
                    document.getElementById('payment-modal').classList.remove('hidden');
                }

                function closePaymentModal() {
                    document.getElementById('payment-modal').classList.add('hidden');
                    document.getElementById('payment-form').reset();
                }

                // Fermer le modal en cliquant à l'extérieur
                document.getElementById('payment-modal').addEventListener('click', function(e) {
                    if (e.target === this) {
                        closePaymentModal();
                    }
                });
            </script>
        @endpush
    </x-app-layout>
