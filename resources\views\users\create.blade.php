<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-user-plus mr-3 text-green-600"></i>Nouvel Utilisateur
                </h2>
                <p class="text-sm text-gray-600 mt-1">Ajoutez un nouveau membre à votre équipe</p>
            </div>
            <x-button variant="secondary" href="{{ route('users.index') }}" icon="fas fa-arrow-left">
                Retour
            </x-button>
        </div>
    </x-slot>

    <div class="py-4 sm:py-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <x-card title="Informations de l'utilisateur" icon="fas fa-user">
                <form action="{{ route('users.store') }}" method="POST" class="space-y-6">
                    @csrf
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Section Informations personnelles -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                                <i class="fas fa-id-card mr-2 text-blue-600"></i>Informations personnelles
                            </h3>
                        </div>

                        <!-- Nom -->
                        <x-form-field 
                            label="Nom complet" 
                            name="name" 
                            :required="true"
                            icon="fas fa-user"
                            placeholder="Nom et prénom de l'utilisateur"
                            :value="old('name')"
                        />

                        <!-- Email -->
                        <x-form-field 
                            label="Adresse email" 
                            name="email" 
                            type="email"
                            :required="true"
                            icon="fas fa-envelope"
                            placeholder="<EMAIL>"
                            :value="old('email')"
                            help="L'email servira d'identifiant de connexion"
                        />

                        <!-- Téléphone -->
                        <x-form-field 
                            label="Téléphone" 
                            name="phone" 
                            icon="fas fa-phone"
                            placeholder="+33 1 23 45 67 89"
                            :value="old('phone')"
                        />

                        <!-- Rôle -->
                        <x-form-field 
                            label="Rôle" 
                            name="role" 
                            type="select"
                            :required="true"
                            icon="fas fa-user-tag"
                            :options="$roles->pluck('name', 'name')->toArray()"
                            placeholder="Sélectionner un rôle"
                            help="Détermine les permissions de l'utilisateur"
                        />

                        <!-- Section Sécurité -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                                <i class="fas fa-shield-alt mr-2 text-green-600"></i>Sécurité et accès
                            </h3>
                        </div>

                        <!-- Mot de passe -->
                        <x-form-field 
                            label="Mot de passe" 
                            name="password" 
                            type="password"
                            :required="true"
                            icon="fas fa-lock"
                            placeholder="••••••••"
                            help="Minimum 8 caractères"
                        />

                        <!-- Confirmation mot de passe -->
                        <x-form-field 
                            label="Confirmer le mot de passe" 
                            name="password_confirmation" 
                            type="password"
                            :required="true"
                            icon="fas fa-lock"
                            placeholder="••••••••"
                            help="Doit être identique au mot de passe"
                        />

                        <!-- Section Paramètres -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                                <i class="fas fa-cog mr-2 text-purple-600"></i>Paramètres du compte
                            </h3>
                        </div>

                        <!-- Statut -->
                        <div class="lg:col-span-2">
                            <x-form-field 
                                label="Statut du compte" 
                                name="is_active" 
                                type="checkbox"
                                placeholder="Compte actif"
                                :value="old('is_active', true)"
                                help="Les comptes inactifs ne peuvent pas se connecter"
                            />
                        </div>
                    </div>

                    <!-- Informations sur les rôles -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">
                            <i class="fas fa-info-circle mr-2"></i>Informations sur les rôles
                        </h4>
                        <div class="text-sm text-blue-800 space-y-1">
                            <p><strong>Super Admin :</strong> Accès complet à toutes les fonctionnalités</p>
                            <p><strong>Admin :</strong> Gestion des utilisateurs, produits, clients et rapports</p>
                            <p><strong>Manager :</strong> Gestion des ventes, stocks et clients</p>
                            <p><strong>Caissier :</strong> Point de vente et gestion des ventes uniquement</p>
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="flex flex-col sm:flex-row sm:justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                        <x-button variant="secondary" href="{{ route('users.index') }}" icon="fas fa-times">
                            Annuler
                        </x-button>
                        <x-button type="submit" variant="primary" icon="fas fa-user-plus">
                            Créer l'utilisateur
                        </x-button>
                    </div>
                </form>
            </x-card>
        </div>
    </div>
</x-app-layout>
