<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-user-plus mr-2"></i>{{ __('Nouveau Client') }}
            </h2>
            <a href="{{ route('customers.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Retour
            </a>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form id="customer-form" action="{{ route('customers.store') }}" method="POST" class="space-y-6">
                        @csrf
                        
                        <!-- Informations personnelles -->
                        <div class="space-y-4">
                            <div class="flex items-center bg-blue-50 p-4 rounded-lg mb-4">
                                <i class="fas fa-user-circle text-2xl text-blue-500 mr-3"></i>
                                <div>
                                    <h3 class="font-medium text-blue-800">Informations personnelles</h3>
                                    <p class="text-sm text-blue-600">Détails du client</p>
                                </div>
                            </div>
                            
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nom complet <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-user text-gray-400"></i>
                                    </div>
                                    <input type="text" 
                                           name="name" 
                                           id="name" 
                                           value="{{ old('name') }}"
                                           required
                                           minlength="3"
                                           placeholder="Ex: Jean Dupont"
                                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                                </div>
                                @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                    Email
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-envelope text-gray-400"></i>
                                    </div>
                                    <input type="email" 
                                           name="email" 
                                           id="email" 
                                           value="{{ old('email') }}"
                                           placeholder="Ex: <EMAIL>"
                                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('email') border-red-500 @enderror">
                                </div>
                                @error('email')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                    Téléphone
                                </label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-phone text-gray-400"></i>
                                    </div>
                                    <input type="tel" 
                                           name="phone" 
                                           id="phone" 
                                           value="{{ old('phone') }}"
                                           placeholder="Ex: +221 77 123 45 67"
                                           pattern="[\d\s\+\-\(\)]*"
                                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('phone') border-red-500 @enderror">
                                </div>
                                @error('phone')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                    Adresse
                                </label>
                                <div class="relative">
                                    <div class="absolute top-3 left-3 pointer-events-none">
                                        <i class="fas fa-map-marker-alt text-gray-400"></i>
                                    </div>
                                    <textarea name="address" 
                                              id="address" 
                                              rows="3"
                                              placeholder="Adresse complète du client"
                                              class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('address') border-red-500 @enderror">{{ old('address') }}</textarea>
                                </div>
                                @error('address')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="flex items-center mb-2">
                                    <input type="checkbox"
                                           name="is_active"
                                           id="is_active"
                                           value="1"
                                           {{ old('is_active', true) ? 'checked' : '' }}
                                           class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                    <label for="is_active" class="ml-2 text-sm font-medium text-gray-700">
                                        Client actif
                                    </label>
                                </div>
                                <p class="text-xs text-gray-500">Les clients inactifs ne peuvent pas effectuer d'achats</p>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="flex justify-end space-x-3 border-t pt-6">
                            <button type="button" 
                                    onclick="window.location='{{ route('customers.index') }}'"
                                    class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-times mr-2"></i>Annuler
                            </button>
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-save mr-2"></i>Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('customer-form');

            // Formatage automatique du numéro de téléphone
            const phoneInput = document.getElementById('phone');
            phoneInput.addEventListener('input', function(e) {
                // Supprimer tous les caractères non numériques sauf + ( ) -
                let value = this.value.replace(/[^\d\+\(\)\-]/g, '');
                // Limiter la longueur
                if (value.length > 15) value = value.slice(0, 15);
                this.value = value;
            });

            // Validation du formulaire
            form.addEventListener('submit', function(e) {
                let errors = [];
                const name = document.getElementById('name').value.trim();
                const email = document.getElementById('email').value.trim();

                if (name.length < 3) {
                    errors.push('Le nom doit contenir au moins 3 caractères');
                }

                if (email && !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
                    errors.push('L\'adresse email n\'est pas valide');
                }

                if (errors.length > 0) {
                    e.preventDefault();
                    errors.forEach(error => notyf.error(error));
                }
            });
        });
    </script>
    @endpush
</x-app-layout>
