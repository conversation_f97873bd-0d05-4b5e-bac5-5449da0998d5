<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\CreditController;
use App\Http\Controllers\POSController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\PromotionController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\ProductPriceController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

// Redirection intelligente selon le rôle de l'utilisateur
Route::get('/', function () {
    if (!Auth::check()) {
        return redirect()->route('login');
    }

    $user = Auth::user();
    $userRoles = $user->roles->pluck('name')->toArray();

    // SuperAdmin et Admin vont au dashboard
    if (in_array('superAdmin', $userRoles) || in_array('admin', $userRoles)) {
        return redirect()->route('dashboard');
    }

    // Manager et Caissier vont au point de vente
    return redirect()->route('pos.index');
});

// Routes protégées par authentification
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])
        ->name('dashboard')
        ->middleware(['auth']);
    Route::get('/dashboard/stats', [DashboardController::class, 'getStats'])
        ->name('dashboard.stats')
        ->middleware(['auth']);
    Route::get('/dashboard/chart/{type}', [DashboardController::class, 'getChartData'])
        ->name('dashboard.getChartData')
        ->middleware(['auth']);
    Route::get('/dashboard/alerts', [DashboardController::class, 'getAlerts'])
        ->name('dashboard.getAlerts')
        ->middleware(['auth']);
    Route::get('/dashboard/employee-stats', [DashboardController::class, 'getEmployeeStats'])
        ->name('dashboard.getEmployeeStats')
        ->middleware(['auth']);

    // Fonctionnalités bonus
    Route::get('/bonus-features', function () {
        return view('bonus-features');
    })->name('bonus-features');

    // Point de vente (POS)
    Route::get('/pos', [POSController::class, 'index'])->name('pos.index');
    Route::post('/pos/sale', [POSController::class, 'processSale'])->name('pos.sale');
    Route::post('/pos/save-sale', [POSController::class, 'saveSale'])->name('pos.save-sale');
    Route::get('/pos/receipt/{sale}', [POSController::class, 'receipt'])->name('pos.receipt');
    Route::get('/pos/search-customers', [POSController::class, 'searchCustomers'])->name('pos.search-customers');

    // Gestion des produits
    Route::resource('products', ProductController::class);
    Route::post('/products/{product}/adjust-stock', [ProductController::class, 'adjustStock'])->name('products.adjust-stock');

    // Gestion des clients
    Route::resource('customers', CustomerController::class);
    Route::get('/customers/{customer}/credits', [CustomerController::class, 'credits'])->name('customers.credits');

    // Gestion des ventes
    Route::resource('sales', SaleController::class)->except(['create', 'store']);
    Route::get('/sales/{sale}/receipt', [SaleController::class, 'receipt'])->name('sales.receipt');

    // Gestion des crédits
    Route::resource('credits', CreditController::class)->except(['create', 'store']);
    Route::post('/credits/{credit}/payment', [CreditController::class, 'addPayment'])->name('credits.payment');
    Route::get('/credits/{credit}/payments', [CreditController::class, 'payments'])->name('credits.payments');

    // Rapports
    Route::get('/reports', [ReportsController::class, 'index'])->name('reports.index');
    Route::get('/reports/profit-analytics', [ReportsController::class, 'profitAnalytics'])->name('reports.profit-analytics');
    Route::get('/reports/stock-analytics', [ReportsController::class, 'stockAnalytics'])->name('reports.stock-analytics');
    Route::post('/reports/export-profit', [ReportsController::class, 'exportProfitReport'])->name('reports.export-profit');

    // Routes pour les rapports classiques (existants)
    Route::match(['GET', 'POST'], '/reports/sales', [ReportController::class, 'salesReport'])->name('reports.sales');
    Route::match(['GET', 'POST'], '/reports/credits', [ReportController::class, 'creditsReport'])->name('reports.credits');
    Route::match(['GET', 'POST'], '/reports/stock', [ReportController::class, 'stockReport'])->name('reports.stock');

    // Gestion des fournisseurs
    Route::resource('suppliers', SupplierController::class);

    // Gestion des promotions
    Route::resource('promotions', PromotionController::class);
    Route::patch('/promotions/{promotion}/toggle', [PromotionController::class, 'toggle'])->name('promotions.toggle');

    // Routes pour la gestion des magasins
    Route::resource('stores', StoreController::class);
    Route::post('stores/{store}/assign-users', [StoreController::class, 'assignUsers'])->name('stores.assign-users');
    Route::post('stores/{store}/initialize', [StoreController::class, 'initializeFromStore'])->name('stores.initialize');
    Route::get('store-dashboard', [StoreController::class, 'dashboard'])->name('store.dashboard');

    // Routes pour la gestion des prix par magasin
    Route::resource('product-prices', ProductPriceController::class);
    Route::post('product-prices/bulk-update', [ProductPriceController::class, 'bulkUpdate'])->name('product-prices.bulk-update');
    Route::post('product-prices/copy', [ProductPriceController::class, 'copyPrices'])->name('product-prices.copy');

    // Gestion des utilisateurs (réservé aux super admins et admins)
    Route::middleware('can:manage_users')->group(function () {
        Route::resource('users', UserController::class);
        Route::patch('/users/{user}/toggle', [UserController::class, 'toggle'])->name('users.toggle');
        Route::patch('/users/{user}/reset-password', [UserController::class, 'resetPassword'])->name('users.reset-password');
    });

    // Profil utilisateur
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Routes additionnelles pour la gestion des magasins, ventes, produits et rapports
Route::middleware(['auth'])->group(function () {
    // Routes liées aux magasins
    Route::resource('stores', StoreController::class);
    Route::get('stores/{store}/stock', [StoreController::class, 'stockStatus']);
    Route::post('stores/{store}/stock/adjust', [StoreController::class, 'adjustStock']);
    
    // Routes liées aux ventes
    Route::resource('sales', SaleController::class);
    Route::get('sales/{store}/new', [SaleController::class, 'create']);
    Route::get('sales/{store}/reports', [SaleController::class, 'reports']);
    
    // Routes liées aux produits
    Route::resource('products', ProductController::class);
    Route::post('products/{product}/prices', [ProductController::class, 'updatePrices']);
    
    // Routes liées aux clients
    Route::resource('customers', CustomerController::class);
    Route::get('customers/{customer}/credits', [CustomerController::class, 'credits']);
    
    // Routes pour les rapports
    Route::prefix('reports')->group(function () {
        Route::get('daily/{store}', [ReportController::class, 'daily']);
        Route::get('monthly/{store}', [ReportController::class, 'monthly']);
        Route::get('inventory/{store}', [ReportController::class, 'inventory']);
    });
});

require __DIR__ . '/auth.php';
