<?php

namespace App\Http\Controllers;

use App\Models\Promotion;
use App\Models\Product;
use App\Models\Customer;
use Illuminate\Http\Request;
use Carbon\Carbon;

class PromotionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $promotions = Promotion::latest()->paginate(20);

        // Statistiques
        $totalPromotions = Promotion::count();
        $activePromotions = Promotion::active()->count();
        $expiredPromotions = Promotion::expired()->count();
        $expiringPromotions = Promotion::where('is_active', true)
            ->where('end_date', '<=', Carbon::now()->addDays(3))
            ->where('end_date', '>=', Carbon::now())
            ->count();

        return view('promotions.index', compact('promotions', 'totalPromotions', 'activePromotions', 'expiredPromotions', 'expiringPromotions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $products = Product::active()->get();
        $customers = Customer::active()->get();

        return view('promotions.create', compact('products', 'customers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:percentage,fixed_amount,buy_x_get_y',
            'value' => 'required|numeric|min:0',
            'min_quantity' => 'required|integer|min:1',
            'free_quantity' => 'nullable|integer|min:0',
            'min_amount' => 'nullable|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'usage_limit' => 'nullable|integer|min:1',
            'applicable_products' => 'nullable|array',
            'applicable_customers' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        // Traitement des champs spéciaux
        $validated['free_quantity'] = $validated['free_quantity'] ?? 0;
        $validated['min_amount'] = $validated['min_amount'] ?? 0;
        $validated['is_active'] = $request->has('is_active');
        $validated['usage_count'] = 0;

        // Conversion des tableaux en JSON
        $validated['applicable_products'] = !empty($validated['applicable_products']) ? $validated['applicable_products'] : null;
        $validated['applicable_customers'] = !empty($validated['applicable_customers']) ? $validated['applicable_customers'] : null;

        $promotion = Promotion::create($validated);

        return redirect()->route('promotions.index')
            ->with('success', 'Promotion créée avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Promotion $promotion)
    {
        // Charger les produits et clients applicables
        $applicableProducts = collect();
        $applicableCustomers = collect();

        if ($promotion->applicable_products) {
            $applicableProducts = Product::whereIn('id', $promotion->applicable_products)->get();
        }

        if ($promotion->applicable_customers) {
            $applicableCustomers = Customer::whereIn('id', $promotion->applicable_customers)->get();
        }

        return view('promotions.show', compact('promotion', 'applicableProducts', 'applicableCustomers'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Promotion $promotion)
    {
        $products = Product::active()->get();
        $customers = Customer::active()->get();

        return view('promotions.edit', compact('promotion', 'products', 'customers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Promotion $promotion)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:percentage,fixed_amount,buy_x_get_y',
            'value' => 'required|numeric|min:0',
            'min_quantity' => 'required|integer|min:1',
            'free_quantity' => 'nullable|integer|min:0',
            'min_amount' => 'nullable|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'usage_limit' => 'nullable|integer|min:1',
            'applicable_products' => 'nullable|array',
            'applicable_customers' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        // Traitement des champs spéciaux
        $validated['free_quantity'] = $validated['free_quantity'] ?? 0;
        $validated['min_amount'] = $validated['min_amount'] ?? 0;
        $validated['is_active'] = $request->has('is_active');

        // Conversion des tableaux en JSON
        $validated['applicable_products'] = !empty($validated['applicable_products']) ? $validated['applicable_products'] : null;
        $validated['applicable_customers'] = !empty($validated['applicable_customers']) ? $validated['applicable_customers'] : null;

        $promotion->update($validated);

        return redirect()->route('promotions.index')
            ->with('success', 'Promotion mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Promotion $promotion)
    {
        $promotion->delete();

        return redirect()->route('promotions.index')
            ->with('success', 'Promotion supprimée avec succès.');
    }

    /**
     * Toggle promotion status
     */
    public function toggle(Promotion $promotion)
    {
        $promotion->update(['is_active' => !$promotion->is_active]);

        $status = $promotion->is_active ? 'activée' : 'désactivée';

        return redirect()->back()
            ->with('success', "Promotion {$status} avec succès.");
    }
}
