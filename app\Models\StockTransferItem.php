<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StockTransferItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'stock_transfer_id',
        'product_id',
        'quantity_requested',
        'quantity_approved',
        'quantity_sent',
        'quantity_received',
        'unit_cost',
        'total_cost',
        'status',
        'notes',
        'shipped_at',
        'received_at'
    ];

    protected function casts(): array
    {
        return [
            'quantity_requested' => 'integer',
            'quantity_approved' => 'integer',
            'quantity_sent' => 'integer',
            'quantity_received' => 'integer',
            'unit_cost' => 'decimal:2',
            'total_cost' => 'decimal:2',
            'shipped_at' => 'datetime',
            'received_at' => 'datetime',
        ];
    }

    /**
     * Relations
     */
    public function stockTransfer(): BelongsTo
    {
        return $this->belongsTo(StockTransfer::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeShipped($query)
    {
        return $query->where('status', 'shipped');
    }

    public function scopeReceived($query)
    {
        return $query->where('status', 'received');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Accessors
     */
    public function getStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'En attente',
            'approved' => 'Approuvé',
            'rejected' => 'Rejeté',
            'shipped' => 'Expédié',
            'received' => 'Reçu',
            'cancelled' => 'Annulé'
        ];

        return $labels[$this->status] ?? $this->status;
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'warning',
            'approved' => 'info',
            'rejected' => 'danger',
            'shipped' => 'primary',
            'received' => 'success',
            'cancelled' => 'secondary'
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    /**
     * Calculer l'écart entre demandé et reçu
     */
    public function getDiscrepancyAttribute()
    {
        return $this->quantity_received - $this->quantity_requested;
    }

    /**
     * Vérifier s'il y a un écart
     */
    public function hasDiscrepancy(): bool
    {
        return $this->quantity_received != $this->quantity_requested;
    }

    /**
     * Calculer le pourcentage de réception
     */
    public function getReceiptPercentageAttribute()
    {
        if ($this->quantity_requested == 0) {
            return 0;
        }
        
        return round(($this->quantity_received / $this->quantity_requested) * 100, 2);
    }

    /**
     * Vérifier si l'item est complètement reçu
     */
    public function isFullyReceived(): bool
    {
        return $this->quantity_received >= $this->quantity_requested;
    }

    /**
     * Vérifier si l'item est partiellement reçu
     */
    public function isPartiallyReceived(): bool
    {
        return $this->quantity_received > 0 && $this->quantity_received < $this->quantity_requested;
    }

    /**
     * Obtenir la valeur reçue
     */
    public function getReceivedValueAttribute()
    {
        return $this->quantity_received * $this->unit_cost;
    }

    /**
     * Obtenir la valeur de l'écart
     */
    public function getDiscrepancyValueAttribute()
    {
        return $this->discrepancy * $this->unit_cost;
    }
}
