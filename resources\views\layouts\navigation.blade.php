<!-- <PERSON> Header -->
<div class="lg:hidden bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
    <div class="flex items-center justify-between h-16 px-4">
        <!-- Mobile Menu Button -->
        <button @click="sidebarOpen = !sidebarOpen"
            class="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <i class="fas fa-bars text-xl"></i>
        </button>

        <!-- Logo -->
        <div class="flex items-center">
            <a href="{{ route('dashboard') }}" class="flex items-center space-x-2">
                <div
                    class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-cash-register text-white text-sm"></i>
                </div>
                <span class="text-xl font-bold text-gray-900">POS</span>
            </a>
        </div>

        <!-- User Menu -->
        <div class="relative" x-data="{ open: false }">
            <button @click="open = !open"
                class="flex items-center space-x-2 p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-sm"></i>
                </div>
            </button>

            <!-- Dropdown -->
            <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200"
                x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-75" x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-95"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                <div class="py-1">
                    <a href="{{ route('profile.edit') }}"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-user-cog mr-2"></i>Profil
                    </a>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit"
                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-sign-out-alt mr-2"></i>Déconnexion
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sidebar -->
<nav class="fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0"
    :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }" x-cloak>

    <!-- Sidebar Header -->
    <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200">
        <a href="{{ route('dashboard') }}" class="flex items-center space-x-3">
            <div
                class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-cash-register text-white"></i>
            </div>
            <div>
                <h1 class="text-xl font-bold text-gray-900">POS System</h1>
                <p class="text-xs text-gray-500">Point de Vente</p>
            </div>
        </a>

        <!-- Close button for mobile -->
        <button @click="sidebarOpen = false"
            class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- Navigation Links -->
    <div class="flex-1 px-4 py-6 overflow-y-auto">
        <nav class="space-y-2">
            <!-- Dashboard (seulement pour superAdmin et admin) -->
            @if (Auth::user()->roles->whereIn('name', ['superAdmin', 'admin'])->count() > 0)
                <a href="{{ route('dashboard') }}"
                    class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                      {{ request()->routeIs('dashboard')
                          ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                    <i
                        class="fas fa-tachometer-alt mr-3 text-lg {{ request()->routeIs('dashboard') ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                    Dashboard
                </a>
            @endif

            <!-- POS -->
            <a href="{{ route('pos.index') }}"
                class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                      {{ request()->routeIs('pos.*')
                          ? 'bg-green-50 text-green-700 border-r-2 border-green-600'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                <i
                    class="fas fa-cash-register mr-3 text-lg {{ request()->routeIs('pos.*') ? 'text-green-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                Point de Vente
                <span class="ml-auto bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full">
                    Ctrl+N
                </span>
            </a>

            <!-- Divider -->
            <div class="border-t border-gray-200 my-4"></div>

            <!-- Inventory Section -->
            <div class="mb-4">
                <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Inventaire</h3>

                @can('view_products')
                    <a href="{{ route('products.index') }}"
                        class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                          {{ request()->routeIs('products.*')
                              ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i
                            class="fas fa-box mr-3 text-lg {{ request()->routeIs('products.*') ? 'text-purple-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                        Produits
                    </a>
                @endcan
                {{-- @if (Auth::user()->roles->whereIn('name', ['superAdmin', 'admin', 'manager'])->count() > 0)
                    @can('view_products')
                        <a href="{{ route('suppliers.index') }}"
                            class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                          {{ request()->routeIs('suppliers.*')
                              ? 'bg-orange-50 text-orange-700 border-r-2 border-orange-600'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                            <i
                                class="fas fa-truck mr-3 text-lg {{ request()->routeIs('suppliers.*') ? 'text-orange-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                            Fournisseurs
                        </a>
                    @endcan
                @endif --}}
            </div>

            <!-- Sales Section -->
            <div class="mb-4">
                <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Ventes</h3>

                @can('view_customers')
                    <a href="{{ route('customers.index') }}"
                        class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                          {{ request()->routeIs('customers.*')
                              ? 'bg-indigo-50 text-indigo-700 border-r-2 border-indigo-600'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i
                            class="fas fa-users mr-3 text-lg {{ request()->routeIs('customers.*') ? 'text-indigo-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                        Clients
                    </a>
                @endcan

                <a href="{{ route('sales.index') }}"
                    class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                          {{ request()->routeIs('sales.*')
                              ? 'bg-yellow-50 text-yellow-700 border-r-2 border-yellow-600'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                    <i
                        class="fas fa-receipt mr-3 text-lg {{ request()->routeIs('sales.*') ? 'text-yellow-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                    Ventes
                </a>

                @can('view_credits')
                    <a href="{{ route('credits.index') }}"
                        class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                          {{ request()->routeIs('credits.*')
                              ? 'bg-red-50 text-red-700 border-r-2 border-red-600'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i
                            class="fas fa-credit-card mr-3 text-lg {{ request()->routeIs('credits.*') ? 'text-red-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                        Crédits
                    </a>
                @endcan

                {{-- @can('manage_users')
                    <a href="{{ route('promotions.index') }}"
                        class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                          {{ request()->routeIs('promotions.*')
                              ? 'bg-pink-50 text-pink-700 border-r-2 border-pink-600'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i
                            class="fas fa-tags mr-3 text-lg {{ request()->routeIs('promotions.*') ? 'text-pink-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                        Promotions
                    </a>
                @endcan --}}
            </div>

            <!-- Analytics Section -->
            @if (Auth::user()->roles->whereIn('name', ['superAdmin', 'admin'])->count() > 0)
                <div class="mb-4">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Analytics</h3>

                    @can('view_reports')
                        <a href="{{ route('reports.index') }}"
                            class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                          {{ request()->routeIs('reports.*')
                              ? 'bg-teal-50 text-teal-700 border-r-2 border-teal-600'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                            <i
                                class="fas fa-chart-bar mr-3 text-lg {{ request()->routeIs('reports.*') ? 'text-teal-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                            Rapports
                        </a>
                    @endcan
                </div>

                <!-- Administration Section -->
                <div class="mb-4">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Administration
                    </h3>

                    @can('manage_users')
                        <a href="{{ route('users.index') }}"
                            class="group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors
                          {{ request()->routeIs('users.*')
                              ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900' }}">
                            <i
                                class="fas fa-users-cog mr-3 text-lg {{ request()->routeIs('users.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600' }}"></i>
                            Utilisateurs
                        </a>
                    @endcan
                </div>
            @endif
        </nav>
    </div>

    <!-- User Profile Section -->
    <div class="border-t border-gray-200 p-4">
        <div class="flex items-center space-x-3">
            <div
                class="w-10 h-10 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-white"></i>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                    {{ Auth::user()->name }}
                </p>
                <p class="text-xs text-gray-500 truncate">
                    {{ Auth::user()->role ?? 'Utilisateur' }}
                </p>
            </div>
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open"
                    class="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100">
                    <i class="fas fa-ellipsis-v"></i>
                </button>

                <!-- Dropdown -->
                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200"
                    x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
                    x-transition:leave="transition ease-in duration-75"
                    x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95"
                    class="absolute bottom-full right-0 mb-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                    <div class="py-1">
                        <a href="{{ route('profile.edit') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user-cog mr-2"></i>Profil
                        </a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit"
                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>Déconnexion
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Mobile Overlay -->
<div x-show="sidebarOpen" @click="sidebarOpen = false"
    x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300"
    x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
    class="fixed inset-0 bg-gray-600 bg-opacity-75 z-30 lg:hidden" x-cloak></div>
