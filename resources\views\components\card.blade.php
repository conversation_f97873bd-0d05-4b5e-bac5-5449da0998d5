@props([
    'title' => null,
    'subtitle' => null,
    'icon' => null,
    'padding' => 'p-6',
    'shadow' => 'shadow-soft',
    'hover' => false,
    'border' => true,
    'background' => 'bg-white'
])

<div {{ $attributes->merge([
    'class' => "
        {$background} 
        {$shadow} 
        rounded-xl 
        " . ($border ? 'border border-gray-100' : '') . "
        " . ($hover ? 'hover:shadow-medium transition-all duration-200 hover:-translate-y-1' : '') . "
        overflow-hidden
    "
]) }}>
    @if($title || $subtitle || $icon)
    <div class="border-b border-gray-100 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                @if($icon)
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="{{ $icon }} text-white"></i>
                    </div>
                </div>
                @endif
                
                <div>
                    @if($title)
                    <h3 class="text-lg font-semibold text-gray-900">{{ $title }}</h3>
                    @endif
                    
                    @if($subtitle)
                    <p class="text-sm text-gray-500">{{ $subtitle }}</p>
                    @endif
                </div>
            </div>
            
            @isset($actions)
            <div class="flex items-center space-x-2">
                {{ $actions }}
            </div>
            @endisset
        </div>
    </div>
    @endif

    <div class="{{ $padding }}">
        {{ $slot }}
    </div>

    @isset($footer)
    <div class="border-t border-gray-100 px-6 py-4 bg-gray-50">
        {{ $footer }}
    </div>
    @endisset
</div>
