<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\StoreStock;
use App\Models\Sale;
use App\Services\CacheService;
use App\Services\MultiStoreAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class StoreController extends Controller
{
    protected $cacheService;
    protected $analyticsService;

    public function __construct(
        CacheService $cacheService,
        MultiStoreAnalyticsService $analyticsService
    ) {
        $this->cacheService = $cacheService;
        $this->analyticsService = $analyticsService;
        $this->middleware('auth:sanctum');
    }

    /**
     * Liste des magasins accessibles
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = Store::with(['users', 'sales' => function($q) {
            $q->where('created_at', '>=', now()->startOfMonth());
        }]);

        // Filtrer selon les permissions
        if (!$user->hasRole('superAdmin')) {
            if ($user->hasRole('admin')) {
                // Admin voit tous les magasins
            } elseif ($user->hasRole('manager')) {
                $query->whereIn('id', $user->managedStores()->pluck('id'));
            } else {
                $query->where('id', $user->store_id);
            }
        }

        $stores = $query->active()->get();

        return response()->json([
            'success' => true,
            'data' => $stores->map(function($store) {
                return [
                    'id' => $store->id,
                    'name' => $store->name,
                    'address' => $store->address,
                    'city' => $store->city,
                    'phone' => $store->phone,
                    'email' => $store->email,
                    'is_active' => $store->is_active,
                    'employees_count' => $store->users->count(),
                    'monthly_sales' => $store->sales->sum('total_amount'),
                    'created_at' => $store->created_at,
                    'updated_at' => $store->updated_at
                ];
            })
        ]);
    }

    /**
     * Détails d'un magasin
     */
    public function show(Request $request, Store $store)
    {
        $user = Auth::user();
        
        if (!$user->can('view', $store)) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé à ce magasin'
            ], 403);
        }

        $storeData = [
            'id' => $store->id,
            'name' => $store->name,
            'address' => $store->address,
            'city' => $store->city,
            'phone' => $store->phone,
            'email' => $store->email,
            'is_active' => $store->is_active,
            'created_at' => $store->created_at,
            'updated_at' => $store->updated_at
        ];

        // Ajouter les statistiques si demandées
        if ($request->get('include_stats', false)) {
            $stats = $this->cacheService->getStoreStats($store->id);
            $storeData['statistics'] = $stats;
        }

        // Ajouter les employés si demandés
        if ($request->get('include_employees', false)) {
            $storeData['employees'] = $store->users->map(function($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'roles' => $user->getRoleNames()
                ];
            });
        }

        return response()->json([
            'success' => true,
            'data' => $storeData
        ]);
    }

    /**
     * Statistiques d'un magasin
     */
    public function statistics(Request $request, Store $store)
    {
        $user = Auth::user();
        
        if (!$user->can('viewReports', $store)) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé aux statistiques de ce magasin'
            ], 403);
        }

        $period = $request->get('period', 'month');
        $stats = $this->cacheService->getStoreStats($store->id, $period);

        return response()->json([
            'success' => true,
            'data' => $stats,
            'period' => $period
        ]);
    }

    /**
     * Stock d'un magasin
     */
    public function stock(Request $request, Store $store)
    {
        $user = Auth::user();
        
        if (!$user->can('view', $store)) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé au stock de ce magasin'
            ], 403);
        }

        $query = StoreStock::with(['product'])
            ->where('store_id', $store->id)
            ->where('is_active', true);

        // Filtres
        if ($request->has('low_stock')) {
            $query->whereRaw('quantity <= min_stock_level');
        }

        if ($request->has('out_of_stock')) {
            $query->where('quantity', '<=', 0);
        }

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->whereHas('product', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        $stocks = $query->paginate($request->get('per_page', 50));

        return response()->json([
            'success' => true,
            'data' => $stocks->items(),
            'pagination' => [
                'current_page' => $stocks->currentPage(),
                'last_page' => $stocks->lastPage(),
                'per_page' => $stocks->perPage(),
                'total' => $stocks->total()
            ]
        ]);
    }

    /**
     * Ventes d'un magasin
     */
    public function sales(Request $request, Store $store)
    {
        $user = Auth::user();
        
        if (!$user->can('view', $store)) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé aux ventes de ce magasin'
            ], 403);
        }

        $query = Sale::with(['customer', 'user', 'items.product'])
            ->where('store_id', $store->id);

        // Filtres par date
        if ($request->has('start_date')) {
            $query->whereDate('created_at', '>=', $request->get('start_date'));
        }

        if ($request->has('end_date')) {
            $query->whereDate('created_at', '<=', $request->get('end_date'));
        }

        // Filtre par statut
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filtre par méthode de paiement
        if ($request->has('payment_method')) {
            $query->where('payment_method', $request->get('payment_method'));
        }

        $sales = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $sales->items(),
            'pagination' => [
                'current_page' => $sales->currentPage(),
                'last_page' => $sales->lastPage(),
                'per_page' => $sales->perPage(),
                'total' => $sales->total()
            ]
        ]);
    }

    /**
     * Produits les plus vendus dans un magasin
     */
    public function topProducts(Request $request, Store $store)
    {
        $user = Auth::user();
        
        if (!$user->can('viewReports', $store)) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé aux rapports de ce magasin'
            ], 403);
        }

        $period = $request->get('period', 'month');
        $limit = $request->get('limit', 10);
        
        $topProducts = $this->cacheService->getTopSellingProducts($store->id, $period, $limit);

        return response()->json([
            'success' => true,
            'data' => $topProducts,
            'period' => $period,
            'limit' => $limit
        ]);
    }

    /**
     * Comparaison avec d'autres magasins
     */
    public function comparison(Request $request, Store $store)
    {
        $user = Auth::user();
        
        if (!$user->can('viewReports', $store)) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé aux comparaisons de ce magasin'
            ], 403);
        }

        $period = $request->get('period', 'month');
        $compareWith = $request->get('compare_with', []);
        
        // Vérifier l'accès aux magasins de comparaison
        if (!empty($compareWith)) {
            $accessibleStores = $this->getAccessibleStores($user)->pluck('id')->toArray();
            $compareWith = array_intersect($compareWith, $accessibleStores);
        }

        $dateRange = $this->getDateRange($period);
        $comparison = $this->analyticsService->getPerformanceComparison($dateRange);
        
        // Filtrer pour inclure le magasin principal et ceux de comparaison
        $storeIds = array_merge([$store->id], $compareWith);
        $comparison = array_filter($comparison, function($storeData) use ($storeIds) {
            return in_array($storeData->id, $storeIds);
        });

        return response()->json([
            'success' => true,
            'data' => array_values($comparison),
            'period' => $period,
            'compared_stores' => $compareWith
        ]);
    }

    /**
     * Créer un nouveau magasin (Admin/SuperAdmin seulement)
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->hasAnyRole(['superAdmin', 'admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Permission insuffisante pour créer un magasin'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:100',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $store = Store::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Magasin créé avec succès',
            'data' => $store
        ], 201);
    }

    /**
     * Mettre à jour un magasin
     */
    public function update(Request $request, Store $store)
    {
        $user = Auth::user();
        
        if (!$user->can('manageSettings', $store)) {
            return response()->json([
                'success' => false,
                'message' => 'Permission insuffisante pour modifier ce magasin'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'address' => 'sometimes|required|string|max:500',
            'city' => 'sometimes|required|string|max:100',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $store->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Magasin mis à jour avec succès',
            'data' => $store
        ]);
    }

    /**
     * Obtenir les magasins accessibles pour l'utilisateur
     */
    private function getAccessibleStores($user)
    {
        if ($user->hasRole('superAdmin')) {
            return Store::active()->get();
        }

        if ($user->hasRole('admin')) {
            return Store::active()->get();
        }

        if ($user->hasRole('manager')) {
            return $user->managedStores()->active()->get();
        }

        if ($user->store_id) {
            return Store::where('id', $user->store_id)->active()->get();
        }

        return collect();
    }

    /**
     * Obtenir la plage de dates
     */
    private function getDateRange($period)
    {
        switch ($period) {
            case 'today':
                return [now()->startOfDay(), now()->endOfDay()];
            case 'week':
                return [now()->startOfWeek(), now()->endOfWeek()];
            case 'month':
                return [now()->startOfMonth(), now()->endOfMonth()];
            case 'quarter':
                return [now()->startOfQuarter(), now()->endOfQuarter()];
            case 'year':
                return [now()->startOfYear(), now()->endOfYear()];
            default:
                return [now()->startOfMonth(), now()->endOfMonth()];
        }
    }
}
