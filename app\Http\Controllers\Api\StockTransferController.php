<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\StockTransfer;
use App\Models\Store;
use App\Services\StockTransferService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class StockTransferController extends Controller
{
    protected $transferService;

    public function __construct(StockTransferService $transferService)
    {
        $this->transferService = $transferService;
        $this->middleware('auth:sanctum');
    }

    /**
     * Liste des transferts
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = StockTransfer::with([
            'fromStore', 'toStore', 'requestedBy', 'approvedBy', 
            'shippedBy', 'receivedBy', 'items.product'
        ]);

        // Filtrer selon les permissions
        if (!$user->hasRole('superAdmin')) {
            $accessibleStoreIds = $this->getAccessibleStoreIds($user);
            $query->where(function($q) use ($accessibleStoreIds) {
                $q->whereIn('from_store_id', $accessibleStoreIds)
                  ->orWhereIn('to_store_id', $accessibleStoreIds);
            });
        }

        // Filtres
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->has('from_store_id')) {
            $query->where('from_store_id', $request->get('from_store_id'));
        }

        if ($request->has('to_store_id')) {
            $query->where('to_store_id', $request->get('to_store_id'));
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->get('priority'));
        }

        if ($request->has('start_date')) {
            $query->whereDate('created_at', '>=', $request->get('start_date'));
        }

        if ($request->has('end_date')) {
            $query->whereDate('created_at', '<=', $request->get('end_date'));
        }

        $transfers = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $transfers->items(),
            'pagination' => [
                'current_page' => $transfers->currentPage(),
                'last_page' => $transfers->lastPage(),
                'per_page' => $transfers->perPage(),
                'total' => $transfers->total()
            ]
        ]);
    }

    /**
     * Détails d'un transfert
     */
    public function show(StockTransfer $transfer)
    {
        $user = Auth::user();
        
        if (!$this->canAccessTransfer($user, $transfer)) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé à ce transfert'
            ], 403);
        }

        $transfer->load([
            'fromStore', 'toStore', 'requestedBy', 'approvedBy',
            'shippedBy', 'receivedBy', 'items.product'
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'transfer' => $transfer,
                'statistics' => $transfer->getStatistics(),
                'discrepancies' => $transfer->getDiscrepancies(),
                'can_approve' => $transfer->canBeApproved() && $user->can('transferStock', $transfer->toStore),
                'can_ship' => $transfer->canBeShipped() && $user->can('transferStock', $transfer->fromStore),
                'can_receive' => $transfer->canBeReceived() && $user->can('transferStock', $transfer->toStore),
                'can_cancel' => $transfer->canBeCancelled() && $user->can('transferStock', $transfer->fromStore)
            ]
        ]);
    }

    /**
     * Créer une demande de transfert
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        $validator = Validator::make($request->all(), [
            'from_store_id' => 'required|exists:stores,id',
            'to_store_id' => 'required|exists:stores,id|different:from_store_id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'reason' => 'nullable|string|max:500',
            'priority' => 'nullable|in:low,normal,high,urgent'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        $fromStore = Store::find($request->from_store_id);
        $toStore = Store::find($request->to_store_id);

        if (!$user->can('transferStock', $fromStore)) {
            return response()->json([
                'success' => false,
                'message' => 'Permission insuffisante pour transférer depuis ce magasin'
            ], 403);
        }

        try {
            $transfer = $this->transferService->createTransferRequest(
                $request->from_store_id,
                $request->to_store_id,
                $request->items,
                $request->reason,
                $request->priority ?? 'normal'
            );

            return response()->json([
                'success' => true,
                'message' => 'Demande de transfert créée avec succès',
                'data' => $transfer->load(['fromStore', 'toStore', 'items.product'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Approuver un transfert
     */
    public function approve(Request $request, StockTransfer $transfer)
    {
        $user = Auth::user();
        
        if (!$user->can('transferStock', $transfer->toStore)) {
            return response()->json([
                'success' => false,
                'message' => 'Permission insuffisante pour approuver ce transfert'
            ], 403);
        }

        if (!$transfer->canBeApproved()) {
            return response()->json([
                'success' => false,
                'message' => 'Ce transfert ne peut plus être approuvé'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'approved_quantities' => 'nullable|array',
            'approved_quantities.*' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $transfer = $this->transferService->approveTransfer(
                $transfer,
                $request->approved_quantities ?? []
            );

            return response()->json([
                'success' => true,
                'message' => 'Transfert approuvé avec succès',
                'data' => $transfer->load(['fromStore', 'toStore', 'items.product'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Expédier un transfert
     */
    public function ship(StockTransfer $transfer)
    {
        $user = Auth::user();
        
        if (!$user->can('transferStock', $transfer->fromStore)) {
            return response()->json([
                'success' => false,
                'message' => 'Permission insuffisante pour expédier ce transfert'
            ], 403);
        }

        if (!$transfer->canBeShipped()) {
            return response()->json([
                'success' => false,
                'message' => 'Ce transfert ne peut pas être expédié'
            ], 400);
        }

        try {
            $transfer = $this->transferService->shipTransfer($transfer);

            return response()->json([
                'success' => true,
                'message' => 'Transfert expédié avec succès',
                'data' => $transfer->load(['fromStore', 'toStore', 'items.product'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Recevoir un transfert
     */
    public function receive(Request $request, StockTransfer $transfer)
    {
        $user = Auth::user();
        
        if (!$user->can('transferStock', $transfer->toStore)) {
            return response()->json([
                'success' => false,
                'message' => 'Permission insuffisante pour recevoir ce transfert'
            ], 403);
        }

        if (!$transfer->canBeReceived()) {
            return response()->json([
                'success' => false,
                'message' => 'Ce transfert ne peut pas être reçu'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'received_quantities' => 'nullable|array',
            'received_quantities.*' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $transfer = $this->transferService->receiveTransfer(
                $transfer,
                $request->received_quantities ?? []
            );

            return response()->json([
                'success' => true,
                'message' => 'Transfert reçu avec succès',
                'data' => $transfer->load(['fromStore', 'toStore', 'items.product'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Annuler un transfert
     */
    public function cancel(Request $request, StockTransfer $transfer)
    {
        $user = Auth::user();
        
        if (!$user->can('transferStock', $transfer->fromStore)) {
            return response()->json([
                'success' => false,
                'message' => 'Permission insuffisante pour annuler ce transfert'
            ], 403);
        }

        if (!$transfer->canBeCancelled()) {
            return response()->json([
                'success' => false,
                'message' => 'Ce transfert ne peut plus être annulé'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Raison d\'annulation requise',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $transfer = $this->transferService->cancelTransfer($transfer, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Transfert annulé avec succès',
                'data' => $transfer->load(['fromStore', 'toStore', 'items.product'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Vérifier l'accès à un transfert
     */
    private function canAccessTransfer($user, StockTransfer $transfer)
    {
        if ($user->hasRole('superAdmin')) {
            return true;
        }

        $accessibleStoreIds = $this->getAccessibleStoreIds($user);
        
        return in_array($transfer->from_store_id, $accessibleStoreIds) ||
               in_array($transfer->to_store_id, $accessibleStoreIds);
    }

    /**
     * Obtenir les IDs des magasins accessibles
     */
    private function getAccessibleStoreIds($user)
    {
        if ($user->hasRole('superAdmin')) {
            return Store::active()->pluck('id')->toArray();
        }

        if ($user->hasRole('admin')) {
            return Store::active()->pluck('id')->toArray();
        }

        if ($user->hasRole('manager')) {
            return $user->managedStores()->active()->pluck('id')->toArray();
        }

        if ($user->store_id) {
            return [$user->store_id];
        }

        return [];
    }
}
