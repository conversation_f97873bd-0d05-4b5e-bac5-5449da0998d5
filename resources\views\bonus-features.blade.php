<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <i class="fas fa-star mr-2"></i>{{ __('Fonctionnalités Bonus') }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Introduction -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-8 mb-8 text-white">
                <div class="text-center">
                    <h1 class="text-3xl font-bold mb-4">🎉 Fonctionnalités Bonus Activées !</h1>
                    <p class="text-xl opacity-90">Votre système POS est maintenant équipé de fonctionnalités avancées</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Export Excel -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-file-excel text-green-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Export Excel</h3>
                                <p class="text-sm text-gray-500">Exportez vos données en format Excel</p>
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Rapports de ventes en Excel
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Export des crédits clients
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Rapport de stock détaillé
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Formatage automatique et styles
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <a href="{{ route('reports.index') }}" 
                               class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-download mr-2"></i>Tester l'export
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-bell text-blue-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Notifications Intelligentes</h3>
                                <p class="text-sm text-gray-500">Alertes automatiques pour votre business</p>
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Alertes de stock faible
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Notifications de crédits en retard
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Alertes par email et dans l'app
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Commandes automatisées
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <div class="bg-blue-50 p-3 rounded-lg">
                                <p class="text-sm text-blue-800">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Commande: <code class="bg-blue-200 px-2 py-1 rounded">php artisan pos:check-low-stock</code>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fournisseurs -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-truck text-purple-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Gestion des Fournisseurs</h3>
                                <p class="text-sm text-gray-500">Gérez vos relations fournisseurs</p>
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Base de données fournisseurs
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Liaison produits-fournisseurs
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Gestion des crédits fournisseurs
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Informations fiscales (TVA)
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <a href="{{ route('suppliers.index') }}" 
                               class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                <i class="fas fa-truck mr-2"></i>Voir les fournisseurs
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Promotions -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-tags text-yellow-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Système de Promotions</h3>
                                <p class="text-sm text-gray-500">Créez des offres attractives</p>
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Remises en pourcentage
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Remises fixes en montant
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Offres "Achetez X, obtenez Y"
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Conditions et limites d'usage
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <a href="{{ route('promotions.index') }}" 
                               class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                                <i class="fas fa-tags mr-2"></i>Gérer les promotions
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistiques des nouvelles fonctionnalités -->
            <div class="mt-8 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">
                        <i class="fas fa-chart-pie mr-2"></i>Aperçu des Nouvelles Données
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ \App\Models\Supplier::count() }}</div>
                            <div class="text-sm text-gray-500">Fournisseurs</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600">{{ \App\Models\Promotion::count() }}</div>
                            <div class="text-sm text-gray-500">Promotions</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ \App\Models\Product::whereNotNull('supplier_id')->count() }}</div>
                            <div class="text-sm text-gray-500">Produits liés</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ \App\Models\Promotion::where('is_active', true)->count() }}</div>
                            <div class="text-sm text-gray-500">Promos actives</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="mt-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg p-6 text-white">
                <div class="text-center">
                    <h3 class="text-xl font-semibold mb-4">🚀 Prêt à explorer ?</h3>
                    <div class="flex flex-wrap justify-center gap-4">
                        <a href="{{ route('reports.index') }}" 
                           class="bg-white text-gray-800 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                            Tester les exports Excel
                        </a>
                        <a href="{{ route('suppliers.index') }}" 
                           class="bg-white text-gray-800 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                            Gérer les fournisseurs
                        </a>
                        <a href="{{ route('promotions.index') }}" 
                           class="bg-white text-gray-800 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                            Créer des promotions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
