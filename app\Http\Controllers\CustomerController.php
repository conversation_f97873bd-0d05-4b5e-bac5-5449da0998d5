<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\Credit;
use Illuminate\Support\Facades\Auth;

class CustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Customer::withCount(['sales', 'credits']);

        // Filtrage par rôle
        if (Auth::user()->hasRole(['caissier', 'manager'])) {
            $query->where('created_by', Auth::id());
        }

        // Recherche texte
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%$search%")
                  ->orWhere('email', 'like', "%$search%")
                  ->orWhere('phone', 'like', "%$search%")
                  ->orWhere('address', 'like', "%$search%") ;
            });
        }

        // Filtre statut
        if ($request->filled('status')) {
            switch ($request->input('status')) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'with_debt':
                    $query->where('current_balance', '>', 0);
                    break;
            }
        }

        $customers = $query->latest()->paginate(20)->appends($request->all());

        return view('customers.index', compact('customers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('customers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'due_date' => 'nullable|date|after:today',
        ]);

        $data = $request->all();
        $data['created_by'] = Auth::id();
        
        Customer::create($data);

        return redirect()->route('customers.index')
            ->with('success', 'Client créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Customer $customer)
    {
        $customer->load(['sales.saleItems', 'credits.payments']);

        // Statistiques du client
        $totalSales = $customer->sales->sum('total_amount');
        $totalCredits = $customer->credits->sum('total_amount');
        $totalPaid = $customer->credits->sum('amount_paid');
        $activeCredits = $customer->credits->where('status', 'active');

        return view('customers.show', compact(
            'customer',
            'totalSales',
            'totalCredits',
            'totalPaid',
            'activeCredits'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Customer $customer)
    {
        return view('customers.edit', compact('customer'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email,' . $customer->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'due_date' => 'nullable|date|after:today',
        ]);

        $customer->update($request->all());

        return redirect()->route('customers.index')
            ->with('success', 'Client mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Customer $customer)
    {
        // Vérifier s'il y a des crédits actifs
        if ($customer->credits()->where('status', 'active')->exists()) {
            return back()->with('error', 'Impossible de supprimer un client avec des crédits actifs.');
        }

        $customer->delete();

        return redirect()->route('customers.index')
            ->with('success', 'Client supprimé avec succès.');
    }

    /**
     * Show customer credits
     */
    public function credits(Customer $customer)
    {
        $credits = $customer->credits()
            ->with(['sale', 'payments'])
            ->latest()
            ->paginate(10);

        return view('customers.credits', compact('customer', 'credits'));
    }
}
