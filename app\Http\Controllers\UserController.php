<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $query = User::with('roles')->latest();

        // Masquer les SuperAdmin pour les autres rôles
        $currentUser = Auth::user();
        $currentUserRoles = $currentUser->roles->pluck('name')->toArray();

        if (!in_array('superAdmin', $currentUserRoles)) {
            // Si l'utilisateur connecté n'est pas SuperAdmin, masquer les SuperAdmin
            $query->whereDoesntHave('roles', function($roleQuery) {
                $roleQuery->where('name', 'superAdmin');
            });
        }

        $users = $query->paginate(20);

        // Statistiques (adaptées selon le rôle)
        $statsQuery = User::query();
        if (!in_array('superAdmin', $currentUserRoles)) {
            $statsQuery->whereDoesntHave('roles', function($roleQuery) {
                $roleQuery->where('name', 'superAdmin');
            });
        }

        $totalUsers = $statsQuery->count();
        $activeUsers = $statsQuery->where('is_active', true)->count();
        $inactiveUsers = $statsQuery->where('is_active', false)->count();

        // Répartition par rôles (exclure SuperAdmin si nécessaire)
        $roleStatsQuery = Role::withCount('users');
        if (!in_array('superAdmin', $currentUserRoles)) {
            $roleStatsQuery->where('name', '!=', 'superAdmin');
        }
        $roleStats = $roleStatsQuery->get();

        return view('users.index', compact('users', 'totalUsers', 'activeUsers', 'inactiveUsers', 'roleStats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Filtrer les rôles disponibles selon l'utilisateur connecté
        $currentUser = Auth::user();
        $currentUserRoles = $currentUser->roles->pluck('name')->toArray();

        $rolesQuery = Role::query();
        if (!in_array('superAdmin', $currentUserRoles)) {
            $rolesQuery->where('name', '!=', 'superAdmin');
        }
        $roles = $rolesQuery->get();

        return view('users.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|exists:roles,name',
            'is_active' => 'boolean',
        ]);

        // Empêcher l'attribution du rôle SuperAdmin par les non-SuperAdmin
        $currentUser = Auth::user();
        $currentUserRoles = $currentUser->roles->pluck('name')->toArray();

        if (!in_array('superAdmin', $currentUserRoles) && $validated['role'] === 'superAdmin') {
            return redirect()->back()
                ->with('error', 'Vous n\'avez pas les permissions pour attribuer ce rôle.')
                ->withInput();
        }

        $validated['password'] = Hash::make($validated['password']);
        $validated['is_active'] = $request->has('is_active');

        $user = User::create($validated);
        $user->assignRole($validated['role']);

        return redirect()->route('users.index')
            ->with('success', 'Utilisateur créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        // Empêcher l'accès aux SuperAdmin pour les autres rôles
        $currentUser = Auth::user();
        $currentUserRoles = $currentUser->roles->pluck('name')->toArray();
        $targetUserRoles = $user->roles->pluck('name')->toArray();

        if (!in_array('superAdmin', $currentUserRoles) && in_array('superAdmin', $targetUserRoles)) {
            abort(404, 'Utilisateur non trouvé.');
        }

        $user->load(['roles', 'sales' => function($query) {
            $query->latest()->take(10);
        }]);

        // Statistiques de l'utilisateur
        $totalSales = $user->sales()->count();
        $totalSalesAmount = $user->sales()->sum('total_amount');
        $todaySales = $user->sales()->whereDate('created_at', today())->count();
        $todaySalesAmount = $user->sales()->whereDate('created_at', today())->sum('total_amount');

        return view('users.show', compact('user', 'totalSales', 'totalSalesAmount', 'todaySales', 'todaySalesAmount'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        // Empêcher l'accès aux SuperAdmin pour les autres rôles
        $currentUser = Auth::user();
        $currentUserRoles = $currentUser->roles->pluck('name')->toArray();
        $targetUserRoles = $user->roles->pluck('name')->toArray();

        if (!in_array('superAdmin', $currentUserRoles) && in_array('superAdmin', $targetUserRoles)) {
            abort(404, 'Utilisateur non trouvé.');
        }

        // Filtrer les rôles disponibles selon l'utilisateur connecté
        $rolesQuery = Role::query();
        if (!in_array('superAdmin', $currentUserRoles)) {
            $rolesQuery->where('name', '!=', 'superAdmin');
        }
        $roles = $rolesQuery->get();

        return view('users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {


        // Empêcher l'accès aux SuperAdmin pour les autres rôles
        $currentUser = Auth::user();
        $currentUserRoles = $currentUser->roles->pluck('name')->toArray();
        $targetUserRoles = $user->roles->pluck('name')->toArray();

        if (!in_array('superAdmin', $currentUserRoles) && in_array('superAdmin', $targetUserRoles)) {
            abort(404, 'Utilisateur non trouvé.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|exists:roles,name',
            'is_active' => 'boolean',
        ]);

        // Empêcher l'attribution du rôle SuperAdmin par les non-SuperAdmin
        if (!in_array('superAdmin', $currentUserRoles) && $validated['role'] === 'superAdmin') {
            return redirect()->back()
                ->with('error', 'Vous n\'avez pas les permissions pour attribuer ce rôle.');
        }

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $validated['is_active'] = $request->has('is_active');

        $user->update($validated);
        $user->syncRoles([$validated['role']]);

        return redirect()->route('users.index')
            ->with('success', 'Utilisateur mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // Empêcher l'accès aux SuperAdmin pour les autres rôles
        $currentUser = Auth::user();
        $currentUserRoles = $currentUser->roles->pluck('name')->toArray();
        $targetUserRoles = $user->roles->pluck('name')->toArray();

        if (!in_array('superAdmin', $currentUserRoles) && in_array('superAdmin', $targetUserRoles)) {
            abort(404, 'Utilisateur non trouvé.');
        }

        // Empêcher la suppression de son propre compte
        if ($user->id === Auth::id()) {
            return redirect()->route('users.index')
                ->with('error', 'Vous ne pouvez pas supprimer votre propre compte.');
        }

        // Vérifier s'il y a des ventes liées
        if ($user->sales()->count() > 0) {
            return redirect()->route('users.index')
                ->with('error', 'Impossible de supprimer cet utilisateur car il a des ventes associées.');
        }

        $user->delete();

        return redirect()->route('users.index')
            ->with('success', 'Utilisateur supprimé avec succès.');
    }

    /**
     * Toggle user status
     */
    public function toggle(User $user)
    {
        // Empêcher l'accès aux SuperAdmin pour les autres rôles
        $currentUser = Auth::user();
        $currentUserRoles = $currentUser->roles->pluck('name')->toArray();
        $targetUserRoles = $user->roles->pluck('name')->toArray();

        if (!in_array('superAdmin', $currentUserRoles) && in_array('superAdmin', $targetUserRoles)) {
            abort(404, 'Utilisateur non trouvé.');
        }

        // Empêcher la désactivation de son propre compte
        if ($user->id === Auth::id()) {
            return redirect()->back()
                ->with('error', 'Vous ne pouvez pas désactiver votre propre compte.');
        }

        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'activé' : 'désactivé';

        return redirect()->back()
            ->with('success', "Utilisateur {$status} avec succès.");
    }

    /**
     * Reset user password
     */
    public function resetPassword(User $user)
    {
        // Empêcher l'accès aux SuperAdmin pour les autres rôles
        $currentUser = Auth::user();
        $currentUserRoles = $currentUser->roles->pluck('name')->toArray();
        $targetUserRoles = $user->roles->pluck('name')->toArray();

        if (!in_array('superAdmin', $currentUserRoles) && in_array('superAdmin', $targetUserRoles)) {
            abort(404, 'Utilisateur non trouvé.');
        }

        $newPassword = 'password123';
        $user->update(['password' => Hash::make($newPassword)]);

        return redirect()->back()
            ->with('success', "Mot de passe réinitialisé. Nouveau mot de passe : {$newPassword}");
    }
}
