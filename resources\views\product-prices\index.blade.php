<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-tags mr-3 text-blue-600"></i>Gestion des Prix
                    @if($store)
                        <span class="text-lg text-blue-600 font-medium">- {{ $store->name }}</span>
                    @endif
                </h2>
                <p class="text-sm text-gray-600 mt-1">Gérez les prix des produits par magasin</p>
            </div>
            <div class="flex space-x-2">
                <x-button href="{{ route('product-prices.create', ['store_id' => request('store_id')]) }}" 
                         variant="primary" icon="fas fa-plus">
                    Nouveau Prix
                </x-button>
                @if($store)
                    <x-button href="{{ route('stores.show', $store) }}" variant="outline" icon="fas fa-store">
                        Retour au magasin
                    </x-button>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Filtres et recherche -->
            <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                <form method="GET" action="{{ route('product-prices.index') }}" class="space-y-4">
                    @if(request('store_id'))
                        <input type="hidden" name="store_id" value="{{ request('store_id') }}">
                    @endif
                    
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Rechercher un produit..."
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        @if(!request('store_id'))
                            <div>
                                <select name="store_id" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Tous les magasins</option>
                                    @foreach($stores as $storeOption)
                                        <option value="{{ $storeOption->id }}" {{ request('store_id') == $storeOption->id ? 'selected' : '' }}>
                                            {{ $storeOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        @endif
                        
                        <div>
                            <select name="status" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Tous les statuts</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Prix actifs</option>
                                <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Prix expirés</option>
                                <option value="future" {{ request('status') === 'future' ? 'selected' : '' }}>Prix futurs</option>
                            </select>
                        </div>
                        
                        <div class="flex space-x-2">
                            <x-button type="submit" variant="secondary" icon="fas fa-search" class="flex-1">
                                Rechercher
                            </x-button>
                            @if(request()->hasAny(['search', 'store_id', 'status']))
                                <x-button href="{{ route('product-prices.index') }}" variant="outline" icon="fas fa-times">
                                    Reset
                                </x-button>
                            @endif
                        </div>
                    </div>
                </form>
            </div>

            <!-- Actions en lot -->
            @if($productPrices->count() > 0)
                <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Actions en lot</h3>
                        <div class="flex space-x-2">
                            <x-button onclick="openBulkUpdateModal()" variant="secondary" size="sm" icon="fas fa-edit">
                                Mise à jour en lot
                            </x-button>
                            <x-button onclick="exportPrices()" variant="outline" size="sm" icon="fas fa-download">
                                Exporter
                            </x-button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Liste des prix -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                @if($productPrices->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" id="selectAll" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Produit
                                    </th>
                                    @if(!request('store_id'))
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Magasin
                                        </th>
                                    @endif
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Prix de revient
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Prix de vente
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Marge
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Période
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Statut
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($productPrices as $price)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" name="selected_prices[]" value="{{ $price->id }}" 
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded price-checkbox">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    @if($price->product->image)
                                                        <img class="h-10 w-10 rounded-full object-cover" src="{{ $price->product->image }}" alt="">
                                                    @else
                                                        <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                            <i class="fas fa-box text-gray-600"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ $price->product->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $price->product->barcode }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        @if(!request('store_id'))
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $price->store->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $price->store->code }}</div>
                                            </td>
                                        @endif
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                            {{ number_format($price->cost_price, 0, ',', ' ') }} FCFA
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ number_format($price->selling_price, 0, ',', ' ') }} FCFA
                                            </div>
                                            @if($price->wholesale_price)
                                                <div class="text-xs text-gray-500">
                                                    Gros: {{ number_format($price->wholesale_price, 0, ',', ' ') }} FCFA
                                                </div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            @php
                                                $margin = $price->cost_price > 0 ? (($price->selling_price - $price->cost_price) / $price->cost_price) * 100 : 0;
                                                $profit = $price->selling_price - $price->cost_price;
                                            @endphp
                                            <div class="text-sm font-medium {{ $profit > 0 ? 'text-green-600' : 'text-red-600' }}">
                                                {{ number_format($margin, 1) }}%
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                {{ $profit > 0 ? '+' : '' }}{{ number_format($profit, 0, ',', ' ') }} FCFA
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                                            <div>Du {{ $price->effective_from->format('d/m/Y') }}</div>
                                            @if($price->effective_until)
                                                <div>Au {{ $price->effective_until->format('d/m/Y') }}</div>
                                            @else
                                                <div class="text-blue-600">Indéfini</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            @if($price->isActive())
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-circle mr-1 text-xs"></i>Actif
                                                </span>
                                            @elseif($price->isFuture())
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <i class="fas fa-clock mr-1 text-xs"></i>Futur
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    <i class="fas fa-times-circle mr-1 text-xs"></i>Expiré
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex items-center justify-end space-x-2">
                                                <x-button href="{{ route('product-prices.show', $price) }}" 
                                                         variant="outline" size="sm" icon="fas fa-eye">
                                                    Voir
                                                </x-button>
                                                <x-button href="{{ route('product-prices.edit', $price) }}" 
                                                         variant="secondary" size="sm" icon="fas fa-edit">
                                                    Modifier
                                                </x-button>
                                                @if(!$price->isActive())
                                                    <form method="POST" action="{{ route('product-prices.destroy', $price) }}" 
                                                          onsubmit="return confirm('Supprimer ce prix ?')" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <x-button type="submit" variant="danger" size="sm" icon="fas fa-trash">
                                                            Supprimer
                                                        </x-button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($productPrices->hasPages())
                        <div class="px-6 py-4 border-t border-gray-200">
                            {{ $productPrices->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-tags text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun prix trouvé</h3>
                        <p class="text-gray-500 mb-6">
                            @if(request()->hasAny(['search', 'store_id', 'status']))
                                Aucun prix ne correspond à vos critères de recherche.
                            @else
                                Commencez par définir les prix de vos produits.
                            @endif
                        </p>
                        @if(!request()->hasAny(['search', 'store_id', 'status']))
                            <x-button href="{{ route('product-prices.create', ['store_id' => request('store_id')]) }}" 
                                     variant="primary" icon="fas fa-plus">
                                Définir le premier prix
                            </x-button>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Statistiques -->
            @if($productPrices->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 bg-blue-100 rounded-full">
                                <i class="fas fa-tags text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Prix</p>
                                <p class="text-2xl font-bold text-gray-900">{{ $productPrices->total() }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 bg-green-100 rounded-full">
                                <i class="fas fa-check-circle text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Prix Actifs</p>
                                <p class="text-2xl font-bold text-gray-900">{{ $activePricesCount }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 bg-yellow-100 rounded-full">
                                <i class="fas fa-percentage text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Marge Moyenne</p>
                                <p class="text-2xl font-bold text-gray-900">{{ number_format($averageMargin, 1) }}%</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 bg-purple-100 rounded-full">
                                <i class="fas fa-coins text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Bénéfice Moyen</p>
                                <p class="text-2xl font-bold text-gray-900">{{ number_format($averageProfit) }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    @push('scripts')
        <script>
            // Gestion de la sélection multiple
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.price-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            function openBulkUpdateModal() {
                // Implémenter le modal de mise à jour en lot
                alert('Fonctionnalité de mise à jour en lot à implémenter');
            }

            function exportPrices() {
                // Implémenter l'export des prix
                const params = new URLSearchParams(window.location.search);
                params.set('export', 'true');
                window.location.href = '{{ route("product-prices.index") }}?' + params.toString();
            }
        </script>
    @endpush
</x-app-layout>
