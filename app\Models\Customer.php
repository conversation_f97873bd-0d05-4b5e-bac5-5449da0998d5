<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Notifications\CreditOverdueAlert;
use Carbon\Carbon;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'type', // 'retail', 'wholesale', 'vip'
        'credit_limit',
        'current_balance',
        'due_date',
        'is_active',
        'created_by',
        'store_id',
        'notes',
        'identification_number', // Numéro de pièce d'identité
    ];

    protected function casts(): array
    {
        return [
            'due_date' => 'date',
            'current_balance' => 'decimal:2',
            'credit_limit' => 'decimal:2',
            'is_active' => 'boolean',
            'deleted_at' => 'datetime',
        ];
    }

    // Relations améliorées
    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function credits()
    {
        return $this->hasMany(Credit::class);
    }

    public function creditPayments()
    {
        return $this->hasManyThrough(CreditPayment::class, Credit::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    // Nouvelles méthodes utilitaires
    public function updateBalance()
    {
        $totalCredits = $this->credits()->sum('amount');
        $totalPayments = $this->creditPayments()->sum('amount');
        $this->current_balance = $totalCredits - $totalPayments;
        $this->save();
    }

    public function canReceiveCredit($amount)
    {
        return ($this->current_balance + $amount) <= $this->credit_limit;
    }

    public function addCredit($amount, $saleId = null, $dueDate = null)
    {
        if (!$this->canReceiveCredit($amount)) {
            throw new \Exception("Le montant dépasse la limite de crédit du client");
        }

        $credit = $this->credits()->create([
            'amount' => $amount,
            'sale_id' => $saleId,
            'due_date' => $dueDate ?? now()->addDays(30),
            'status' => 'pending'
        ]);

        $this->updateBalance();
        return $credit;
    }

    public function makePayment($amount, $paymentMethod = 'cash', $reference = null)
    {
        if ($amount > $this->current_balance) {
            throw new \Exception("Le montant du paiement dépasse le solde actuel");
        }

        // Payer les crédits du plus ancien au plus récent
        $unpaidCredits = $this->credits()
            ->where('status', 'pending')
            ->orderBy('created_at')
            ->get();

        $remainingAmount = $amount;

        foreach ($unpaidCredits as $credit) {
            if ($remainingAmount <= 0) break;

            $paymentAmount = min($remainingAmount, $credit->remaining_amount);
            
            $credit->payments()->create([
                'amount' => $paymentAmount,
                'payment_method' => $paymentMethod,
                'reference' => $reference,
                'user_id' => auth()->id()
            ]);

            $remainingAmount -= $paymentAmount;
            $credit->updateStatus();
        }

        $this->updateBalance();
    }

    public function getPurchaseHistory($startDate = null, $endDate = null)
    {
        $query = $this->sales()->with('items.product');

        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }

        return $query->get();
    }

    public function checkCreditStatus()
    {
        if ($this->is_overdue) {
            $this->notify(new CreditOverdueAlert($this));
            return false;
        }

        if ($this->days_until_due <= 7 && $this->current_balance > 0) {
            $this->notify(new CreditDueSoonAlert($this));
        }

        return true;
    }

    // Scopes améliorés
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeWithDebt($query)
    {
        return $query->where('current_balance', '>', 0);
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->where('current_balance', '>', 0);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Accesseurs améliorés
    public function getIsOverdueAttribute()
    {
        return $this->due_date && $this->due_date->isPast() && $this->current_balance > 0;
    }

    public function getAvailableCreditAttribute()
    {
        return $this->credit_limit - $this->current_balance;
    }

    public function getCreditStatusAttribute()
    {
        if ($this->current_balance <= 0) {
            return 'paid';
        }
        if ($this->is_overdue) {
            return 'overdue';
        }
        if ($this->days_until_due <= 7) {
            return 'due_soon';
        }
        return 'active';
    }
}
