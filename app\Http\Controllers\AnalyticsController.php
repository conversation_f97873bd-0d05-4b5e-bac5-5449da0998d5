<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\MultiStoreAnalyticsService;
use App\Services\CacheService;
use App\Models\Store;
use Illuminate\Support\Facades\Auth;

class AnalyticsController extends Controller
{
    protected $analyticsService;
    protected $cacheService;

    public function __construct(
        MultiStoreAnalyticsService $analyticsService,
        CacheService $cacheService
    ) {
        $this->analyticsService = $analyticsService;
        $this->cacheService = $cacheService;
        $this->middleware('auth');
    }

    /**
     * Tableau de bord principal des analytics
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');
        
        // Vérifier les permissions
        if (!$user->hasAnyRole(['superAdmin', 'admin', 'manager'])) {
            return redirect()->route('dashboard')
                ->with('error', 'Accès non autorisé aux analytics.');
        }

        // Obtenir les magasins accessibles
        $accessibleStores = $this->getAccessibleStores($user);
        
        if ($accessibleStores->isEmpty()) {
            return redirect()->route('dashboard')
                ->with('error', 'Aucun magasin accessible pour les analytics.');
        }

        // Données du tableau de bord
        $dashboardData = $this->analyticsService->getStoreComparisonDashboard($period);
        
        // Filtrer les données selon les magasins accessibles
        if (!$user->hasRole('superAdmin')) {
            $storeIds = $accessibleStores->pluck('id')->toArray();
            $dashboardData = $this->filterDataByStores($dashboardData, $storeIds);
        }

        return view('analytics.index', compact(
            'dashboardData',
            'accessibleStores',
            'period'
        ));
    }

    /**
     * Comparaison détaillée des magasins
     */
    public function storeComparison(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');
        $storeIds = $request->get('stores', []);
        
        if (!$user->hasAnyRole(['superAdmin', 'admin', 'manager'])) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        $accessibleStores = $this->getAccessibleStores($user);
        
        // Filtrer les magasins demandés selon les permissions
        if (!empty($storeIds)) {
            $allowedStoreIds = $accessibleStores->pluck('id')->toArray();
            $storeIds = array_intersect($storeIds, $allowedStoreIds);
        } else {
            $storeIds = $accessibleStores->pluck('id')->toArray();
        }

        $dateRange = $this->getDateRange($period);
        $comparison = $this->analyticsService->getPerformanceComparison($dateRange);
        
        // Filtrer par magasins sélectionnés
        $comparison = array_filter($comparison, function($store) use ($storeIds) {
            return in_array($store->id, $storeIds);
        });

        return response()->json([
            'comparison' => array_values($comparison),
            'period' => $period,
            'stores' => $storeIds
        ]);
    }

    /**
     * Analyse des profits
     */
    public function profitAnalysis(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');
        
        if (!$user->can('viewFinancials', Store::class)) {
            return response()->json(['error' => 'Accès non autorisé aux données financières'], 403);
        }

        $dateRange = $this->getDateRange($period);
        $profitData = $this->analyticsService->getProfitAnalysis($dateRange);
        
        // Filtrer selon les permissions
        if (!$user->hasRole('superAdmin')) {
            $accessibleStoreIds = $this->getAccessibleStores($user)->pluck('id')->toArray();
            $profitData = array_filter($profitData, function($store) use ($accessibleStoreIds) {
                return in_array($store->id, $accessibleStoreIds);
            });
        }

        return response()->json([
            'profit_data' => array_values($profitData),
            'period' => $period
        ]);
    }

    /**
     * Tendances de revenus
     */
    public function revenueTrends(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');
        $storeId = $request->get('store_id');
        
        if (!$user->hasAnyRole(['superAdmin', 'admin', 'manager'])) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Vérifier l'accès au magasin spécifique
        if ($storeId) {
            $store = Store::find($storeId);
            if (!$store || !$user->can('viewReports', $store)) {
                return response()->json(['error' => 'Accès non autorisé à ce magasin'], 403);
            }
        }

        $dateRange = $this->getDateRange($period);
        $trends = $this->analyticsService->getRevenueTrends($dateRange);
        
        // Filtrer par magasin si spécifié
        if ($storeId) {
            $trends = array_filter($trends, function($key) use ($storeId) {
                return $key == $storeId;
            }, ARRAY_FILTER_USE_KEY);
        }

        // Filtrer selon les permissions
        if (!$user->hasRole('superAdmin')) {
            $accessibleStoreIds = $this->getAccessibleStores($user)->pluck('id')->toArray();
            $trends = array_filter($trends, function($key) use ($accessibleStoreIds) {
                return in_array($key, $accessibleStoreIds);
            }, ARRAY_FILTER_USE_KEY);
        }

        return response()->json([
            'trends' => $trends,
            'period' => $period
        ]);
    }

    /**
     * Analytics des stocks
     */
    public function stockAnalytics(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->hasAnyRole(['superAdmin', 'admin', 'manager'])) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        $stockEfficiency = $this->analyticsService->getStockEfficiency();
        
        // Filtrer selon les permissions
        if (!$user->hasRole('superAdmin')) {
            $accessibleStoreIds = $this->getAccessibleStores($user)->pluck('id')->toArray();
            $stockEfficiency = array_filter($stockEfficiency, function($store) use ($accessibleStoreIds) {
                return in_array($store->id, $accessibleStoreIds);
            });
        }

        return response()->json([
            'stock_efficiency' => array_values($stockEfficiency)
        ]);
    }

    /**
     * Analytics des clients
     */
    public function customerAnalytics(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');
        
        if (!$user->hasAnyRole(['superAdmin', 'admin', 'manager'])) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        $dateRange = $this->getDateRange($period);
        $customerData = $this->analyticsService->getCustomerAnalytics($dateRange);
        
        // Filtrer selon les permissions
        if (!$user->hasRole('superAdmin')) {
            $accessibleStoreIds = $this->getAccessibleStores($user)->pluck('id')->toArray();
            $customerData = array_filter($customerData, function($store) use ($accessibleStoreIds) {
                return in_array($store->id, $accessibleStoreIds);
            });
        }

        return response()->json([
            'customer_data' => array_values($customerData),
            'period' => $period
        ]);
    }

    /**
     * Performance des produits
     */
    public function productPerformance(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');
        $storeId = $request->get('store_id');
        $limit = $request->get('limit', 20);
        
        if (!$user->hasAnyRole(['superAdmin', 'admin', 'manager'])) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        $dateRange = $this->getDateRange($period);
        $productData = $this->analyticsService->getProductPerformance($dateRange);
        
        // Filtrer par magasin si spécifié
        if ($storeId) {
            $store = Store::find($storeId);
            if (!$store || !$user->can('viewReports', $store)) {
                return response()->json(['error' => 'Accès non autorisé à ce magasin'], 403);
            }
            
            $productData = array_filter($productData, function($product) use ($storeId) {
                return $product->store_id == $storeId;
            });
        }

        // Filtrer selon les permissions
        if (!$user->hasRole('superAdmin')) {
            $accessibleStoreIds = $this->getAccessibleStores($user)->pluck('id')->toArray();
            $productData = array_filter($productData, function($product) use ($accessibleStoreIds) {
                return in_array($product->store_id, $accessibleStoreIds);
            });
        }

        // Limiter les résultats
        $productData = array_slice(array_values($productData), 0, $limit);

        return response()->json([
            'product_data' => $productData,
            'period' => $period
        ]);
    }

    /**
     * Exporter les données analytics
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        $type = $request->get('type', 'comparison');
        $period = $request->get('period', 'month');
        $format = $request->get('format', 'excel');
        
        if (!$user->hasAnyRole(['superAdmin', 'admin', 'manager'])) {
            return response()->json(['error' => 'Accès non autorisé'], 403);
        }

        // Implémenter l'export selon le type et format demandé
        // Cette méthode peut être étendue pour supporter différents formats
        
        return response()->json([
            'message' => 'Export en cours de développement',
            'type' => $type,
            'period' => $period,
            'format' => $format
        ]);
    }

    /**
     * Obtenir les magasins accessibles pour l'utilisateur
     */
    private function getAccessibleStores($user)
    {
        if ($user->hasRole('superAdmin')) {
            return Store::active()->get();
        }

        if ($user->hasRole('admin')) {
            return Store::active()->get();
        }

        if ($user->hasRole('manager')) {
            return $user->managedStores()->active()->get();
        }

        if ($user->store_id) {
            return Store::where('id', $user->store_id)->active()->get();
        }

        return collect();
    }

    /**
     * Filtrer les données par magasins
     */
    private function filterDataByStores($data, $storeIds)
    {
        // Implémenter le filtrage selon la structure des données
        return $data;
    }

    /**
     * Obtenir la plage de dates
     */
    private function getDateRange($period)
    {
        switch ($period) {
            case 'today':
                return [now()->startOfDay(), now()->endOfDay()];
            case 'week':
                return [now()->startOfWeek(), now()->endOfWeek()];
            case 'month':
                return [now()->startOfMonth(), now()->endOfMonth()];
            case 'quarter':
                return [now()->startOfQuarter(), now()->endOfQuarter()];
            case 'year':
                return [now()->startOfYear(), now()->endOfYear()];
            default:
                return [now()->startOfMonth(), now()->endOfMonth()];
        }
    }
}
