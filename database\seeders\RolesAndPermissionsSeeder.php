<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Réinitialiser les caches des rôles et permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Créer les permissions
        Permission::create(['name' => 'manage_all_stores']);
        Permission::create(['name' => 'manage_store']);
        Permission::create(['name' => 'manage_sales']);
        Permission::create(['name' => 'manage_inventory']);
        Permission::create(['name' => 'manage_customers']);
        Permission::create(['name' => 'manage_suppliers']);
        Permission::create(['name' => 'manage_users']);
        Permission::create(['name' => 'view_reports']);

        // Créer les rôles et assigner les permissions
        $role = Role::create(['name' => 'super-admin']);
        $role->givePermissionTo(Permission::all());

        $role = Role::create(['name' => 'store-manager']);
        $role->givePermissionTo([
            'manage_store',
            'manage_sales',
            'manage_inventory',
            'manage_customers',
            'manage_suppliers',
            'view_reports'
        ]);

        $role = Role::create(['name' => 'cashier']);
        $role->givePermissionTo([
            'manage_sales',
            'manage_customers'
        ]);
    }
}
