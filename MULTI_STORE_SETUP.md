# Guide de Migration vers le Système Multi-Magasins avec Calcul des Bénéfices

## Vue d'ensemble

Ce guide explique comment migrer votre système POS Laravel existant vers un système multi-magasins avec calcul automatique des bénéfices par vente.

## Nouvelles Fonctionnalités

### 1. Support Multi-Magasins
- **Table `stores`** : Gestion des informations des magasins
- **Table `store_stocks`** : Gestion des stocks par produit et par magasin
- **Table `product_prices`** : Gestion des prix par produit et par magasin
- **Relations** : Tous les utilisateurs, ventes, clients sont liés à un magasin

### 2. Calcul des Bénéfices
- **Prix de coût** : Stocké dans `product_prices.cost_price`
- **Prix de vente** : Stocké dans `product_prices.selling_price`
- **Bénéfice par article** : Calculé automatiquement dans `sale_items.profit_amount`
- **Marge bénéficiaire** : Calculée automatiquement dans `sale_items.profit_margin`

## Structure des Nouvelles Tables

### Table `stores`
```sql
- id (Primary Key)
- name (Nom du magasin)
- code (Code unique du magasin)
- description
- address, city, phone, email
- manager_name (Nom du gérant)
- default_tax_rate (Taux de taxe par défaut)
- currency (Devise, défaut: XOF)
- is_active (Statut actif/inactif)
- settings (JSON - paramètres spécifiques)
```

### Table `product_prices`
```sql
- id (Primary Key)
- product_id (Foreign Key vers products)
- store_id (Foreign Key vers stores)
- cost_price (Prix d'achat/coût)
- selling_price (Prix de vente)
- min_selling_price (Prix minimum autorisé)
- wholesale_price (Prix de gros)
- tax_rate (Taux de taxe spécifique)
- effective_from/effective_until (Période de validité)
- is_active
```

### Table `store_stocks`
```sql
- id (Primary Key)
- product_id (Foreign Key vers products)
- store_id (Foreign Key vers stores)
- quantity (Quantité en stock)
- min_stock_level (Seuil d'alerte)
- max_stock_level (Stock maximum)
- reorder_point (Point de réapprovisionnement)
- location (Emplacement dans le magasin)
- last_counted_at (Dernier inventaire)
```

## Instructions de Migration

### Étape 1 : Sauvegarde
```bash
# Sauvegardez votre base de données avant la migration
mysqldump -u root -p pos_credit_client > backup_before_multistore.sql
```

### Étape 2 : Exécution des Migrations
```bash
# Exécuter les nouvelles migrations
php artisan migrate

# Les migrations suivantes seront exécutées :
# 1. 2025_07_30_000001_create_stores_table.php
# 2. 2025_07_30_000002_create_product_prices_table.php
# 3. 2025_07_30_000003_create_store_stocks_table.php
# 4. 2025_07_30_000004_add_store_support_to_existing_tables.php
# 5. 2025_07_30_000005_migrate_existing_data_to_multistore.php
# 6. 2025_07_30_000006_remove_stock_columns_from_products.php
```

### Étape 3 : Peuplement des Données de Test
```bash
# Exécuter les seeders pour créer des données de test
php artisan db:seed --class=StoreSeeder
php artisan db:seed --class=UserStoreAssignmentSeeder
php artisan db:seed --class=ProductPriceSeeder
php artisan db:seed --class=StoreStockSeeder

# Ou exécuter tous les seeders
php artisan db:seed
```

## Utilisation du Nouveau Système

### 1. Gestion des Magasins
```php
// Créer un nouveau magasin
$store = Store::create([
    'name' => 'Nouveau Magasin',
    'code' => 'NEW_STORE',
    'address' => 'Adresse du magasin',
    'city' => 'Ville',
    'phone' => '+226 XX XX XX XX',
    'email' => '<EMAIL>',
    'manager_name' => 'Nom du gérant',
    'default_tax_rate' => 18.00,
    'currency' => 'XOF',
    'is_active' => true
]);
```

### 2. Gestion des Prix par Magasin
```php
// Définir le prix d'un produit pour un magasin
ProductPrice::create([
    'product_id' => $product->id,
    'store_id' => $store->id,
    'cost_price' => 1000.00,      // Prix d'achat
    'selling_price' => 1500.00,   // Prix de vente
    'min_selling_price' => 1200.00, // Prix minimum
    'wholesale_price' => 1300.00, // Prix de gros
    'tax_rate' => 18.00,
    'is_active' => true,
    'effective_from' => now()
]);

// Récupérer le prix actuel d'un produit pour un magasin
$price = ProductPrice::getCurrentPrice($productId, $storeId);
```

### 3. Gestion des Stocks par Magasin
```php
// Récupérer le stock d'un produit dans un magasin
$stock = StoreStock::where('product_id', $productId)
                  ->where('store_id', $storeId)
                  ->first();

// Ajouter du stock
$stock->addStock(50, 'Réapprovisionnement');

// Retirer du stock
$stock->removeStock(10, 'Vente');

// Ajuster le stock
$stock->adjustStock(100, 'Inventaire');
```

### 4. Calcul des Bénéfices
```php
// Dans le contrôleur POS, lors de la création d'une vente
foreach ($request->items as $item) {
    $price = ProductPrice::getCurrentPrice($item['product_id'], $storeId);
    
    $saleItem = SaleItem::create([
        'sale_id' => $sale->id,
        'product_id' => $item['product_id'],
        'product_name' => $product->name,
        'unit_price' => $item['unit_price'],
        'cost_price' => $price->cost_price, // Prix de coût au moment de la vente
        'quantity' => $item['quantity'],
        'total_price' => $item['unit_price'] * $item['quantity']
    ]);
    
    // Calculer automatiquement le bénéfice
    $saleItem->calculateProfit();
    $saleItem->save();
}

// Récupérer les bénéfices d'une vente
$sale = Sale::with('saleItems')->find($saleId);
$totalProfit = $sale->total_profit;
$averageMargin = $sale->average_profit_margin;
```

## Modifications des Contrôleurs

### POSController
- Ajouter `store_id` lors de la création des ventes
- Utiliser `ProductPrice::getCurrentPrice()` pour récupérer les prix
- Calculer automatiquement les bénéfices

### ProductController
- Gérer les prix par magasin
- Afficher les stocks par magasin

### DashboardController
- Filtrer les données par magasin de l'utilisateur connecté
- Afficher les statistiques de bénéfices

## Points d'Attention

1. **Migration des Données** : La migration crée automatiquement un "Magasin Principal" et y associe toutes les données existantes.

2. **Prix à Définir** : Après la migration, vous devrez définir manuellement les prix de coût et de vente pour chaque produit dans chaque magasin.

3. **Permissions** : Les super admins peuvent voir tous les magasins, les autres utilisateurs ne voient que leur magasin assigné.

4. **Calcul des Bénéfices** : Les bénéfices ne seront calculés que pour les nouvelles ventes après la migration.

## Prochaines Étapes

1. Mettre à jour les vues pour afficher les informations par magasin
2. Ajouter une interface de gestion des magasins
3. Créer des rapports de bénéfices par magasin
4. Implémenter les transferts de stock entre magasins
5. Ajouter la gestion des promotions par magasin
