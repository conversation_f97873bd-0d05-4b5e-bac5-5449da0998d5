<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRequest;
use App\Models\Store;
use App\Models\Product;
use App\Models\User;
use App\Services\StoreService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StoreController extends Controller
{
    protected $storeService;

    public function __construct(StoreService $storeService)
    {
        $this->storeService = $storeService;
        $this->middleware('auth');
        $this->middleware('role:admin,super-admin')->except(['index', 'show', 'select']);
    }

    public function index()
    {
        $stores = Store::with('manager')
            ->withCount(['sales', 'products'])
            ->when(!auth()->user()->hasRole('super-admin'), function($query) {
                return $query->whereHas('users', function($q) {
                    $q->where('user_id', auth()->id());
                });
            })
            ->get();

        return view('stores.index', compact('stores'));
    }

    public function create()
    {
        $managers = User::role('manager')->get();
        return view('stores.create', compact('managers'));
    }

    public function store(StoreRequest $request)
    {
        try {
            DB::beginTransaction();

            $store = $this->storeService->createStore($request->validated());

            DB::commit();

            return redirect()->route('stores.show', $store)
                ->with('success', 'Magasin créé avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', $e->getMessage())->withInput();
        }
    }

    public function show(Store $store)
    {
        $this->authorize('view', $store);

        $store->load(['manager', 'users']);

        $statistics = $this->storeService->getStoreStatistics($store);
        $lowStockProducts = $this->storeService->getLowStockProducts($store);
        $topProducts = $this->storeService->getTopSellingProducts($store);
        $recentSales = $store->sales()->with('customer')->latest()->limit(5)->get();

        return view('stores.show', compact(
            'store',
            'statistics',
            'lowStockProducts',
            'topProducts',
            'recentSales'
        ));
    }

    public function edit(Store $store)
    {
        $this->authorize('update', $store);

        $managers = User::role('manager')->get();
        return view('stores.edit', compact('store', 'managers'));
    }

    public function update(StoreRequest $request, Store $store)
    {
        $this->authorize('update', $store);

        try {
            DB::beginTransaction();

            $store = $this->storeService->updateStore($store, $request->validated());

            DB::commit();

            return redirect()->route('stores.show', $store)
                ->with('success', 'Magasin mis à jour avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', $e->getMessage())->withInput();
        }
    }

    public function destroy(Store $store)
    {
        $this->authorize('delete', $store);

        if ($store->sales()->exists()) {
            return back()->with('error', 'Impossible de supprimer un magasin ayant des ventes');
        }

        try {
            DB::beginTransaction();

            $this->storeService->deleteStore($store);

            DB::commit();

            return redirect()->route('stores.index')
                ->with('success', 'Magasin supprimé avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', $e->getMessage());
        }
    }

    public function select(Store $store)
    {
        $this->authorize('access', $store);

        session(['current_store_id' => $store->id]);

        return redirect()->route('dashboard')
            ->with('success', "Magasin {$store->name} sélectionné");
    }

    public function inventory(Store $store)
    {
        $this->authorize('view', $store);

        $products = Product::whereHas('storeStocks', function($query) use ($store) {
            $query->where('store_id', $store->id);
        })->with(['storeStocks' => function($query) use ($store) {
            $query->where('store_id', $store->id);
        }, 'productPrices' => function($query) use ($store) {
            $query->where('store_id', $store->id)
                  ->where('is_active', true);
        }])->get();

        return view('stores.inventory', compact('store', 'products'));
    }

    public function adjustStock(Request $request, Store $store)
    {
        $this->authorize('update', $store);

        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|numeric',
            'operation' => 'required|in:add,subtract,set',
            'reason' => 'required|string'
        ]);

        try {
            DB::beginTransaction();

            $this->storeService->adjustStock(
                $store,
                $request->product_id,
                $request->quantity,
                $request->operation,
                $request->reason
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Stock ajusté avec succès'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }
}
