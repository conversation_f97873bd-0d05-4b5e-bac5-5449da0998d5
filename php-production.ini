# ===========================================
# CONFIGURATION PHP POUR PRODUCTION
# POS Crédit Client
# ===========================================

# Ce fichier doit être placé dans /etc/php/8.2/fpm/conf.d/99-pos-production.ini
# ou ajouté à votre php.ini principal

# ===========================================
# SÉCURITÉ
# ===========================================

# Désactiver l'affichage des erreurs
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php/error.log

# Masquer la version PHP
expose_php = Off

# Désactiver les fonctions dangereuses
disable_functions = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source

# Limiter l'inclusion de fichiers
allow_url_fopen = Off
allow_url_include = Off

# ===========================================
# PERFORMANCE
# ===========================================

# Mémoire
memory_limit = 256M
max_execution_time = 300
max_input_time = 300

# Uploads
upload_max_filesize = 20M
post_max_size = 20M
max_file_uploads = 20

# Sessions
session.gc_maxlifetime = 28800
session.gc_probability = 1
session.gc_divisor = 100

# ===========================================
# OPCACHE (OBLIGATOIRE EN PRODUCTION)
# ===========================================

# Activer OPcache
opcache.enable = 1
opcache.enable_cli = 0

# Mémoire OPcache
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000

# Validation des fichiers
opcache.validate_timestamps = 0
opcache.revalidate_freq = 0

# Optimisations
opcache.save_comments = 1
opcache.fast_shutdown = 1
opcache.enable_file_override = 1

# ===========================================
# REALPATH CACHE
# ===========================================

realpath_cache_size = 4096K
realpath_cache_ttl = 600

# ===========================================
# CONFIGURATION SPÉCIFIQUE LARAVEL
# ===========================================

# Variables d'environnement
variables_order = "GPCS"
request_order = "GP"

# Timezone
date.timezone = "UTC"

# ===========================================
# LOGS ET MONITORING
# ===========================================

# Logs détaillés
log_errors_max_len = 1024
ignore_repeated_errors = Off
ignore_repeated_source = Off

# Slow logs
request_slowlog_timeout = 10s
slowlog = /var/log/php/slow.log

# ===========================================
# CONFIGURATION PHP-FPM
# ===========================================

# Ces paramètres vont dans /etc/php/8.2/fpm/pool.d/www.conf

# Processus
# pm = dynamic
# pm.max_children = 50
# pm.start_servers = 5
# pm.min_spare_servers = 5
# pm.max_spare_servers = 35
# pm.max_requests = 500

# Timeouts
# request_terminate_timeout = 300
# request_slowlog_timeout = 10s

# Monitoring
# pm.status_path = /status
# ping.path = /ping

# ===========================================
# INSTRUCTIONS D'INSTALLATION
# ===========================================

# 1. Copiez ce fichier vers /etc/php/8.2/fpm/conf.d/99-pos-production.ini
# 2. Redémarrez PHP-FPM: sudo systemctl restart php8.2-fpm
# 3. Vérifiez la configuration: php -i | grep opcache
# 4. Testez les performances avec: ab -n 1000 -c 10 http://votre-domaine.com/

# ===========================================
# VÉRIFICATIONS
# ===========================================

# Vérifier OPcache:
# php -r "echo opcache_get_status()['opcache_enabled'] ? 'OPcache activé' : 'OPcache désactivé';"

# Vérifier la configuration:
# php --ini

# Surveiller les performances:
# tail -f /var/log/php/slow.log
