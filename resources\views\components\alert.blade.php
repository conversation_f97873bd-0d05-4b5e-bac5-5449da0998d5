@props([
    'type' => 'info',
    'dismissible' => true,
    'icon' => null,
    'title' => null
])

@php
$types = [
    'success' => [
        'bg' => 'bg-green-50',
        'border' => 'border-green-200',
        'text' => 'text-green-800',
        'icon' => 'fas fa-check-circle',
        'iconColor' => 'text-green-400'
    ],
    'error' => [
        'bg' => 'bg-red-50',
        'border' => 'border-red-200',
        'text' => 'text-red-800',
        'icon' => 'fas fa-exclamation-circle',
        'iconColor' => 'text-red-400'
    ],
    'warning' => [
        'bg' => 'bg-yellow-50',
        'border' => 'border-yellow-200',
        'text' => 'text-yellow-800',
        'icon' => 'fas fa-exclamation-triangle',
        'iconColor' => 'text-yellow-400'
    ],
    'info' => [
        'bg' => 'bg-blue-50',
        'border' => 'border-blue-200',
        'text' => 'text-blue-800',
        'icon' => 'fas fa-info-circle',
        'iconColor' => 'text-blue-400'
    ]
];

$config = $types[$type] ?? $types['info'];
$iconClass = $icon ?? $config['icon'];
@endphp

<div {{ $attributes->merge([
    'class' => "rounded-lg border p-4 {$config['bg']} {$config['border']} fade-in",
    'x-data' => $dismissible ? '{ show: true }' : null,
    'x-show' => $dismissible ? 'show' : null,
    'x-transition:enter' => $dismissible ? 'transition ease-out duration-300' : null,
    'x-transition:enter-start' => $dismissible ? 'opacity-0 transform scale-95' : null,
    'x-transition:enter-end' => $dismissible ? 'opacity-100 transform scale-100' : null,
    'x-transition:leave' => $dismissible ? 'transition ease-in duration-200' : null,
    'x-transition:leave-start' => $dismissible ? 'opacity-100 transform scale-100' : null,
    'x-transition:leave-end' => $dismissible ? 'opacity-0 transform scale-95' : null
]) }}>
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="{{ $iconClass }} {{ $config['iconColor'] }}"></i>
        </div>
        <div class="ml-3 flex-1">
            @if($title)
            <h3 class="text-sm font-medium {{ $config['text'] }}">
                {{ $title }}
            </h3>
            @endif
            <div class="text-sm {{ $config['text'] }} {{ $title ? 'mt-1' : '' }}">
                {{ $slot }}
            </div>
        </div>
        @if($dismissible)
        <div class="ml-auto pl-3">
            <div class="-mx-1.5 -my-1.5">
                <button @click="show = false" 
                        type="button" 
                        class="inline-flex rounded-md p-1.5 {{ $config['text'] }} hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-{{ $type }}-50 focus:ring-{{ $type }}-600">
                    <span class="sr-only">Fermer</span>
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
        </div>
        @endif
    </div>
</div>
