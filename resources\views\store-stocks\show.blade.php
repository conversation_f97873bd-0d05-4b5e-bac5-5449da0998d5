<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-box mr-3 text-blue-600"></i>{{ $storeStock->product->name }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">
                    Stock dans {{ $storeStock->store->name }} • {{ $storeStock->product->barcode }}
                </p>
            </div>
            <div class="flex space-x-2">
                <x-button onclick="openQuickAdjustModal()" variant="primary" icon="fas fa-plus-minus">
                    Ajuster le stock
                </x-button>
                <x-button href="{{ route('store-stocks.edit', $storeStock) }}" variant="secondary" icon="fas fa-edit">
                    Modifier
                </x-button>
                <x-button href="{{ route('store-stocks.index', ['store_id' => $storeStock->store_id]) }}" 
                         variant="outline" icon="fas fa-arrow-left">
                    Retour
                </x-button>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Informations principales -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- Statut du stock -->
                <div class="lg:col-span-2">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">
                                <i class="fas fa-info-circle mr-2 text-blue-600"></i>Statut du stock
                            </h3>
                            @php $status = $storeStock->getStatus(); @endphp
                            @if($status === 'out_of_stock')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i>Rupture de stock
                                </span>
                            @elseif($status === 'low_stock')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>Stock faible
                                </span>
                            @elseif($status === 'overstock')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                    <i class="fas fa-arrow-up mr-1"></i>Surstock
                                </span>
                            @else
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>En stock
                                </span>
                            @endif
                        </div>

                        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold {{ $storeStock->quantity <= 0 ? 'text-red-600' : ($storeStock->quantity <= $storeStock->min_stock_level ? 'text-yellow-600' : 'text-gray-900') }}">
                                    {{ $storeStock->quantity }}
                                </div>
                                <div class="text-sm text-gray-600">Stock actuel</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-700">{{ $storeStock->min_stock_level }}</div>
                                <div class="text-sm text-gray-600">Seuil minimum</div>
                            </div>
                            @if($storeStock->max_stock_level)
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-700">{{ $storeStock->max_stock_level }}</div>
                                    <div class="text-sm text-gray-600">Seuil maximum</div>
                                </div>
                            @endif
                            @if($storeStock->reorder_point)
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">{{ $storeStock->reorder_point }}</div>
                                    <div class="text-sm text-gray-600">Point de commande</div>
                                </div>
                            @endif
                        </div>

                        @if($storeStock->location)
                            <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-map-marker-alt text-gray-600 mr-2"></i>
                                    <span class="font-medium text-gray-700">Emplacement:</span>
                                    <span class="text-gray-900 ml-2">{{ $storeStock->location }}</span>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Valeur du stock -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-coins mr-2 text-green-600"></i>Valeur du stock
                    </h3>
                    
                    @php
                        $currentPrice = $storeStock->product->getCurrentPrice($storeStock->store_id);
                        $stockValue = $currentPrice ? $storeStock->quantity * $currentPrice->cost_price : 0;
                        $potentialRevenue = $currentPrice ? $storeStock->quantity * $currentPrice->selling_price : 0;
                        $potentialProfit = $potentialRevenue - $stockValue;
                    @endphp
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Valeur d'achat:</span>
                            <span class="font-medium text-gray-900">{{ number_format($stockValue, 0, ',', ' ') }} FCFA</span>
                        </div>
                        @if($currentPrice)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Valeur de vente:</span>
                                <span class="font-medium text-gray-900">{{ number_format($potentialRevenue, 0, ',', ' ') }} FCFA</span>
                            </div>
                            <div class="flex justify-between border-t pt-2">
                                <span class="text-gray-600">Bénéfice potentiel:</span>
                                <span class="font-medium text-green-600">{{ number_format($potentialProfit, 0, ',', ' ') }} FCFA</span>
                            </div>
                            <div class="text-center text-sm text-gray-500">
                                Prix unitaire: {{ number_format($currentPrice->cost_price, 0, ',', ' ') }} FCFA → {{ number_format($currentPrice->selling_price, 0, ',', ' ') }} FCFA
                            </div>
                        @else
                            <div class="text-center text-sm text-yellow-600">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Aucun prix défini pour ce produit dans ce magasin
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Informations du produit -->
            <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">
                    <i class="fas fa-tag mr-2 text-purple-600"></i>Informations du produit
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Nom</h4>
                        <p class="text-gray-900">{{ $storeStock->product->name }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Code-barres</h4>
                        <p class="text-gray-900 font-mono">{{ $storeStock->product->barcode }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Catégorie</h4>
                        <p class="text-gray-900">{{ $storeStock->product->category->name ?? 'Non définie' }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Unité</h4>
                        <p class="text-gray-900">{{ $storeStock->product->unit ?? 'Unité' }}</p>
                    </div>
                    @if($storeStock->product->description)
                        <div class="md:col-span-2 lg:col-span-4">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Description</h4>
                            <p class="text-gray-900">{{ $storeStock->product->description }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Historique des mouvements -->
            <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-history mr-2 text-orange-600"></i>Historique des mouvements ({{ $stockMovements->count() }})
                    </h3>
                    <x-button href="{{ route('stock-movements.index', ['product_id' => $storeStock->product_id, 'store_id' => $storeStock->store_id]) }}" 
                             variant="outline" size="sm" icon="fas fa-external-link-alt">
                        Voir tout l'historique
                    </x-button>
                </div>

                @if($stockMovements->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">Quantité</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">Stock après</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Utilisateur</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Motif</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @foreach($stockMovements->take(10) as $movement)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $movement->created_at->format('d/m/Y H:i') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($movement->type === 'in')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-arrow-up mr-1"></i>Entrée
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i class="fas fa-arrow-down mr-1"></i>Sortie
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium {{ $movement->type === 'in' ? 'text-green-600' : 'text-red-600' }}">
                                            {{ $movement->type === 'in' ? '+' : '-' }}{{ $movement->quantity }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                                            {{ $movement->stock_after }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $movement->user->name ?? 'Système' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $movement->reason ?? 'Non spécifié' }}
                                            @if($movement->notes)
                                                <div class="text-xs text-gray-400">{{ Str::limit($movement->notes, 30) }}</div>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-history text-4xl text-gray-400 mb-4"></i>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Aucun mouvement enregistré</h4>
                        <p class="text-gray-500">L'historique des mouvements apparaîtra ici.</p>
                    </div>
                @endif
            </div>

            <!-- Statistiques et analyses -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Statistiques de vente -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-chart-line mr-2 text-blue-600"></i>Statistiques de vente (30 derniers jours)
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Quantité vendue:</span>
                            <span class="font-medium text-gray-900">{{ $salesStats['quantity_sold'] ?? 0 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Chiffre d'affaires:</span>
                            <span class="font-medium text-gray-900">{{ number_format($salesStats['revenue'] ?? 0, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Bénéfice réalisé:</span>
                            <span class="font-medium text-green-600">{{ number_format($salesStats['profit'] ?? 0, 0, ',', ' ') }} FCFA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Ventes moyennes/jour:</span>
                            <span class="font-medium text-gray-900">{{ number_format($salesStats['avg_daily_sales'] ?? 0, 1) }}</span>
                        </div>
                        @if(($salesStats['avg_daily_sales'] ?? 0) > 0)
                            <div class="flex justify-between border-t pt-2">
                                <span class="text-gray-600">Autonomie estimée:</span>
                                <span class="font-medium text-blue-600">
                                    {{ round($storeStock->quantity / $salesStats['avg_daily_sales']) }} jours
                                </span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Recommandations -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-lightbulb mr-2 text-yellow-600"></i>Recommandations
                    </h3>
                    
                    <div class="space-y-3">
                        @if($storeStock->quantity <= 0)
                            <div class="p-3 bg-red-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                                    <span class="text-red-800 font-medium">Rupture de stock critique</span>
                                </div>
                                <p class="text-red-700 text-sm mt-1">Réapprovisionnement urgent nécessaire</p>
                            </div>
                        @elseif($storeStock->quantity <= $storeStock->min_stock_level)
                            <div class="p-3 bg-yellow-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-circle text-yellow-600 mr-2"></i>
                                    <span class="text-yellow-800 font-medium">Stock faible</span>
                                </div>
                                <p class="text-yellow-700 text-sm mt-1">Planifier un réapprovisionnement</p>
                            </div>
                        @endif

                        @if($storeStock->reorder_point && $storeStock->quantity <= $storeStock->reorder_point)
                            <div class="p-3 bg-blue-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-shopping-cart text-blue-600 mr-2"></i>
                                    <span class="text-blue-800 font-medium">Point de commande atteint</span>
                                </div>
                                <p class="text-blue-700 text-sm mt-1">Déclencher une commande fournisseur</p>
                            </div>
                        @endif

                        @if($storeStock->max_stock_level && $storeStock->quantity > $storeStock->max_stock_level)
                            <div class="p-3 bg-purple-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-arrow-up text-purple-600 mr-2"></i>
                                    <span class="text-purple-800 font-medium">Surstock détecté</span>
                                </div>
                                <p class="text-purple-700 text-sm mt-1">Considérer une promotion ou un transfert</p>
                            </div>
                        @endif

                        @if(!$currentPrice)
                            <div class="p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-tag text-gray-600 mr-2"></i>
                                    <span class="text-gray-800 font-medium">Prix non défini</span>
                                </div>
                                <p class="text-gray-700 text-sm mt-1">
                                    <a href="{{ route('product-prices.create', ['product_id' => $storeStock->product_id, 'store_id' => $storeStock->store_id]) }}"
                                       class="text-blue-600 hover:text-blue-800">Définir un prix de vente</a>
                                </p>
                            </div>
                        @else
                            <div class="p-3 bg-green-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                    <span class="text-green-800 font-medium">Stock optimal</span>
                                </div>
                                <p class="text-green-700 text-sm mt-1">Le stock est dans les niveaux recommandés</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'ajustement rapide -->
    <div id="quickAdjustModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <form method="POST" action="{{ route('store-stocks.adjust', $storeStock) }}">
                    @csrf
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            Ajuster le stock
                        </h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Stock actuel</label>
                                <p class="text-2xl font-bold text-gray-900">{{ $storeStock->quantity }}</p>
                            </div>
                            
                            <div>
                                <label for="adjustment_type" class="block text-sm font-medium text-gray-700 mb-1">Type d'ajustement</label>
                                <select id="adjustment_type" name="adjustment_type" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="add">Ajouter au stock</option>
                                    <option value="remove">Retirer du stock</option>
                                    <option value="set">Définir le stock</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">Quantité</label>
                                <input type="number" id="quantity" name="quantity" min="0" step="1" required
                                       class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label for="reason" class="block text-sm font-medium text-gray-700 mb-1">Motif</label>
                                <select id="reason" name="reason" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="restock">Réapprovisionnement</option>
                                    <option value="sale">Vente</option>
                                    <option value="damage">Produit endommagé</option>
                                    <option value="theft">Vol</option>
                                    <option value="inventory">Inventaire</option>
                                    <option value="other">Autre</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                                <textarea id="notes" name="notes" rows="2" 
                                         class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                         placeholder="Notes sur cet ajustement..."></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-2">
                        <button type="button" onclick="closeQuickAdjustModal()" 
                               class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Annuler
                        </button>
                        <button type="submit" 
                               class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">
                            Ajuster le stock
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            function openQuickAdjustModal() {
                document.getElementById('quickAdjustModal').classList.remove('hidden');
                document.getElementById('quantity').focus();
            }
            
            function closeQuickAdjustModal() {
                document.getElementById('quickAdjustModal').classList.add('hidden');
            }
            
            // Fermer le modal en cliquant à l'extérieur
            document.getElementById('quickAdjustModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeQuickAdjustModal();
                }
            });
        </script>
    @endpush
</x-app-layout>
