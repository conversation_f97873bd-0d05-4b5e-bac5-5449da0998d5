<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_prices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('store_id')->constrained()->onDelete('cascade');
            $table->decimal('cost_price', 10, 2)->nullable(); // Prix d'achat/coût
            $table->decimal('selling_price', 10, 2); // Prix de vente
            $table->decimal('min_selling_price', 10, 2)->nullable(); // Prix de vente minimum autorisé
            $table->decimal('wholesale_price', 10, 2)->nullable(); // Prix de gros
            $table->decimal('tax_rate', 5, 2)->default(0); // Taux de taxe spécifique au produit
            $table->boolean('is_active')->default(true);
            $table->date('effective_from')->default(now()); // Date d'entrée en vigueur
            $table->date('effective_until')->nullable(); // Date de fin de validité
            $table->text('notes')->nullable();
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->unique(['product_id', 'store_id', 'effective_from'], 'unique_product_store_price');
            $table->index(['store_id', 'is_active']);
            $table->index(['product_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_prices');
    }
};
