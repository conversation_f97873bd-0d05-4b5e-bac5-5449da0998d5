<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-user mr-3 text-blue-600"></i>{{ $user->name }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">{{ $user->email }} - {{ $user->roles->first()?->name ?? 'Aucun rôle' }}</p>
            </div>
            <div class="flex space-x-2">
                @if($user->id !== auth()->id())
                <form action="{{ route('users.toggle', $user) }}" method="POST" class="inline">
                    @csrf
                    @method('PATCH')
                    <x-button 
                        type="submit" 
                        variant="{{ $user->is_active ? 'warning' : 'success' }}" 
                        icon="fas {{ $user->is_active ? 'fa-user-slash' : 'fa-user-check' }}"
                    >
                        {{ $user->is_active ? 'Désactiver' : 'Activer' }}
                    </x-button>
                </form>
                @endif
                <x-button variant="primary" href="{{ route('users.edit', $user) }}" icon="fas fa-edit">
                    Modifier
                </x-button>
                <x-button variant="secondary" href="{{ route('users.index') }}" icon="fas fa-arrow-left">
                    Retour
                </x-button>
            </div>
        </div>
    </x-slot>

    <div class="py-4 sm:py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Statut de l'utilisateur -->
            <div class="mb-6 sm:mb-8">
                @if(!$user->is_active)
                    <x-alert type="warning" title="Compte désactivé">
                        Ce compte utilisateur est actuellement désactivé et ne peut pas se connecter.
                    </x-alert>
                @elseif(!$user->email_verified_at)
                    <x-alert type="info" title="Email non vérifié">
                        L'adresse email de cet utilisateur n'a pas encore été vérifiée.
                    </x-alert>
                @else
                    <x-alert type="success" title="Compte actif">
                        Ce compte utilisateur est actif et fonctionnel.
                    </x-alert>
                @endif
            </div>

            <!-- Statistiques de l'utilisateur -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
                <x-stat-card
                    title="Ventes totales"
                    :value="$totalSales"
                    icon="fas fa-receipt"
                    color="blue"
                    description="Depuis la création"
                />

                <x-stat-card
                    title="Chiffre d'affaires"
                    :value="number_format($totalSalesAmount, 0, ',', ' ') . ' FCFA'"
                    icon="fas fa-euro-sign"
                    color="green"
                    description="Total généré"
                />

                <x-stat-card
                    title="Ventes aujourd'hui"
                    :value="$todaySales"
                    icon="fas fa-calendar-day"
                    color="yellow"
                    description="Activité du jour"
                />

                <x-stat-card
                    title="CA aujourd'hui"
                    :value="number_format($todaySalesAmount, 0, ',', ' ') . ' FCFA'"
                    icon="fas fa-chart-line"
                    color="purple"
                    description="Revenus du jour"
                />
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- Informations de l'utilisateur -->
                <div class="lg:col-span-2">
                    <x-card title="Informations personnelles" icon="fas fa-id-card">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Nom complet</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $user->name }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Adresse email</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $user->email }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Téléphone</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $user->phone ?? 'Non renseigné' }}</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Rôle</label>
                                @if($user->roles->count() > 0)
                                    @foreach($user->roles as $role)
                                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium mt-1
                                        @switch($role->name)
                                            @case('superAdmin') bg-red-100 text-red-800 @break
                                            @case('admin') bg-orange-100 text-orange-800 @break
                                            @case('manager') bg-blue-100 text-blue-800 @break
                                            @case('caissier') bg-green-100 text-green-800 @break
                                            @default bg-gray-100 text-gray-800
                                        @endswitch">
                                        {{ ucfirst($role->name) }}
                                    </span>
                                    @endforeach
                                @else
                                    <p class="mt-1 text-sm text-gray-500">Aucun rôle assigné</p>
                                @endif
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Statut du compte</label>
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium mt-1
                                    {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $user->is_active ? 'Actif' : 'Inactif' }}
                                </span>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700">Email vérifié</label>
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium mt-1
                                    {{ $user->email_verified_at ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ $user->email_verified_at ? 'Vérifié' : 'Non vérifié' }}
                                </span>
                            </div>
                        </div>
                    </x-card>

                    <!-- Ventes récentes -->
                    @if($user->sales->count() > 0)
                    <div class="mt-6">
                        <x-card title="Ventes récentes" icon="fas fa-receipt" padding="p-0">
                            <x-table 
                                :headers="['Date', 'Client', 'Montant', 'Statut']"
                            >
                                @foreach($user->sales as $sale)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 sm:px-6 py-4 text-sm text-gray-900">
                                        {{ $sale->created_at->format('d/m/Y H:i') }}
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 text-sm text-gray-900">
                                        {{ $sale->customer?->name ?? 'Client anonyme' }}
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 text-sm font-medium text-gray-900">
                                        {{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA
                                    </td>
                                    <td class="px-4 sm:px-6 py-4">
                                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium
                                            @switch($sale->status)
                                                @case('completed') bg-green-100 text-green-800 @break
                                                @case('pending') bg-yellow-100 text-yellow-800 @break
                                                @case('cancelled') bg-red-100 text-red-800 @break
                                                @default bg-gray-100 text-gray-800
                                            @endswitch">
                                            {{ ucfirst($sale->status) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </x-table>
                            
                            @if($totalSales > 10)
                            <div class="p-4 text-center border-t border-gray-200">
                                <a href="{{ route('sales.index', ['user' => $user->id]) }}" 
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    Voir toutes les ventes ({{ $totalSales }})
                                </a>
                            </div>
                            @endif
                        </x-card>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Actions rapides -->
                    <x-card title="Actions rapides" icon="fas fa-bolt">
                        <div class="space-y-3">
                            <x-button 
                                variant="primary" 
                                href="{{ route('users.edit', $user) }}" 
                                icon="fas fa-edit"
                                class="w-full"
                            >
                                Modifier l'utilisateur
                            </x-button>

                            @if($user->id !== auth()->id())
                            <form action="{{ route('users.reset-password', $user) }}" method="POST">
                                @csrf
                                @method('PATCH')
                                <x-button 
                                    type="submit" 
                                    variant="warning" 
                                    icon="fas fa-key"
                                    class="w-full"
                                    onclick="return confirm('Réinitialiser le mot de passe à &quot;password123&quot; ?')"
                                >
                                    Réinitialiser le mot de passe
                                </x-button>
                            </form>

                            <form action="{{ route('users.toggle', $user) }}" method="POST">
                                @csrf
                                @method('PATCH')
                                <x-button 
                                    type="submit" 
                                    variant="{{ $user->is_active ? 'warning' : 'success' }}" 
                                    icon="fas {{ $user->is_active ? 'fa-user-slash' : 'fa-user-check' }}"
                                    class="w-full"
                                >
                                    {{ $user->is_active ? 'Désactiver' : 'Activer' }} le compte
                                </x-button>
                            </form>
                            @endif
                        </div>
                    </x-card>

                    <!-- Permissions -->
                    @if($user->roles->count() > 0)
                    <x-card title="Permissions" icon="fas fa-shield-alt">
                        @foreach($user->roles as $role)
                        <div class="mb-4 last:mb-0">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">{{ ucfirst($role->name) }}</h4>
                            @if($role->permissions->count() > 0)
                            <div class="space-y-1">
                                @foreach($role->permissions as $permission)
                                <div class="flex items-center text-xs text-gray-600">
                                    <i class="fas fa-check text-green-500 mr-2"></i>
                                    {{ $permission->name }}
                                </div>
                                @endforeach
                            </div>
                            @else
                            <p class="text-xs text-gray-500">Aucune permission spécifique</p>
                            @endif
                        </div>
                        @endforeach
                    </x-card>
                    @endif

                    <!-- Informations système -->
                    <x-card title="Informations système" icon="fas fa-info-circle">
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-500">Créé le:</span>
                                <span class="text-gray-900">{{ $user->created_at->format('d/m/Y') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500">Modifié le:</span>
                                <span class="text-gray-900">{{ $user->updated_at->format('d/m/Y') }}</span>
                            </div>
                            @if($user->email_verified_at)
                            <div class="flex justify-between">
                                <span class="text-gray-500">Email vérifié:</span>
                                <span class="text-gray-900">{{ $user->email_verified_at->format('d/m/Y') }}</span>
                            </div>
                            @endif
                        </div>
                    </x-card>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
