<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Store;

class UserStoreAssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $stores = Store::all();

        if ($stores->isEmpty()) {
            $this->command->warn('Aucun magasin trouvé. Veuillez d\'abord exécuter StoreSeeder.');
            return;
        }

        foreach ($users as $user) {
            // Assigner les super admins au magasin principal
            if ($user->hasRole('superAdmin')) {
                $mainStore = $stores->where('code', 'CENTRE')->first() ?? $stores->first();
                $user->update(['store_id' => $mainStore->id]);
                $this->command->info("Super Admin {$user->name} assigné au magasin {$mainStore->name}");
            }
            // Assigner les admins au magasin principal également
            elseif ($user->hasRole('admin')) {
                $mainStore = $stores->where('code', 'CENTRE')->first() ?? $stores->first();
                $user->update(['store_id' => $mainStore->id]);
                $this->command->info("Admin {$user->name} assigné au magasin {$mainStore->name}");
            }
            // Répartir les caissiers entre les différents magasins
            elseif ($user->hasRole('cashier')) {
                $randomStore = $stores->random();
                $user->update(['store_id' => $randomStore->id]);
                $this->command->info("Caissier {$user->name} assigné au magasin {$randomStore->name}");
            }
            // Assigner les autres utilisateurs au magasin principal par défaut
            else {
                $mainStore = $stores->where('code', 'CENTRE')->first() ?? $stores->first();
                $user->update(['store_id' => $mainStore->id]);
                $this->command->info("Utilisateur {$user->name} assigné au magasin {$mainStore->name}");
            }
        }

        // Afficher un résumé
        $this->command->info("\n=== Résumé des assignations ===");
        foreach ($stores as $store) {
            $userCount = User::where('store_id', $store->id)->count();
            $this->command->info("{$store->name} ({$store->code}): {$userCount} utilisateur(s)");
        }
    }
}
