<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-plus mr-3 text-blue-600"></i>Ajouter un Stock
                    @if($store)
                        <span class="text-lg text-blue-600 font-medium">- {{ $store->name }}</span>
                    @endif
                </h2>
                <p class="text-sm text-gray-600 mt-1">Ajoutez un nouveau produit au stock du magasin</p>
            </div>
            <x-button href="{{ route('store-stocks.index', ['store_id' => request('store_id')]) }}" 
                     variant="outline" icon="fas fa-arrow-left">
                Retour aux stocks
            </x-button>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <form method="POST" action="{{ route('store-stocks.store') }}" class="space-y-6">
                @csrf
                @if(request('store_id'))
                    <input type="hidden" name="store_id" value="{{ request('store_id') }}">
                @endif

                <!-- Sélection du magasin et produit -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>Informations de base
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @if(!request('store_id'))
                            <x-form-field>
                                <x-input-label for="store_id" value="Magasin *" />
                                <select id="store_id" name="store_id" required
                                       class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Sélectionnez un magasin</option>
                                    @foreach($stores as $storeOption)
                                        <option value="{{ $storeOption->id }}" {{ old('store_id') == $storeOption->id ? 'selected' : '' }}>
                                            {{ $storeOption->name }} ({{ $storeOption->code }})
                                        </option>
                                    @endforeach
                                </select>
                                <x-input-error :messages="$errors->get('store_id')" />
                            </x-form-field>
                        @else
                            <x-form-field>
                                <x-input-label value="Magasin" />
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-store text-blue-600 mr-2"></i>
                                        <span class="font-medium">{{ $store->name }}</span>
                                        <span class="text-gray-500 ml-2">({{ $store->code }})</span>
                                    </div>
                                </div>
                            </x-form-field>
                        @endif

                        <x-form-field>
                            <x-input-label for="product_id" value="Produit *" />
                            <select id="product_id" name="product_id" required
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Sélectionnez un produit</option>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}" 
                                           data-barcode="{{ $product->barcode }}"
                                           data-category="{{ $product->category->name ?? '' }}"
                                           {{ old('product_id') == $product->id ? 'selected' : '' }}>
                                        {{ $product->name }} - {{ $product->barcode }}
                                    </option>
                                @endforeach
                            </select>
                            <x-input-error :messages="$errors->get('product_id')" />
                            <p class="text-xs text-gray-500 mt-1">Seuls les produits sans stock dans ce magasin sont affichés</p>
                        </x-form-field>

                        <!-- Informations du produit sélectionné -->
                        <div id="productInfo" class="md:col-span-2 hidden">
                            <div class="p-4 bg-blue-50 rounded-lg">
                                <h4 class="font-medium text-blue-900 mb-2">Informations du produit</h4>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <span class="text-blue-700 font-medium">Code-barres:</span>
                                        <span id="productBarcode" class="text-blue-900"></span>
                                    </div>
                                    <div>
                                        <span class="text-blue-700 font-medium">Catégorie:</span>
                                        <span id="productCategory" class="text-blue-900"></span>
                                    </div>
                                    <div>
                                        <span class="text-blue-700 font-medium">Prix actuel:</span>
                                        <span id="productPrice" class="text-blue-900"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quantités et seuils -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-layer-group mr-2 text-green-600"></i>Quantités et seuils
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <x-form-field>
                            <x-input-label for="quantity" value="Quantité initiale *" />
                            <x-text-input id="quantity" name="quantity" type="number" 
                                         min="0" step="1" value="{{ old('quantity', '0') }}" required />
                            <x-input-error :messages="$errors->get('quantity')" />
                            <p class="text-xs text-gray-500 mt-1">Quantité à ajouter au stock</p>
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="min_stock_level" value="Seuil minimum *" />
                            <x-text-input id="min_stock_level" name="min_stock_level" type="number" 
                                         min="0" step="1" value="{{ old('min_stock_level', '5') }}" required />
                            <x-input-error :messages="$errors->get('min_stock_level')" />
                            <p class="text-xs text-gray-500 mt-1">Alerte de stock faible</p>
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="max_stock_level" value="Seuil maximum" />
                            <x-text-input id="max_stock_level" name="max_stock_level" type="number" 
                                         min="0" step="1" value="{{ old('max_stock_level') }}" />
                            <x-input-error :messages="$errors->get('max_stock_level')" />
                            <p class="text-xs text-gray-500 mt-1">Alerte de surstock</p>
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="reorder_point" value="Point de commande" />
                            <x-text-input id="reorder_point" name="reorder_point" type="number" 
                                         min="0" step="1" value="{{ old('reorder_point') }}" />
                            <x-input-error :messages="$errors->get('reorder_point')" />
                            <p class="text-xs text-gray-500 mt-1">Seuil de réapprovisionnement</p>
                        </x-form-field>
                    </div>

                    <!-- Calculateur automatique -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h4 class="font-medium text-gray-900 mb-3">
                            <i class="fas fa-calculator mr-2 text-gray-600"></i>Calculateur automatique de seuils
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Ventes moyennes/jour</label>
                                <input type="number" id="avg_daily_sales" min="0" step="0.1" 
                                       class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Ex: 2.5">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Délai de livraison (jours)</label>
                                <input type="number" id="lead_time" min="1" step="1" value="7"
                                       class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div class="flex items-end">
                                <button type="button" onclick="calculateThresholds()" 
                                       class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                    <i class="fas fa-calculator mr-2"></i>Calculer
                                </button>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">
                            Le calculateur suggère des seuils basés sur vos ventes moyennes et délais de livraison
                        </p>
                    </div>
                </div>

                <!-- Emplacement et organisation -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-map-marker-alt mr-2 text-purple-600"></i>Emplacement et organisation
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field>
                            <x-input-label for="location" value="Emplacement dans le magasin" />
                            <x-text-input id="location" name="location" type="text" 
                                         value="{{ old('location') }}" 
                                         placeholder="Ex: Rayon A, Étagère 3, Niveau 2" />
                            <x-input-error :messages="$errors->get('location')" />
                            <p class="text-xs text-gray-500 mt-1">Aide à localiser le produit rapidement</p>
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="supplier_reference" value="Référence fournisseur" />
                            <x-text-input id="supplier_reference" name="supplier_reference" type="text" 
                                         value="{{ old('supplier_reference') }}" 
                                         placeholder="Référence du fournisseur" />
                            <x-input-error :messages="$errors->get('supplier_reference')" />
                        </x-form-field>
                    </div>
                </div>

                <!-- Mouvement de stock initial -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-clipboard-list mr-2 text-orange-600"></i>Mouvement de stock initial
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field>
                            <x-input-label for="movement_type" value="Type de mouvement" />
                            <select id="movement_type" name="movement_type" 
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="initial_stock" {{ old('movement_type', 'initial_stock') === 'initial_stock' ? 'selected' : '' }}>
                                    Stock initial
                                </option>
                                <option value="restock" {{ old('movement_type') === 'restock' ? 'selected' : '' }}>
                                    Réapprovisionnement
                                </option>
                                <option value="transfer" {{ old('movement_type') === 'transfer' ? 'selected' : '' }}>
                                    Transfert depuis un autre magasin
                                </option>
                                <option value="inventory" {{ old('movement_type') === 'inventory' ? 'selected' : '' }}>
                                    Ajustement d'inventaire
                                </option>
                            </select>
                            <x-input-error :messages="$errors->get('movement_type')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="unit_cost" value="Coût unitaire" />
                            <x-text-input id="unit_cost" name="unit_cost" type="number" 
                                         min="0" step="0.01" value="{{ old('unit_cost') }}" 
                                         placeholder="Coût d'achat unitaire" />
                            <x-input-error :messages="$errors->get('unit_cost')" />
                            <p class="text-xs text-gray-500 mt-1">Pour calculer la valeur du stock</p>
                        </x-form-field>

                        <x-form-field class="md:col-span-2">
                            <x-input-label for="notes" value="Notes" />
                            <textarea id="notes" name="notes" rows="3"
                                     class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                     placeholder="Notes sur ce stock initial...">{{ old('notes') }}</textarea>
                            <x-input-error :messages="$errors->get('notes')" />
                        </x-form-field>
                    </div>
                </div>

                <!-- Résumé et validation -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-check-circle mr-2 text-green-600"></i>Résumé
                    </h3>
                    
                    <div id="summary" class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Quantité ajoutée:</span>
                            <span id="summaryQuantity" class="text-gray-900 ml-2">0</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Valeur totale:</span>
                            <span id="summaryValue" class="text-gray-900 ml-2">0 FCFA</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Seuil d'alerte:</span>
                            <span id="summaryThreshold" class="text-gray-900 ml-2">5</span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-4">
                    <x-button href="{{ route('store-stocks.index', ['store_id' => request('store_id')]) }}" 
                             variant="outline">
                        Annuler
                    </x-button>
                    <x-button type="submit" variant="primary" icon="fas fa-save">
                        Ajouter au stock
                    </x-button>
                </div>
            </form>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const productSelect = document.getElementById('product_id');
                const productInfo = document.getElementById('productInfo');
                const quantityInput = document.getElementById('quantity');
                const unitCostInput = document.getElementById('unit_cost');
                const minStockInput = document.getElementById('min_stock_level');

                // Afficher les informations du produit sélectionné
                productSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption.value) {
                        document.getElementById('productBarcode').textContent = selectedOption.dataset.barcode || 'N/A';
                        document.getElementById('productCategory').textContent = selectedOption.dataset.category || 'N/A';
                        productInfo.classList.remove('hidden');
                        
                        // Charger le prix actuel via AJAX si nécessaire
                        loadProductPrice(selectedOption.value);
                    } else {
                        productInfo.classList.add('hidden');
                    }
                    updateSummary();
                });

                // Mise à jour du résumé en temps réel
                [quantityInput, unitCostInput, minStockInput].forEach(input => {
                    input.addEventListener('input', updateSummary);
                });

                function updateSummary() {
                    const quantity = parseFloat(quantityInput.value) || 0;
                    const unitCost = parseFloat(unitCostInput.value) || 0;
                    const minStock = parseFloat(minStockInput.value) || 0;
                    
                    document.getElementById('summaryQuantity').textContent = quantity;
                    document.getElementById('summaryValue').textContent = new Intl.NumberFormat('fr-FR').format(quantity * unitCost) + ' FCFA';
                    document.getElementById('summaryThreshold').textContent = minStock;
                }

                function loadProductPrice(productId) {
                    // Simuler le chargement du prix - à implémenter avec AJAX
                    document.getElementById('productPrice').textContent = 'Chargement...';
                }
            });

            function calculateThresholds() {
                const avgDailySales = parseFloat(document.getElementById('avg_daily_sales').value) || 0;
                const leadTime = parseFloat(document.getElementById('lead_time').value) || 7;
                
                if (avgDailySales > 0) {
                    // Calcul des seuils recommandés
                    const reorderPoint = Math.ceil(avgDailySales * leadTime * 1.2); // 20% de marge de sécurité
                    const minStock = Math.ceil(avgDailySales * leadTime * 0.5);
                    const maxStock = Math.ceil(avgDailySales * leadTime * 3);
                    
                    document.getElementById('reorder_point').value = reorderPoint;
                    document.getElementById('min_stock_level').value = minStock;
                    document.getElementById('max_stock_level').value = maxStock;
                    
                    // Mise à jour du résumé
                    document.getElementById('summaryThreshold').textContent = minStock;
                    
                    alert(`Seuils calculés:\n- Point de commande: ${reorderPoint}\n- Stock minimum: ${minStock}\n- Stock maximum: ${maxStock}`);
                } else {
                    alert('Veuillez saisir les ventes moyennes par jour');
                }
            }
        </script>
    @endpush
</x-app-layout>
