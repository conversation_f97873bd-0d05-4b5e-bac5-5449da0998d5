<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-credit-card mr-2"></i>Détails du Crédit #{{ $credit->id }}
            </h2>
            <div class="flex space-x-2">
                @if($credit->status === 'active')
                <button onclick="openPaymentModal()" 
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>Ajouter Paiement
                </button>
                @endif
                <a href="{{ route('credits.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Informations du crédit -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                
                <!-- Informations principales -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-6">Informations du crédit</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Vente associée</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <a href="{{ route('sales.show', $credit->sale) }}" class="text-blue-600 hover:text-blue-800">
                                            {{ $credit->sale->sale_number }}
                                        </a>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Date de création</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $credit->created_at->format('d/m/Y H:i') }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Client</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <a href="{{ route('customers.show', $credit->customer) }}" class="text-blue-600 hover:text-blue-800">
                                            {{ $credit->customer->name }}
                                        </a>
                                    </p>
                                    @if($credit->customer->phone)
                                    <p class="text-xs text-gray-500">{{ $credit->customer->phone }}</p>
                                    @endif
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Échéance</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        @if($credit->due_date)
                                            {{ $credit->due_date->format('d/m/Y') }}
                                            @if($credit->isOverdue())
                                                <span class="text-red-600 text-xs block">
                                                    ({{ $credit->due_date->diffInDays(now()) }} jours de retard)
                                                </span>
                                            @endif
                                        @else
                                            <span class="text-gray-400">Non définie</span>
                                        @endif
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Montant total</label>
                                    <p class="mt-1 text-lg font-semibold text-gray-900">{{ number_format($credit->total_amount, 0, ',', ' ') }} FCFA</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Montant payé</label>
                                    <p class="mt-1 text-lg font-semibold text-green-600">{{ number_format($credit->amount_paid, 0, ',', ' ') }} FCFA</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Reste à payer</label>
                                    <p class="mt-1 text-lg font-semibold text-red-600">{{ number_format($credit->remaining_balance, 0, ',', ' ') }} FCFA</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Statut</label>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($credit->status === 'active' && $credit->isOverdue()) bg-red-100 text-red-800
                                        @elseif($credit->status === 'active') bg-yellow-100 text-yellow-800
                                        @elseif($credit->status === 'paid') bg-green-100 text-green-800
                                        @elseif($credit->status === 'cancelled') bg-gray-100 text-gray-800
                                        @endif">
                                        @if($credit->status === 'active' && $credit->isOverdue())
                                            <i class="fas fa-exclamation-triangle mr-1"></i>En retard
                                        @else
                                            @switch($credit->status)
                                                @case('active')
                                                    <i class="fas fa-clock mr-1"></i>Actif
                                                    @break
                                                @case('paid')
                                                    <i class="fas fa-check-circle mr-1"></i>Payé
                                                    @break
                                                @case('cancelled')
                                                    <i class="fas fa-times-circle mr-1"></i>Annulé
                                                    @break
                                            @endswitch
                                        @endif
                                    </span>
                                </div>
                            </div>

                            @if($credit->notes)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700">Notes</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $credit->notes }}</p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Résumé et actions -->
                <div class="space-y-6">
                    <!-- Progression du paiement -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Progression du paiement</h3>
                            
                            @php
                                $percentage = $credit->total_amount > 0 ? ($credit->amount_paid / $credit->total_amount) * 100 : 0;
                            @endphp
                            
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>Payé</span>
                                    <span>{{ number_format($percentage, 1) }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: {{ $percentage }}%"></div>
                                </div>
                            </div>

                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Total:</span>
                                    <span class="font-medium">{{ number_format($credit->total_amount, 0, ',', ' ') }} FCFA</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Payé:</span>
                                    <span class="font-medium text-green-600">{{ number_format($credit->amount_paid, 0, ',', ' ') }} FCFA</span>
                                </div>
                                <div class="flex justify-between border-t pt-2">
                                    <span class="text-gray-500">Reste:</span>
                                    <span class="font-medium text-red-600">{{ number_format($credit->remaining_balance, 0, ',', ' ') }} FCFA</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    @if($credit->status === 'active')
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Actions rapides</h3>
                            
                            <div class="space-y-3">
                                <button onclick="openPaymentModal()" 
                                        class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-plus mr-2"></i>Ajouter un paiement
                                </button>

                                <a href="{{ route('credits.payments', $credit) }}" 
                                   class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-history mr-2"></i>Historique des paiements
                                </a>

                                @if($credit->isOverdue())
                                <button class="w-full flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                                    <i class="fas fa-bell mr-2"></i>Envoyer rappel
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Alertes -->
                    @if($credit->isOverdue())
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-exclamation-triangle text-red-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="text-sm font-medium text-red-800">Crédit en retard</h4>
                                <p class="text-sm text-red-700">Ce crédit a {{ $credit->due_date->diffInDays(now()) }} jours de retard.</p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Historique des paiements -->
            @if($credit->payments->count() > 0)
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">
                        <i class="fas fa-history mr-2"></i>Historique des paiements ({{ $credit->payments->count() }})
                    </h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mode</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($credit->payments->sortByDesc('created_at') as $payment)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $payment->created_at->format('d/m/Y H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                                        {{ number_format($payment->amount, 0, ',', ' ') }} FCFA
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $payment->payment_method === 'cash' ? 'bg-green-100 text-green-800' : '' }}
                                            {{ $payment->payment_method === 'mobile_money' ? 'bg-blue-100 text-blue-800' : '' }}
                                            {{ $payment->payment_method === 'bank_transfer' ? 'bg-purple-100 text-purple-800' : '' }}">
                                            @switch($payment->payment_method)
                                                @case('cash') Espèces @break
                                                @case('mobile_money') Mobile Money @break
                                                @case('bank_transfer') Virement @break
                                                @default {{ ucfirst($payment->payment_method) }}
                                            @endswitch
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500">
                                        {{ $payment->notes ?? '-' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $payment->user->name }}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Modal de paiement -->
    @if($credit->status === 'active')
    <div id="payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Ajouter un paiement</h3>
                <form action="{{ route('credits.payment', $credit) }}" method="POST">
                    @csrf
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Montant restant</label>
                        <p class="text-lg font-semibold text-red-600">{{ number_format($credit->remaining_balance, 0, ',', ' ') }} FCFA</p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Montant du paiement</label>
                        <input type="number" name="amount" required min="1" max="{{ $credit->remaining_balance }}" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mode de paiement</label>
                        <select name="payment_method" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="cash">Espèces</option>
                            <option value="mobile_money">Mobile Money</option>
                            <option value="bank_transfer">Virement bancaire</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notes (optionnel)</label>
                        <textarea name="notes" rows="2" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                                  placeholder="Notes sur le paiement..."></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closePaymentModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                            Enregistrer le paiement
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endif

    @push('scripts')
    <script>
        function openPaymentModal() {
            document.getElementById('payment-modal').classList.remove('hidden');
        }

        function closePaymentModal() {
            document.getElementById('payment-modal').classList.add('hidden');
        }

        // Fermer le modal en cliquant à l'extérieur
        document.getElementById('payment-modal')?.addEventListener('click', function(e) {
            if (e.target === this) {
                closePaymentModal();
            }
        });
    </script>
    @endpush
</x-app-layout>
