<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\StockMovement;
use App\Models\Supplier;
use Illuminate\Support\Facades\Auth;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $products = Product::latest()->paginate(20);
        return view('products.index', compact('products'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $suppliers = Supplier::where('is_active', true)->orderBy('name')->get();
        return view('products.create', compact('suppliers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'barcode' => 'nullable|string|unique:products,barcode',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'min_stock_level' => 'required|integer|min:0',
            'unit' => 'required|string|max:50',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'supplier_id' => 'nullable|exists:suppliers,id',
        ]);

        $product = Product::create($request->all());

        // Enregistrer le mouvement de stock initial
        if ($product->stock_quantity > 0) {
            StockMovement::create([
                'product_id' => $product->id,
                'user_id' => Auth::id(),
                'type' => 'in',
                'quantity' => $product->stock_quantity,
                'previous_stock' => 0,
                'new_stock' => $product->stock_quantity,
                'reason' => 'Stock initial',
            ]);
        }

        return redirect()->route('products.index')
            ->with('success', 'Produit créé avec succès.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $product->load(['stockMovements.user']);
        return view('products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $suppliers = Supplier::where('is_active', true)->orderBy('name')->get();
        return view('products.edit', compact('product', 'suppliers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'barcode' => 'nullable|string|unique:products,barcode,' . $product->id,
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'min_stock_level' => 'required|integer|min:0',
            'unit' => 'required|string|max:50',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'supplier_id' => 'nullable|exists:suppliers,id',
        ]);

        $product->update($request->all());

        return redirect()->route('products.index')
            ->with('success', 'Produit mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        $product->delete();

        return redirect()->route('products.index')
            ->with('success', 'Produit supprimé avec succès.');
    }

    /**
     * Adjust stock for a product
     */
    public function adjustStock(Request $request, Product $product)
    {
        $request->validate([
            'adjustment_type' => 'required|in:in,out,adjustment',
            'quantity' => 'required|integer',
            'reason' => 'required|string|max:255',
        ]);

        $previousStock = $product->stock_quantity;
        $quantity = $request->quantity;

        if ($request->adjustment_type === 'out') {
            $quantity = -abs($quantity);
        } elseif ($request->adjustment_type === 'adjustment') {
            $quantity = $quantity - $previousStock;
        }

        $newStock = $previousStock + $quantity;

        if ($newStock < 0) {
            return back()->with('error', 'Stock insuffisant pour cette opération.');
        }

        // Mettre à jour le stock
        $product->update(['stock_quantity' => $newStock]);

        // Enregistrer le mouvement
        StockMovement::create([
            'product_id' => $product->id,
            'user_id' => Auth::id(),
            'type' => $request->adjustment_type,
            'quantity' => $quantity,
            'previous_stock' => $previousStock,
            'new_stock' => $newStock,
            'reason' => $request->reason,
        ]);

        return back()->with('success', 'Stock ajusté avec succès.');
    }
}
