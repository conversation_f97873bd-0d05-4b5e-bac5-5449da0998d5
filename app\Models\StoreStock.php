<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Auth;

class StoreStock extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'store_id',
        'quantity',
        'min_stock_level',
        'max_stock_level',
        'reorder_point',
        'location',
        'is_active',
        'last_counted_at',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'last_counted_at' => 'datetime',
        ];
    }

    /**
     * Relations
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeLowStock($query)
    {
        return $query->whereColumn('quantity', '<=', 'min_stock_level');
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('quantity', '<=', 0);
    }

    public function scopeInStock($query)
    {
        return $query->where('quantity', '>', 0);
    }

    /**
     * Méthodes
     */
    public function hasEnoughStock($quantity): bool
    {
        return $this->quantity >= $quantity;
    }

    public function needsReorder(): bool
    {
        return $this->quantity <= $this->reorder_point;
    }

    public function isLowStock(): bool
    {
        return $this->quantity <= $this->min_stock_level;
    }

    public function isOutOfStock(): bool
    {
        return $this->quantity <= 0;
    }

    public function scopeOverStock($query)
    {
        return $query->whereColumn('quantity', '>', 'max_stock_level')
                    ->whereNotNull('max_stock_level');
    }

    public function scopeNeedsReorder($query)
    {
        return $query->whereColumn('quantity', '<=', 'reorder_point')
                    ->whereNotNull('reorder_point');
    }

    public function scopeForStore($query, $storeId)
    {
        return $query->where('store_id', $storeId);
    }

    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Accessors & Mutators
     */
    public function getIsLowStockAttribute()
    {
        return $this->quantity <= $this->min_stock_level;
    }

    public function getIsOutOfStockAttribute()
    {
        return $this->quantity <= 0;
    }

    public function getIsOverStockAttribute()
    {
        return $this->max_stock_level && $this->quantity > $this->max_stock_level;
    }

    public function getNeedsReorderAttribute()
    {
        return $this->reorder_point && $this->quantity <= $this->reorder_point;
    }

    public function getStockStatusAttribute()
    {
        if ($this->is_out_of_stock) {
            return 'out_of_stock';
        } elseif ($this->is_low_stock) {
            return 'low_stock';
        } elseif ($this->is_over_stock) {
            return 'over_stock';
        } elseif ($this->needs_reorder) {
            return 'needs_reorder';
        } else {
            return 'normal';
        }
    }

    public function getStockStatusColorAttribute()
    {
        return match($this->stock_status) {
            'out_of_stock' => 'red',
            'low_stock' => 'orange',
            'over_stock' => 'purple',
            'needs_reorder' => 'yellow',
            default => 'green'
        };
    }

    public function getDaysUntilReorderAttribute()
    {
        // Calcul basé sur la consommation moyenne (à implémenter selon vos besoins)
        // Pour l'instant, retourne null
        return null;
    }

    /**
     * Methods
     */
    public function hasStock($quantity = 1)
    {
        return $this->quantity >= $quantity;
    }

    public function addStock($quantity, $reason = null)
    {
        $this->increment('quantity', $quantity);
        
        // Créer un mouvement de stock
        StockMovement::create([
            'product_id' => $this->product_id,
            'store_id' => $this->store_id,
            'user_id' => Auth::id(),
            'type' => 'in',
            'quantity' => $quantity,
            'previous_stock' => $this->quantity - $quantity,
            'new_stock' => $this->quantity,
            'reason' => $reason ?? 'Ajout de stock',
        ]);
    }

    public function removeStock($quantity, $reason = null)
    {
        if (!$this->hasStock($quantity)) {
            throw new \Exception('Stock insuffisant');
        }

        $this->decrement('quantity', $quantity);
        
        // Créer un mouvement de stock
        StockMovement::create([
            'product_id' => $this->product_id,
            'store_id' => $this->store_id,
            'user_id' => Auth::id(),
            'type' => 'out',
            'quantity' => $quantity,
            'previous_stock' => $this->quantity + $quantity,
            'new_stock' => $this->quantity,
            'reason' => $reason ?? 'Sortie de stock',
        ]);
    }

    public function adjustStock($newQuantity, $reason = null)
    {
        $previousQuantity = $this->quantity;
        $this->update(['quantity' => $newQuantity]);
        
        // Créer un mouvement de stock
        StockMovement::create([
            'product_id' => $this->product_id,
            'store_id' => $this->store_id,
            'user_id' => Auth::id(),
            'type' => 'adjustment',
            'quantity' => $newQuantity - $previousQuantity,
            'previous_stock' => $previousQuantity,
            'new_stock' => $newQuantity,
            'reason' => $reason ?? 'Ajustement de stock',
        ]);
    }
}
