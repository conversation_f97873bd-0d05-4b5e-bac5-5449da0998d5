<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Store;

class StoreSelection
{
    public function handle(Request $request, Closure $next)
    {
        if (!session()->has('current_store_id')) {
            // Si l'utilisateur n'a qu'un seul magasin assigné, le sélectionner automatiquement
            $stores = auth()->user()->stores;
            if ($stores->count() === 1) {
                session(['current_store_id' => $stores->first()->id]);
            }
        }

        // Vérifier si l'utilisateur a accès au magasin sélectionné
        if (session()->has('current_store_id')) {
            $store = Store::find(session('current_store_id'));
            if (!$store || !auth()->user()->stores->contains($store->id)) {
                session()->forget('current_store_id');
                return redirect()->route('stores.select');
            }
            
            // Partager le magasin courant avec toutes les vues
            view()->share('currentStore', $store);
        }

        return $next($request);
    }
}