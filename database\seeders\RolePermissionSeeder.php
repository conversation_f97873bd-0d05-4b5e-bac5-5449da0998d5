<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer les permissions
        $permissions = [
            // Gestion des produits
            'view_products',
            'create_products',
            'edit_products',
            'delete_products',
            'manage_stock',

            // Gestion des clients
            'view_customers',
            'create_customers',
            'edit_customers',
            'delete_customers',

            // Gestion des ventes
            'create_sales',
            'view_sales',
            'edit_sales',
            'delete_sales',

            // Gestion des crédits
            'view_credits',
            'create_credits',
            'manage_credit_payments',

            // Rapports
            'view_reports',
            'export_reports',

            // Administration
            'manage_users',
            'manage_roles',
            'view_dashboard',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Créer les rôles
        $superAdmin = Role::firstOrCreate(['name' => 'superAdmin']);
        $admin = Role::firstOrCreate(['name' => 'admin']);
        $manager = Role::firstOrCreate(['name' => 'manager']);
        $caissier = Role::firstOrCreate(['name' => 'caissier']);

        // Assigner toutes les permissions au superAdmin
        $superAdmin->givePermissionTo(Permission::all());

        // Permissions pour admin
        $admin->givePermissionTo([
            'view_products', 'create_products', 'edit_products', 'manage_stock', 'delete_products',
            'view_customers', 'create_customers', 'edit_customers', 'delete_customers',
            'create_sales', 'view_sales', 'edit_sales', 'delete_sales',
            'view_credits', 'create_credits', 'manage_credit_payments',
            'view_reports', 'export_reports',
            'view_dashboard'
        ]);

        // Permissions pour manager
        $manager->givePermissionTo([
            'view_products', 'create_products', 'edit_products', 'manage_stock',
            'view_customers', 'create_customers', 'edit_customers',
            'create_sales', 'view_sales',
            'view_credits', 'create_credits', 'manage_credit_payments',
            'view_reports',
        ]);

        // Permissions pour caissier
        $caissier->givePermissionTo([
            'view_products',
            'view_customers', 'create_customers',
            'create_sales', 'view_sales',
            'view_credits', 'manage_credit_payments',
        ]);

        // Créer un super administrateur
        $superAdminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Kader Boukar',
                'password' => Hash::make('kaderboukarsandaBK7'),
                'phone' => '96106528',
                'is_active' => true,
            ]
        );
        $superAdminUser->assignRole('superAdmin');

        // Créer un administrateur
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Ibrahim Boukari',
                'password' => Hash::make('ibou98500487'),
                'phone' => '98500487',
                'is_active' => true,
            ]
        );
        $adminUser->assignRole('admin');
    }
}
