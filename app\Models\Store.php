<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Collection;
use App\Notifications\LowStockAlert;

class Store extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'address',
        'city',
        'phone',
        'email',
        'manager_id',
        'default_tax_rate',
        'currency',
        'is_active',
        'settings',
        'opening_hours',
        'closing_hours',
        'location_coordinates',
    ];

    protected function casts(): array
    {
        return [
            'default_tax_rate' => 'decimal:2',
            'is_active' => 'boolean',
            'settings' => 'array',
            'opening_hours' => 'datetime:H:i',
            'closing_hours' => 'datetime:H:i',
            'location_coordinates' => 'array',
        ];
    }

    /**
     * Relations
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    public function stocks(): HasMany
    {
        return $this->hasMany(StoreStock::class);
    }

    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class);
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function productPrices(): HasMany
    {
        return $this->hasMany(ProductPrice::class);
    }

    public function credits(): HasMany
    {
        return $this->hasManyThrough(Credit::class, Sale::class);
    }

    /**
     * Méthodes de gestion des stocks
     */
    public function getLowStockProducts(): Collection
    {
        return $this->stocks()
            ->whereColumn('quantity', '<=', 'min_stock_level')
            ->with(['product' => function($query) {
                $query->with('productPrices');
            }])
            ->get();
    }

    public function getOutOfStockProducts(): Collection
    {
        return $this->stocks()
            ->where('quantity', '<=', 0)
            ->with(['product' => function($query) {
                $query->with('productPrices');
            }])
            ->get();
    }

    public function getTotalProducts(): int
    {
        return $this->stocks()
            ->where('is_active', true)
            ->count();
    }

    public function hasEnoughStock(int $productId, float $quantity): bool
    {
        $storeStock = $this->stocks()
            ->where('product_id', $productId)
            ->first();

        return $storeStock && $storeStock->quantity >= $quantity;
    }

    public function updateStock(int $productId, float $quantity, string $operation = 'add', ?string $reason = null): bool
    {
        try {
            $storeStock = $this->stocks()
                ->where('product_id', $productId)
                ->first();

            if (!$storeStock) {
                return false;
            }

            $oldQuantity = $storeStock->quantity;

            if ($operation === 'add') {
                $storeStock->quantity += $quantity;
            } else {
                if ($storeStock->quantity < $quantity) {
                    return false;
                }
                $storeStock->quantity -= $quantity;
            }

            $storeStock->save();

            // Enregistrer le mouvement de stock
            StockMovement::create([
                'product_id' => $productId,
                'store_id' => $this->id,
                'quantity' => $quantity,
                'type' => $operation,
                'previous_quantity' => $oldQuantity,
                'new_quantity' => $storeStock->quantity,
                'reason' => $reason,
                'user_id' => auth()->id()
            ]);

            // Vérifier si nous devons envoyer une alerte de stock bas
            if ($storeStock->quantity <= $storeStock->min_stock_level) {
                $this->sendLowStockAlert($storeStock);
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la mise à jour du stock: ' . $e->getMessage());
            return false;
        }
    }

    public function transferStock(
        int $productId, 
        float $quantity, 
        Store $destinationStore, 
        ?string $reason = null
    ): bool {
        try {
            if (!$this->hasEnoughStock($productId, $quantity)) {
                return false;
            }

            \DB::transaction(function() use ($productId, $quantity, $destinationStore, $reason) {
                $this->updateStock($productId, $quantity, 'remove', "Transfert vers " . $destinationStore->name);
                $destinationStore->updateStock($productId, $quantity, 'add', "Transfert depuis " . $this->name);
            });

            return true;
        } catch (\Exception $e) {
            \Log::error('Erreur lors du transfert de stock: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Méthodes de gestion des prix et ventes
     */
    public function getProductPrice(int $productId): ?ProductPrice
    {
        return $this->productPrices()
            ->where('product_id', $productId)
            ->where('is_active', true)
            ->where(function($query) {
                $query->whereNull('effective_until')
                    ->orWhere('effective_until', '>=', now());
            })
            ->latest()
            ->first();
    }

    public function updateProductPrice(
        int $productId, 
        float $sellingPrice, 
        float $costPrice, 
        array $additionalData = []
    ): ProductPrice {
        return $this->productPrices()->create(array_merge([
            'product_id' => $productId,
            'selling_price' => $sellingPrice,
            'cost_price' => $costPrice,
            'effective_from' => now(),
            'is_active' => true
        ], $additionalData));
    }

    /**
     * Méthodes de statistiques
     */
    public function getSalesStats(string $period = 'today'): array
    {
        $query = $this->sales()->where('status', 'completed');
        
        switch($period) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'week':
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
                break;
            case 'year':
                $query->whereYear('created_at', now()->year);
                break;
        }

        return [
            'total_amount' => $query->sum('total_amount'),
            'count' => $query->count(),
            'average' => $query->avg('total_amount') ?? 0,
            'items_sold' => $query->join('sale_items', 'sales.id', '=', 'sale_items.sale_id')
                ->sum('sale_items.quantity')
        ];
    }

    public function getOverdueCredits(): Collection
    {
        return $this->credits()
            ->where('status', 'active')
            ->where('due_date', '<', now())
            ->with(['customer', 'sale'])
            ->get();
    }

    public function getDashboardStats(): array
    {
        return [
            'sales_today' => $this->getSalesStats('today'),
            'sales_month' => $this->getSalesStats('month'),
            'low_stock_count' => $this->getLowStockProducts()->count(),
            'out_of_stock_count' => $this->getOutOfStockProducts()->count(),
            'overdue_credits_count' => $this->getOverdueCredits()->count(),
            'active_customers' => $this->customers()->where('is_active', true)->count()
        ];
    }

    /**
     * Méthodes d'état et de validation
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function isOpen(): bool
    {
        if (!$this->opening_hours || !$this->closing_hours) {
            return true;
        }

        $now = now();
        $opening = Carbon::createFromTimeString($this->opening_hours);
        $closing = Carbon::createFromTimeString($this->closing_hours);

        return $now->between($opening, $closing);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOpen($query)
    {
        return $query->where(function($q) {
            $q->whereNull('opening_hours')
              ->orWhereRaw('TIME(NOW()) BETWEEN opening_hours AND closing_hours');
        });
    }

    /**
     * Accessors & Mutators
     */
    public function getFullAddressAttribute(): string
    {
        return trim($this->address . ', ' . $this->city, ', ');
    }

    /**
     * Méthodes protégées
     */
    protected function sendLowStockAlert($storeStock): void
    {
        try {
            $product = $storeStock->product;
            $manager = $this->manager;

            if ($manager) {
                $manager->notify(new LowStockAlert($this, $product, $storeStock->quantity));
            }

            // Notification au super admin
            User::role('superAdmin')->each(function ($admin) use ($product, $storeStock) {
                $admin->notify(new LowStockAlert($this, $product, $storeStock->quantity));
            });
        } catch (\Exception $e) {
            \Log::error('Erreur lors de l\'envoi de l\'alerte de stock bas: ' . $e->getMessage());
        }
    }

    public function getNotificationSettings(): array
    {
        return $this->settings['notifications'] ?? [
            'low_stock_alerts' => true,
            'credit_alerts' => true,
            'sales_reports' => true
        ];
    }

    public function updateSettings(array $settings): bool
    {
        $this->settings = array_merge($this->settings ?? [], $settings);
        return $this->save();
    }

    // Méthodes utilitaires
    public function getStockLevel(Product $product): int
    {
        return $this->stocks()->where('product_id', $product->id)->value('quantity') ?? 0;
    }

    public function getCurrentPrice(Product $product): float
    {
        return $this->productPrices()
            ->where('product_id', $product->id)
            ->where('is_active', true)
            ->orderBy('effective_date', 'desc')
            ->value('price') ?? 0;
    }

    public function checkLowStock()
    {
        $lowStockItems = $this->stocks()
            ->whereRaw('quantity <= min_quantity')
            ->with('product')
            ->get();

        if ($lowStockItems->isNotEmpty()) {
            $this->manager->notify(new LowStockAlert($lowStockItems));
        }
    }
}
