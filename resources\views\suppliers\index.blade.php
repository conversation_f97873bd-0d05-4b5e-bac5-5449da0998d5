<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-truck mr-3 text-orange-600"></i>Gestion des Fournisseurs
                </h2>
                <p class="text-sm text-gray-600 mt-1">Gérez vos partenaires et fournisseurs</p>
            </div>
            <x-button variant="primary" href="{{ route('suppliers.create') }}" icon="fas fa-plus">
                Nouveau Fournisseur
            </x-button>
        </div>
    </x-slot>

    <div class="py-4 sm:py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- Statistiques -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
                <x-stat-card
                    title="Total fournisseurs"
                    :value="$totalSuppliers"
                    icon="fas fa-truck"
                    color="orange"
                    description="Partenaires enregistrés"
                />

                <x-stat-card
                    title="Fournisseurs actifs"
                    :value="$activeSuppliers"
                    icon="fas fa-check-circle"
                    color="green"
                    :description="'Sur ' . $totalSuppliers . ' total'"
                    :progress="$totalSuppliers > 0 ? ($activeSuppliers / $totalSuppliers) * 100 : 0"
                />

                <x-stat-card
                    title="Solde total"
                    :value="number_format($totalBalance, 0, ',', ' ') . ' FCFA'"
                    icon="fas fa-money-bill"
                    color="red"
                    description="Dettes fournisseurs"
                />
            </div>

            <!-- Messages de succès/erreur -->
            @if(session('success'))
                <x-alert type="success" class="mb-6">
                    {{ session('success') }}
                </x-alert>
            @endif

            @if(session('error'))
                <x-alert type="error" class="mb-6">
                    {{ session('error') }}
                </x-alert>
            @endif

            <!-- Liste des fournisseurs -->
            <x-card title="Liste des fournisseurs" icon="fas fa-truck" padding="p-0">
                @if($suppliers->count() > 0)
                    <x-table
                        searchable="true"
                        :headers="[
                            'Fournisseur',
                            'Contact',
                            'Produits',
                            'Limite crédit',
                            'Solde actuel',
                            'Statut',
                            'Actions'
                        ]"
                        :pagination="$suppliers->links()"
                    >
                        @foreach($suppliers as $supplier)
                        <tr class="hover:bg-gray-50 transition-colors">
                            <!-- Fournisseur -->
                            <td class="px-4 sm:px-6 py-4">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-gradient-to-r from-orange-400 to-orange-600 rounded-xl flex items-center justify-center">
                                            <i class="fas fa-truck text-white text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm font-semibold text-gray-900 truncate">{{ $supplier->name }}</p>
                                        @if($supplier->company_name)
                                        <p class="text-xs text-gray-500 truncate">{{ $supplier->company_name }}</p>
                                        @endif
                                    </div>
                                </div>
                            </td>

                            <!-- Contact -->
                            <td class="px-4 sm:px-6 py-4">
                                <div class="text-sm">
                                    <p class="text-gray-900">{{ $supplier->email ?? 'Non renseigné' }}</p>
                                    <p class="text-gray-500">{{ $supplier->phone ?? 'Non renseigné' }}</p>
                                </div>
                            </td>

                            <!-- Produits -->
                            <td class="px-4 sm:px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ $supplier->products_count }} produit(s)
                                </span>
                            </td>

                            <!-- Limite crédit -->
                            <td class="px-4 sm:px-6 py-4 text-sm text-gray-900">
                                {{ number_format($supplier->credit_limit, 0, ',', ' ') }} FCFA
                            </td>

                            <!-- Solde actuel -->
                            <td class="px-4 sm:px-6 py-4">
                                <span class="text-sm font-medium {{ $supplier->current_balance > 0 ? 'text-red-600' : 'text-green-600' }}">
                                    {{ number_format($supplier->current_balance, 0, ',', ' ') }} FCFA
                                </span>
                            </td>

                            <!-- Statut -->
                            <td class="px-4 sm:px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium
                                    {{ $supplier->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $supplier->is_active ? 'Actif' : 'Inactif' }}
                                </span>
                            </td>

                            <!-- Actions -->
                            <td class="px-4 sm:px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('suppliers.show', $supplier) }}"
                                       class="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
                                       title="Voir">
                                        <i class="fas fa-eye text-sm"></i>
                                    </a>
                                    <a href="{{ route('suppliers.edit', $supplier) }}"
                                       class="p-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-lg transition-colors"
                                       title="Modifier">
                                        <i class="fas fa-edit text-sm"></i>
                                    </a>
                                    <form action="{{ route('suppliers.destroy', $supplier) }}"
                                          method="POST"
                                          class="inline"
                                          onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce fournisseur ?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                                                title="Supprimer">
                                            <i class="fas fa-trash text-sm"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </x-table>
                @else
                    <x-slot name="empty">
                        <div class="text-center py-12">
                            <i class="fas fa-truck text-gray-300 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun fournisseur</h3>
                            <p class="text-gray-500 mb-6">Commencez par ajouter votre premier fournisseur.</p>
                            <x-button variant="primary" href="{{ route('suppliers.create') }}" icon="fas fa-plus">
                                Ajouter un fournisseur
                            </x-button>
                        </div>
                    </x-slot>
                @endif
            </x-card>
        </div>
    </div>
</x-app-layout>
