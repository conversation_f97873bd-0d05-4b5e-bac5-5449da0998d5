<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckReportsAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Vérifier si l'utilisateur a accès aux rapports
        $canViewReports = $user->roles->pluck('name')->intersect(['superAdmin', 'admin', 'manager'])->isNotEmpty();
        
        if (!$canViewReports && !$user->store) {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé aux rapports.');
        }

        return $next($request);
    }
}
