<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'headers' => [],
    'searchable' => false,
    'sortable' => false,
    'pagination' => null,
    'emptyMessage' => 'Aucune donnée disponible',
    'emptyIcon' => 'fas fa-inbox'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'headers' => [],
    'searchable' => false,
    'sortable' => false,
    'pagination' => null,
    'emptyMessage' => 'Aucune donnée disponible',
    'emptyIcon' => 'fas fa-inbox'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div <?php echo e($attributes->merge(['class' => 'bg-white rounded-xl shadow-soft border border-gray-100 overflow-hidden'])); ?>>
    
    <?php if($searchable || isset($filters)): ?>
    <!-- Search and Filters -->
    <div class="p-4 border-b border-gray-100 bg-gray-50">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <?php if($searchable): ?>
            <div class="relative flex-1 max-w-md">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="text" 
                       placeholder="Rechercher..." 
                       class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
            </div>
            <?php endif; ?>
            
            <?php if(isset($filters)): ?>
            <div class="flex items-center space-x-3">
                <?php echo e($filters); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Table -->
    <div class="overflow-x-auto scrollbar-thin">
        <table class="min-w-full divide-y divide-gray-200">
            <?php if(!empty($headers)): ?>
            <thead class="bg-gray-50">
                <tr>
                    <?php $__currentLoopData = $headers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $header): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                        <?php if(is_array($header)): ?>
                            <div class="flex items-center space-x-1">
                                <span><?php echo e($header['label']); ?></span>
                                <?php if($sortable && isset($header['sortable']) && $header['sortable']): ?>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-sort text-xs"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <?php echo e($header); ?>

                        <?php endif; ?>
                    </th>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
            </thead>
            <?php endif; ?>
            
            <tbody class="bg-white divide-y divide-gray-200">
                <?php echo e($slot); ?>

            </tbody>
        </table>
    </div>

    <!-- Empty State -->
    <?php if(isset($empty)): ?>
    <div class="text-center py-12">
        <i class="<?php echo e($emptyIcon); ?> text-gray-300 text-6xl mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2"><?php echo e($emptyMessage); ?></h3>
        <?php echo e($empty); ?>

    </div>
    <?php endif; ?>

    <!-- Pagination -->
    <?php if($pagination): ?>
    <div class="px-6 py-4 border-t border-gray-100 bg-gray-50">
        <?php echo e($pagination); ?>

    </div>
    <?php endif; ?>
</div>
<?php /**PATH D:\UNDP\pos\pos\resources\views/components/table.blade.php ENDPATH**/ ?>