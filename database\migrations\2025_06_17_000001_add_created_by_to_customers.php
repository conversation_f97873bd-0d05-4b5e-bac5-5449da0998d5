<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->foreignId('created_by')
                  ->after('is_active')
                  ->nullable()
                  ->constrained('users')
                  ->nullOnDelete();
        });

        // Mettre à jour les enregistrements existants avec l'ID du premier super admin
        $superAdminId = DB::table('users')
            ->whereIn('id', function($query) {
                $query->select('model_id')
                    ->from('model_has_roles')
                    ->where('role_id', function($q) {
                        $q->select('id')
                            ->from('roles')
                            ->where('name', 'superAdmin');
                    });
            })
            ->value('id');

        if ($superAdminId) {
            DB::table('customers')->update(['created_by' => $superAdminId]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropConstrainedForeignId('created_by');
        });
    }
};
