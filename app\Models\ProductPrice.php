<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class ProductPrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'store_id',
        'cost_price',
        'selling_price',
        'min_selling_price',
        'wholesale_price',
        'tax_rate',
        'is_active',
        'effective_from',
        'effective_until',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'cost_price' => 'decimal:2',
            'selling_price' => 'decimal:2',
            'min_selling_price' => 'decimal:2',
            'wholesale_price' => 'decimal:2',
            'tax_rate' => 'decimal:2',
            'is_active' => 'boolean',
            'effective_from' => 'date',
            'effective_until' => 'date',
        ];
    }

    /**
     * Relations
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function($q) {
                $q->whereNull('effective_until')
                  ->orWhere('effective_until', '>=', Carbon::now());
            })
            ->where(function($q) {
                $q->whereNull('effective_from')
                  ->orWhere('effective_from', '<=', Carbon::now());
            });
    }

    public function scopeCurrentlyValid($query)
    {
        return $query->where(function($q) {
            $q->whereNull('effective_until')
              ->orWhere('effective_until', '>=', Carbon::now());
        })
        ->where(function($q) {
            $q->whereNull('effective_from')
              ->orWhere('effective_from', '<=', Carbon::now());
        });
    }

    /**
     * Méthodes
     */
    public function isCurrentlyValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = Carbon::now();
        
        if ($this->effective_from && $this->effective_from->gt($now)) {
            return false;
        }

        if ($this->effective_until && $this->effective_until->lt($now)) {
            return false;
        }

        return true;
    }

    public function calculateProfit(): float
    {
        return $this->selling_price - $this->cost_price;
    }

    public function calculateProfitMargin(): float
    {
        if ($this->cost_price <= 0) {
            return 0;
        }
        return (($this->selling_price - $this->cost_price) / $this->cost_price) * 100;
    }

    public function calculateTaxAmount(): float
    {
        return $this->selling_price * ($this->tax_rate / 100);
    }

    public function getPriceWithTax(): float
    {
        return $this->selling_price + $this->calculateTaxAmount();
    }

    public function scopeCurrent($query)
    {
        return $query->where('effective_from', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('effective_until')
                          ->orWhere('effective_until', '>=', now());
                    });
    }

    public function scopeForStore($query, $storeId)
    {
        return $query->where('store_id', $storeId);
    }

    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Accessors & Mutators
     */
    public function getProfitMarginAttribute()
    {
        if (!$this->cost_price || $this->cost_price <= 0) {
            return null;
        }

        return round((($this->selling_price - $this->cost_price) / $this->cost_price) * 100, 2);
    }

    public function getProfitAmountAttribute()
    {
        if (!$this->cost_price) {
            return null;
        }

        return $this->selling_price - $this->cost_price;
    }

    public function getIsCurrentAttribute()
    {
        return $this->effective_from <= now() && 
               ($this->effective_until === null || $this->effective_until >= now());
    }

    public function getIsExpiredAttribute()
    {
        return $this->effective_until && $this->effective_until < now();
    }

    /**
     * Methods
     */
    public function calculateProfitForQuantity($quantity)
    {
        if (!$this->cost_price) {
            return null;
        }

        return ($this->selling_price - $this->cost_price) * $quantity;
    }

    public function calculateTotalWithTax($quantity = 1)
    {
        $subtotal = $this->selling_price * $quantity;
        $taxAmount = $subtotal * ($this->tax_rate / 100);
        
        return $subtotal + $taxAmount;
    }

    /**
     * Static methods
     */
    public static function getCurrentPrice($productId, $storeId)
    {
        return static::where('product_id', $productId)
                    ->where('store_id', $storeId)
                    ->active()
                    ->current()
                    ->first();
    }

    public static function getPriceHistory($productId, $storeId)
    {
        return static::where('product_id', $productId)
                    ->where('store_id', $storeId)
                    ->orderBy('effective_from', 'desc')
                    ->get();
    }
}
