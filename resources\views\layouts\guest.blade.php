<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'POS System') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700&display=swap" rel="stylesheet" />

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Custom Styles -->
        <style>
            /* Background gradient animation */
            .animated-bg {
                background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
                background-size: 400% 400%;
                animation: gradientShift 15s ease infinite;
            }

            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            /* Floating animation */
            .floating {
                animation: floating 6s ease-in-out infinite;
            }

            @keyframes floating {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-20px); }
            }

            /* Glass morphism effect */
            .glass {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        </style>
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen animated-bg flex items-center justify-center p-4">
            <!-- Background decorations -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
                <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-white opacity-5 rounded-full floating"></div>
                <div class="absolute top-3/4 right-1/4 w-48 h-48 bg-white opacity-5 rounded-full floating" style="animation-delay: -2s;"></div>
                <div class="absolute top-1/2 left-3/4 w-32 h-32 bg-white opacity-5 rounded-full floating" style="animation-delay: -4s;"></div>
            </div>

            <!-- Main content -->
            <div class="relative w-full max-w-md">
                <!-- Logo and branding -->
                <div class="text-center mb-8">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-white rounded-2xl shadow-2xl mb-4">
                        <i class="fas fa-cash-register text-3xl text-blue-600"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-white mb-2">POS System</h1>
                    <p class="text-white/80 text-sm">Point de Vente Moderne</p>
                </div>

                <!-- Login card -->
                <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl border border-white/20 p-8">
                    {{ $slot }}
                </div>

                <!-- Footer -->
                <div class="text-center mt-8">
                    <p class="text-white/60 text-xs">
                        © {{ date('Y') }} POS System. Tous droits réservés.
                    </p>
                </div>
            </div>
        </div>
    </body>
</html>
