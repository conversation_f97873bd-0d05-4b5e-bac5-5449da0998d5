<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Product;
use App\Models\Customer;

class Promotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type',
        'value',
        'min_quantity',
        'free_quantity',
        'min_amount',
        'start_date',
        'end_date',
        'is_active',
        'applicable_products',
        'applicable_customers',
        'usage_limit',
        'usage_count',
    ];

    protected function casts(): array
    {
        return [
            'value' => 'decimal:2',
            'min_amount' => 'decimal:2',
            'start_date' => 'date',
            'end_date' => 'date',
            'is_active' => 'boolean',
            'applicable_products' => 'array',
            'applicable_customers' => 'array',
        ];
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('end_date', '<', now());
    }

    /**
     * Accessors & Mutators
     */
    public function getIsExpiredAttribute()
    {
        return $this->end_date < now();
    }

    public function getIsValidAttribute()
    {
        return $this->is_active &&
               $this->start_date <= now() &&
               $this->end_date >= now() &&
               ($this->usage_limit === null || $this->usage_count < $this->usage_limit);
    }

    /**
     * Relations
     */
    public function products()
    {
        if ($this->applicable_products) {
            return Product::whereIn('id', $this->applicable_products);
        }
        return Product::query();
    }

    public function customers()
    {
        if ($this->applicable_customers) {
            return Customer::whereIn('id', $this->applicable_customers);
        }
        return Customer::query();
    }

    /**
     * Methods
     */
    public function isApplicableToProduct($productId)
    {
        return $this->applicable_products === null || in_array($productId, $this->applicable_products ?? []);
    }

    public function isApplicableToCustomer($customerId)
    {
        return $this->applicable_customers === null || in_array($customerId, $this->applicable_customers ?? []);
    }

    public function canBeUsed()
    {
        return $this->is_valid &&
               ($this->usage_limit === null || $this->usage_count < $this->usage_limit);
    }

    public function incrementUsage()
    {
        $this->increment('usage_count');
    }
}
