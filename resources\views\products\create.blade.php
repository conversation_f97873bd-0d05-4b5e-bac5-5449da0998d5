<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-plus mr-2"></i>{{ __('Nouveau Produit') }}
            </h2>
            <a href="{{ route('products.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Retour
            </a>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form action="{{ route('products.store') }}" method="POST" class="space-y-6">
                        @csrf
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Section 1: Informations de base -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900 border-b pb-2">
                                    <i class="fas fa-info-circle mr-2"></i>Informations de base
                                </h3>
                                
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nom du produit <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" 
                                           name="name" 
                                           id="name" 
                                           value="{{ old('name') }}"
                                           required
                                           placeholder="ex: Chemise en coton"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                                    @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                        Description
                                    </label>
                                    <textarea name="description" 
                                              id="description" 
                                              rows="3"
                                              placeholder="Décrivez les caractéristiques du produit..."
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('description') border-red-500 @enderror">{{ old('description') }}</textarea>
                                    @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="barcode" class="block text-sm font-medium text-gray-700 mb-2">
                                        Code-barres
                                    </label>
                                    <div class="flex">
                                        <input type="text" 
                                               name="barcode" 
                                               id="barcode" 
                                               value="{{ old('barcode') }}"
                                               placeholder="Scanner ou générer un code-barres"
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('barcode') border-red-500 @enderror">
                                        <button type="button" 
                                                onclick="generateBarcode()"
                                                title="Générer un code-barres aléatoire"
                                                class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700">
                                            <i class="fas fa-random"></i>
                                        </button>
                                    </div>
                                    @error('barcode')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Section 2: Gestion du Stock -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900 border-b pb-2">
                                    <i class="fas fa-boxes mr-2"></i>Gestion du Stock
                                </h3>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label for="unit" class="block text-sm font-medium text-gray-700 mb-2">
                                            Unité de mesure <span class="text-red-500">*</span>
                                        </label>
                                        <select name="unit"
                                                id="unit"
                                                required
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('unit') border-red-500 @enderror">
                                            <option value="">Choisir...</option>
                                            <option value="pcs" {{ old('unit') == 'pcs' ? 'selected' : '' }}>Pièces (pcs)</option>
                                            <option value="kg" {{ old('unit') == 'kg' ? 'selected' : '' }}>Kilogrammes (kg)</option>
                                            <option value="g" {{ old('unit') == 'g' ? 'selected' : '' }}>Grammes (g)</option>
                                            <option value="L" {{ old('unit') == 'L' ? 'selected' : '' }}>Litres (L)</option>
                                            <option value="ml" {{ old('unit') == 'ml' ? 'selected' : '' }}>Millilitres (ml)</option>
                                            <option value="m" {{ old('unit') == 'm' ? 'selected' : '' }}>Mètres (m)</option>
                                            <option value="cm" {{ old('unit') == 'cm' ? 'selected' : '' }}>Centimètres (cm)</option>
                                            <option value="boîte" {{ old('unit') == 'boîte' ? 'selected' : '' }}>Boîtes</option>
                                            <option value="carton" {{ old('unit') == 'carton' ? 'selected' : '' }}>Cartons</option>
                                        </select>
                                        @error('unit')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="stock_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                                            Stock initial <span class="text-red-500">*</span>
                                        </label>
                                        <input type="number" 
                                               name="stock_quantity" 
                                               id="stock_quantity" 
                                               value="{{ old('stock_quantity', 0) }}"
                                               min="0"
                                               required
                                               placeholder="Quantité initiale"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('stock_quantity') border-red-500 @enderror">
                                        @error('stock_quantity')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>

                                <div>
                                    <label for="min_stock_level" class="block text-sm font-medium text-gray-700 mb-2">
                                        Niveau d'alerte stock <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number" 
                                           name="min_stock_level" 
                                           id="min_stock_level" 
                                           value="{{ old('min_stock_level', 5) }}"
                                           min="0"
                                           required
                                           placeholder="Seuil minimal avant alerte"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('min_stock_level') border-red-500 @enderror">
                                    @error('min_stock_level')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-1 text-sm text-gray-500">
                                        Une alerte sera générée quand le stock atteindra ce niveau
                                    </p>
                                </div>

                                <div>
                                    <label for="is_active" class="flex items-center">
                                        <input type="checkbox"
                                               name="is_active"
                                               id="is_active"
                                               value="1"
                                               {{ old('is_active', true) ? 'checked' : '' }}
                                               class="rounded border-gray-300 text-blue-600 focus:ring-2 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Produit actif</span>
                                    </label>
                                    <p class="mt-1 text-sm text-gray-500">
                                        Décochez pour masquer temporairement ce produit
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons de soumission -->
                        <div class="flex justify-end space-x-3 border-t pt-6">
                            <a href="{{ route('products.index') }}" 
                               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Annuler
                            </a>
                            <button type="submit"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <i class="fas fa-save mr-2"></i>Enregistrer le produit
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function generateBarcode() {
            // Générer un code-barres EAN-13 aléatoire
            const prefix = '200'; // Préfixe personnalisé
            const randomDigits = Math.floor(Math.random() * 1000000000).toString().padStart(9, '0');
            const barcode = prefix + randomDigits;
            
            // Calculer le chiffre de contrôle
            let sum = 0;
            for (let i = 0; i < 12; i++) {
                sum += parseInt(barcode[i]) * (i % 2 === 0 ? 1 : 3);
            }
            const checkDigit = (10 - (sum % 10)) % 10;
            
            document.getElementById('barcode').value = barcode + checkDigit;
        }
    </script>
    @endpush
</x-app-layout>
