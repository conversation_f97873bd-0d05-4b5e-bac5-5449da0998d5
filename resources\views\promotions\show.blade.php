<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-tags mr-2"></i>{{ $promotion->name }}
            </h2>
            <div class="flex space-x-2">
                <form action="{{ route('promotions.toggle', $promotion) }}" method="POST" class="inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" 
                            class="{{ $promotion->is_active ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700' }} text-white font-bold py-2 px-4 rounded-lg transition-colors">
                        <i class="fas {{ $promotion->is_active ? 'fa-pause' : 'fa-play' }} mr-2"></i>
                        {{ $promotion->is_active ? 'Désactiver' : 'Activer' }}
                    </button>
                </form>
                <a href="{{ route('promotions.edit', $promotion) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-edit mr-2"></i>Modifier
                </a>
                <a href="{{ route('promotions.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Statut de la promotion -->
            <div class="mb-8">
                @if($promotion->is_expired)
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-times-circle text-red-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="text-sm font-medium text-red-800">Promotion expirée</h4>
                                <p class="text-sm text-red-700">Cette promotion a expiré le {{ $promotion->end_date->format('d/m/Y') }}.</p>
                            </div>
                        </div>
                    </div>
                @elseif($promotion->is_valid)
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-check-circle text-green-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="text-sm font-medium text-green-800">Promotion active</h4>
                                <p class="text-sm text-green-700">Cette promotion est actuellement active et peut être utilisée.</p>
                            </div>
                        </div>
                    </div>
                @elseif($promotion->is_active)
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-clock text-yellow-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-800">Promotion programmée</h4>
                                <p class="text-sm text-yellow-700">Cette promotion débutera le {{ $promotion->start_date->format('d/m/Y') }}.</p>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-pause-circle text-gray-600 mr-3 mt-1"></i>
                            <div>
                                <h4 class="text-sm font-medium text-gray-800">Promotion inactive</h4>
                                <p class="text-sm text-gray-700">Cette promotion est désactivée et ne peut pas être utilisée.</p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- Informations de la promotion -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-6">Détails de la promotion</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Nom</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $promotion->name }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Type</label>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $promotion->type === 'percentage' ? 'bg-blue-100 text-blue-800' : '' }}
                                        {{ $promotion->type === 'fixed_amount' ? 'bg-green-100 text-green-800' : '' }}
                                        {{ $promotion->type === 'buy_x_get_y' ? 'bg-purple-100 text-purple-800' : '' }}">
                                        @switch($promotion->type)
                                            @case('percentage') Pourcentage de remise @break
                                            @case('fixed_amount') Montant fixe de remise @break
                                            @case('buy_x_get_y') Achetez X obtenez Y gratuit @break
                                        @endswitch
                                    </span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Valeur</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        @if($promotion->type === 'percentage')
                                            {{ $promotion->value }}% de remise
                                        @elseif($promotion->type === 'fixed_amount')
                                            {{ number_format($promotion->value, 0, ',', ' ') }} FCFA de remise
                                        @else
                                            Achetez {{ $promotion->min_quantity }}, obtenez {{ $promotion->free_quantity }} gratuit
                                        @endif
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Quantité minimum</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $promotion->min_quantity }}</p>
                                </div>

                                @if($promotion->min_amount > 0)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Montant minimum d'achat</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ number_format($promotion->min_amount, 0, ',', ' ') }} FCFA</p>
                                </div>
                                @endif

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Période de validité</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        Du {{ $promotion->start_date->format('d/m/Y') }} au {{ $promotion->end_date->format('d/m/Y') }}
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Utilisation</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        {{ $promotion->usage_count }} / {{ $promotion->usage_limit ?? '∞' }}
                                        @if($promotion->usage_limit)
                                            @php
                                                $percentage = ($promotion->usage_count / $promotion->usage_limit) * 100;
                                            @endphp
                                            <span class="text-xs text-gray-500">({{ number_format($percentage, 1) }}%)</span>
                                        @endif
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Statut</label>
                                    @if($promotion->is_expired)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times-circle mr-1"></i>Expirée
                                        </span>
                                    @elseif($promotion->is_valid)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>Active
                                        </span>
                                    @elseif($promotion->is_active)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-clock mr-1"></i>Programmée
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i class="fas fa-pause-circle mr-1"></i>Inactive
                                        </span>
                                    @endif
                                </div>
                            </div>

                            @if($promotion->description)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700">Description</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $promotion->description }}</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Produits applicables -->
                    @if($applicableProducts->count() > 0)
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-6">
                                <i class="fas fa-box mr-2"></i>Produits concernés ({{ $applicableProducts->count() }})
                            </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($applicableProducts as $product)
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900">{{ $product->name }}</h4>
                                            <p class="text-xs text-gray-500">{{ number_format($product->price, 0, ',', ' ') }} FCFA</p>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $product->is_active ? 'Actif' : 'Inactif' }}
                                        </span>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Clients concernés -->
                    @if($applicableCustomers->count() > 0)
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-6">
                                <i class="fas fa-users mr-2"></i>Clients concernés ({{ $applicableCustomers->count() }})
                            </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($applicableCustomers as $customer)
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900">{{ $customer->name }}</h4>
                                            @if($customer->email)
                                            <p class="text-xs text-gray-500">{{ $customer->email }}</p>
                                            @endif
                                        </div>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {{ $customer->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $customer->is_active ? 'Actif' : 'Inactif' }}
                                        </span>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Actions rapides -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Actions rapides</h3>
                            
                            <div class="space-y-3">
                                <form action="{{ route('promotions.toggle', $promotion) }}" method="POST">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" 
                                            class="w-full flex items-center justify-center px-4 py-2 {{ $promotion->is_active ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700' }} text-white rounded-lg transition-colors">
                                        <i class="fas {{ $promotion->is_active ? 'fa-pause' : 'fa-play' }} mr-2"></i>
                                        {{ $promotion->is_active ? 'Désactiver' : 'Activer' }}
                                    </button>
                                </form>

                                <a href="{{ route('promotions.edit', $promotion) }}" 
                                   class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-edit mr-2"></i>Modifier la promotion
                                </a>

                                <button class="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                    <i class="fas fa-copy mr-2"></i>Dupliquer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Statistiques d'utilisation -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Statistiques</h3>
                            
                            <div class="space-y-4">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Utilisations:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $promotion->usage_count }}</span>
                                </div>

                                @if($promotion->usage_limit)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Limite:</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $promotion->usage_limit }}</span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Restantes:</span>
                                    <span class="text-sm font-medium text-green-600">{{ $promotion->usage_limit - $promotion->usage_count }}</span>
                                </div>

                                @php
                                    $percentage = ($promotion->usage_count / $promotion->usage_limit) * 100;
                                @endphp
                                
                                <div class="mt-4">
                                    <div class="flex justify-between text-xs text-gray-600 mb-1">
                                        <span>Progression</span>
                                        <span>{{ number_format($percentage, 1) }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full {{ $percentage > 80 ? 'bg-red-600' : ($percentage > 60 ? 'bg-yellow-600' : 'bg-green-600') }}" 
                                             style="width: {{ min($percentage, 100) }}%"></div>
                                    </div>
                                </div>
                                @endif

                                <div class="border-t pt-4">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">Créée le:</span>
                                        <span class="text-sm text-gray-900">{{ $promotion->created_at->format('d/m/Y') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">Modifiée le:</span>
                                        <span class="text-sm text-gray-900">{{ $promotion->updated_at->format('d/m/Y') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
