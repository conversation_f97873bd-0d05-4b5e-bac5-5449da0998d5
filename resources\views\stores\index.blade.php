<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-store mr-3 text-blue-600"></i>Gestion des Magasins
                </h2>
                <p class="text-sm text-gray-600 mt-1">Gérez vos magasins et leurs configurations</p>
            </div>
            <x-button variant="primary" href="{{ route('stores.create') }}" icon="fas fa-plus">
                Nouveau Magasin
            </x-button>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Filtres et recherche -->
            <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                <form method="GET" action="{{ route('stores.index') }}" class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" name="search" value="{{ request('search') }}" 
                               placeholder="Rechercher par nom, code ou adresse..."
                               class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <select name="status" class="border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Tous les statuts</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Actifs</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactifs</option>
                        </select>
                    </div>
                    <x-button type="submit" variant="secondary" icon="fas fa-search">
                        Rechercher
                    </x-button>
                    @if(request()->hasAny(['search', 'status']))
                        <x-button href="{{ route('stores.index') }}" variant="outline" icon="fas fa-times">
                            Réinitialiser
                        </x-button>
                    @endif
                </form>
            </div>

            <!-- Liste des magasins -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                @if($stores->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Magasin
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Responsable
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Utilisateurs
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Statut
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Statistiques
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($stores as $store)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                        <i class="fas fa-store text-blue-600"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ $store->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $store->code }}</div>
                                                    @if($store->address)
                                                        <div class="text-xs text-gray-400">{{ Str::limit($store->address, 30) }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $store->manager_name ?? 'Non défini' }}</div>
                                            @if($store->phone)
                                                <div class="text-sm text-gray-500">{{ $store->phone }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                <i class="fas fa-users mr-1"></i>{{ $store->users_count ?? 0 }} utilisateur(s)
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                {{ $store->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                <i class="fas fa-circle mr-1 text-xs"></i>
                                                {{ $store->is_active ? 'Actif' : 'Inactif' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div class="space-y-1">
                                                <div><i class="fas fa-chart-line mr-1"></i>{{ number_format($store->total_sales ?? 0) }} FCFA</div>
                                                <div><i class="fas fa-box mr-1"></i>{{ $store->total_products ?? 0 }} produits</div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex items-center justify-end space-x-2">
                                                <x-button href="{{ route('stores.show', $store) }}" variant="outline" size="sm" icon="fas fa-eye">
                                                    Voir
                                                </x-button>
                                                <x-button href="{{ route('stores.edit', $store) }}" variant="secondary" size="sm" icon="fas fa-edit">
                                                    Modifier
                                                </x-button>
                                                @if($store->users_count == 0)
                                                    <form method="POST" action="{{ route('stores.destroy', $store) }}" 
                                                          onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce magasin ?')" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <x-button type="submit" variant="danger" size="sm" icon="fas fa-trash">
                                                            Supprimer
                                                        </x-button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($stores->hasPages())
                        <div class="px-6 py-4 border-t border-gray-200">
                            {{ $stores->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-store text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun magasin trouvé</h3>
                        <p class="text-gray-500 mb-6">
                            @if(request()->hasAny(['search', 'status']))
                                Aucun magasin ne correspond à vos critères de recherche.
                            @else
                                Commencez par créer votre premier magasin.
                            @endif
                        </p>
                        @if(!request()->hasAny(['search', 'status']))
                            <x-button href="{{ route('stores.create') }}" variant="primary" icon="fas fa-plus">
                                Créer le premier magasin
                            </x-button>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Statistiques globales -->
            @if($stores->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 bg-blue-100 rounded-full">
                                <i class="fas fa-store text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Magasins</p>
                                <p class="text-2xl font-bold text-gray-900">{{ $stores->total() }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 bg-green-100 rounded-full">
                                <i class="fas fa-check-circle text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Magasins Actifs</p>
                                <p class="text-2xl font-bold text-gray-900">{{ $stores->where('is_active', true)->count() }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 bg-yellow-100 rounded-full">
                                <i class="fas fa-users text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Utilisateurs</p>
                                <p class="text-2xl font-bold text-gray-900">{{ $stores->sum('users_count') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 bg-purple-100 rounded-full">
                                <i class="fas fa-chart-line text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">CA Total</p>
                                <p class="text-2xl font-bold text-gray-900">{{ number_format($stores->sum('total_sales')) }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
