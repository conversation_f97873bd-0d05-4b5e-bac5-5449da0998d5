<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ajouter store_id à la table users
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('store_id')->nullable()->after('is_active')->constrained()->nullOnDelete();
            $table->index('store_id');
        });

        // Ajouter store_id à la table sales
        Schema::table('sales', function (Blueprint $table) {
            $table->foreignId('store_id')->nullable()->after('user_id')->constrained()->nullOnDelete();
            $table->index('store_id');
        });

        // Ajouter store_id à la table stock_movements
        Schema::table('stock_movements', function (Blueprint $table) {
            $table->foreignId('store_id')->nullable()->after('user_id')->constrained()->nullOnDelete();
            $table->index('store_id');
        });

        // Ajouter store_id à la table customers (optionnel - pour associer un client à un magasin principal)
        Schema::table('customers', function (Blueprint $table) {
            $table->foreignId('store_id')->nullable()->after('created_by')->constrained()->nullOnDelete();
            $table->index('store_id');
        });

        // Ajouter des colonnes pour le calcul des bénéfices dans sale_items
        Schema::table('sale_items', function (Blueprint $table) {
            $table->decimal('cost_price', 10, 2)->nullable()->after('unit_price'); // Prix de revient au moment de la vente
            $table->decimal('profit_amount', 10, 2)->nullable()->after('total_price'); // Bénéfice calculé
            $table->decimal('profit_margin', 5, 2)->nullable()->after('profit_amount'); // Marge bénéficiaire en %
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer les colonnes ajoutées aux sale_items
        Schema::table('sale_items', function (Blueprint $table) {
            $table->dropColumn(['cost_price', 'profit_amount', 'profit_margin']);
        });

        // Supprimer store_id des tables
        Schema::table('customers', function (Blueprint $table) {
            $table->dropConstrainedForeignId('store_id');
        });

        Schema::table('stock_movements', function (Blueprint $table) {
            $table->dropConstrainedForeignId('store_id');
        });

        Schema::table('sales', function (Blueprint $table) {
            $table->dropConstrainedForeignId('store_id');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropConstrainedForeignId('store_id');
        });
    }
};
