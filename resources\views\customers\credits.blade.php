<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-credit-card mr-2"></i>Crédits de {{ $customer->name }}
            </h2>
            <a href="{{ route('customers.show', $customer) }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Retour au client
            </a>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Résumé du client -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $customer->credits->count() }}</div>
                            <div class="text-sm text-gray-500">Crédits total</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600">
                                {{ number_format($customer->current_balance, 0, ',', ' ') }} FCFA
                            </div>
                            <div class="text-sm text-gray-500">Solde actuel</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">
                                @if($customer->due_date)
                                    {{ $customer->due_date->format('d/m/Y') }}
                                @else
                                    <span class="text-gray-400">Non définie</span>
                                @endif
                            </div>
                            <div class="text-sm text-gray-500">Date d'échéance</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">
                                @if($customer->due_date && $customer->current_balance > 0)
                                    @if($customer->is_overdue)
                                        <span class="text-red-600">En retard</span>
                                    @elseif($customer->days_until_due <= 7)
                                        <span class="text-orange-600">{{ $customer->days_until_due }} jours</span>
                                    @else
                                        <span class="text-green-600">{{ $customer->days_until_due }} jours</span>
                                    @endif
                                @else
                                    <span class="text-gray-400">-</span>
                                @endif
                            </div>
                            <div class="text-sm text-gray-500">Statut échéance</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des crédits -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">
                        <i class="fas fa-list mr-2"></i>Historique des crédits
                    </h3>
                    
                    @if($customer->credits->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Vente
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date création
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Montant total
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Payé
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Reste
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Échéance
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Statut
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($customer->credits->sortByDesc('created_at') as $credit)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            <a href="{{ route('sales.show', $credit->sale) }}" class="text-blue-600 hover:text-blue-800">
                                                {{ $credit->sale->sale_number }}
                                            </a>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $credit->created_at->format('d/m/Y H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ number_format($credit->total_amount, 0, ',', ' ') }} FCFA
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                        {{ number_format($credit->amount_paid, 0, ',', ' ') }} FCFA
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                                        {{ number_format($credit->remaining_balance, 0, ',', ' ') }} FCFA
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @if($credit->due_date)
                                            {{ $credit->due_date->format('d/m/Y') }}
                                            @if($credit->isOverdue())
                                                <span class="text-red-600 text-xs block">
                                                    ({{ $credit->due_date->diffInDays(now()) }} jours de retard)
                                                </span>
                                            @endif
                                        @else
                                            <span class="text-gray-400">Non définie</span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @switch($credit->status)
                                            @case('active')
                                                @if($credit->isOverdue())
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>En retard
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        <i class="fas fa-clock mr-1"></i>Actif
                                                    </span>
                                                @endif
                                                @break
                                            @case('paid')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle mr-1"></i>Payé
                                                </span>
                                                @break
                                            @case('cancelled')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    <i class="fas fa-times-circle mr-1"></i>Annulé
                                                </span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="{{ route('credits.show', $credit) }}" 
                                               class="text-blue-600 hover:text-blue-900" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($credit->status === 'active')
                                            <button onclick="openPaymentModal({{ $credit->id }}, '{{ $credit->sale->sale_number }}', {{ $credit->remaining_balance }})"
                                                    class="text-green-600 hover:text-green-900" title="Ajouter paiement">
                                                <i class="fas fa-plus-circle"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-8">
                        <i class="fas fa-credit-card text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500">Aucun crédit enregistré pour ce client</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de paiement -->
    <div id="payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Ajouter un paiement</h3>
                <form id="payment-form" method="POST">
                    @csrf
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Vente</label>
                        <p id="sale-number" class="text-sm text-gray-900"></p>
                        <p class="text-sm text-gray-500">Montant restant: <span id="remaining-amount"></span> FCFA</p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Montant du paiement</label>
                        <input type="number" name="amount" required min="1" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mode de paiement</label>
                        <select name="payment_method" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="cash">Espèces</option>
                            <option value="mobile_money">Mobile Money</option>
                            <option value="bank_transfer">Virement bancaire</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notes (optionnel)</label>
                        <textarea name="notes" rows="2" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                                  placeholder="Notes sur le paiement..."></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closePaymentModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                            Enregistrer le paiement
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function openPaymentModal(creditId, saleNumber, remainingAmount) {
            document.getElementById('sale-number').textContent = saleNumber;
            document.getElementById('remaining-amount').textContent = new Intl.NumberFormat('fr-FR').format(remainingAmount);
            document.getElementById('payment-form').action = `/credits/${creditId}/payment`;
            document.querySelector('input[name="amount"]').max = remainingAmount;
            document.getElementById('payment-modal').classList.remove('hidden');
        }

        function closePaymentModal() {
            document.getElementById('payment-modal').classList.add('hidden');
            document.getElementById('payment-form').reset();
        }

        // Fermer le modal en cliquant à l'extérieur
        document.getElementById('payment-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePaymentModal();
            }
        });
    </script>
    @endpush
</x-app-layout>
