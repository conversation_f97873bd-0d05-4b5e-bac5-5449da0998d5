<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport de Stock</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        .stats-grid {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .stats-row {
            display: table-row;
        }
        .stats-cell {
            display: table-cell;
            width: 25%;
            padding: 15px;
            text-align: center;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .stats-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .stats-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin: 30px 0 15px 0;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 11px;
            text-transform: uppercase;
            color: #333;
        }
        td {
            font-size: 11px;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        .badge-normal {
            background-color: #d4edda;
            color: #155724;
        }
        .badge-low {
            background-color: #fff3cd;
            color: #856404;
        }
        .badge-out {
            background-color: #f8d7da;
            color: #721c24;
        }
        .badge-inactive {
            background-color: #e2e3e5;
            color: #383d41;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #888;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .alert-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .alert-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Système POS') }}</div>
        <div class="report-title">Rapport de Stock</div>
        <div class="period">
            Généré le {{ now()->format('d/m/Y à H:i') }}
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="stats-grid">
        <div class="stats-row">
            <div class="stats-cell">
                <div class="stats-value">{{ $totalProducts }}</div>
                <div class="stats-label">Total produits</div>
            </div>
            <div class="stats-cell">
                <div class="stats-value">{{ number_format($totalValue, 0, ',', ' ') }} FCFA</div>
                <div class="stats-label">Valeur du stock</div>
            </div>
            <div class="stats-cell">
                <div class="stats-value">{{ $lowStockCount }}</div>
                <div class="stats-label">Stock faible</div>
            </div>
            <div class="stats-cell">
                <div class="stats-value">{{ $outOfStockCount }}</div>
                <div class="stats-label">Rupture de stock</div>
            </div>
        </div>
    </div>

    <!-- Alertes -->
    @if($lowStockCount > 0 || $outOfStockCount > 0)
    <div class="alert-section">
        <div class="alert-title">⚠️ Alertes de stock</div>
        @if($outOfStockCount > 0)
        <p style="color: #721c24; margin: 5px 0;">
            <strong>{{ $outOfStockCount }} produit(s)</strong> en rupture de stock nécessitent un réapprovisionnement immédiat.
        </p>
        @endif
        @if($lowStockCount > 0)
        <p style="color: #856404; margin: 5px 0;">
            <strong>{{ $lowStockCount }} produit(s)</strong> ont un stock faible et doivent être réapprovisionnés prochainement.
        </p>
        @endif
    </div>
    @endif

    <!-- Détail des produits -->
    <div class="section-title">Détail du stock</div>
    @if($products->count() > 0)
    <table>
        <thead>
            <tr>
                <th>Nom du produit</th>
                <th>Code-barres</th>
                <th class="text-right">Prix vente</th>
                <th class="text-right">Prix achat</th>
                <th class="text-center">Stock actuel</th>
                <th class="text-center">Seuil alerte</th>
                <th class="text-right">Valeur stock</th>
                <th class="text-center">Statut stock</th>
                <th class="text-center">Statut produit</th>
            </tr>
        </thead>
        <tbody>
            @foreach($products as $product)
            @php
                $stockValue = $product->stock_quantity * ($product->cost_price ?? 0);
                $stockStatus = 'Normal';
                $stockBadgeClass = 'badge-normal';
                
                if ($product->stock_quantity <= 0) {
                    $stockStatus = 'Rupture';
                    $stockBadgeClass = 'badge-out';
                } elseif ($product->isLowStock()) {
                    $stockStatus = 'Stock faible';
                    $stockBadgeClass = 'badge-low';
                }
            @endphp
            <tr>
                <td>{{ $product->name }}</td>
                <td>{{ $product->barcode ?? 'Non défini' }}</td>
                <td class="text-right">{{ number_format($product->price, 0, ',', ' ') }} FCFA</td>
                <td class="text-right">{{ number_format($product->cost_price ?? 0, 0, ',', ' ') }} FCFA</td>
                <td class="text-center">{{ $product->stock_quantity }} {{ $product->unit }}</td>
                <td class="text-center">{{ $product->min_stock_level }} {{ $product->unit }}</td>
                <td class="text-right">{{ number_format($stockValue, 0, ',', ' ') }} FCFA</td>
                <td class="text-center">
                    <span class="badge {{ $stockBadgeClass }}">{{ $stockStatus }}</span>
                </td>
                <td class="text-center">
                    <span class="badge {{ $product->is_active ? 'badge-normal' : 'badge-inactive' }}">
                        {{ $product->is_active ? 'Actif' : 'Inactif' }}
                    </span>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @else
    <p style="text-align: center; color: #888; padding: 40px;">Aucun produit trouvé</p>
    @endif

    <!-- Produits nécessitant une attention -->
    @php
        $criticalProducts = $products->filter(function($product) {
            return $product->stock_quantity <= 0 || $product->isLowStock();
        });
    @endphp

    @if($criticalProducts->count() > 0)
    <div class="section-title" style="color: #dc3545;">Produits nécessitant une attention ({{ $criticalProducts->count() }})</div>
    <table>
        <thead>
            <tr>
                <th>Produit</th>
                <th class="text-center">Stock actuel</th>
                <th class="text-center">Seuil d'alerte</th>
                <th class="text-center">Priorité</th>
                <th>Fournisseur</th>
            </tr>
        </thead>
        <tbody>
            @foreach($criticalProducts->sortBy('stock_quantity') as $product)
            <tr>
                <td>{{ $product->name }}</td>
                <td class="text-center">{{ $product->stock_quantity }} {{ $product->unit }}</td>
                <td class="text-center">{{ $product->min_stock_level }} {{ $product->unit }}</td>
                <td class="text-center">
                    @if($product->stock_quantity <= 0)
                        <span class="badge badge-out">URGENT</span>
                    @else
                        <span class="badge badge-low">MOYENNE</span>
                    @endif
                </td>
                <td>{{ $product->supplier ? $product->supplier->name : 'Non défini' }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <!-- Résumé par statut -->
    <div class="section-title">Résumé par statut de stock</div>
    <table style="width: 60%; margin: 0 auto;">
        @php
            $normalStock = $products->filter(function($product) {
                return $product->stock_quantity > $product->min_stock_level;
            })->count();
            
            $lowStock = $products->filter(function($product) {
                return $product->stock_quantity > 0 && $product->stock_quantity <= $product->min_stock_level;
            })->count();
            
            $outOfStock = $products->filter(function($product) {
                return $product->stock_quantity <= 0;
            })->count();
        @endphp
        <tr>
            <td>Stock normal</td>
            <td class="text-right">{{ $normalStock }} produit(s)</td>
            <td class="text-right">{{ number_format(($normalStock / $totalProducts) * 100, 1) }}%</td>
        </tr>
        <tr>
            <td>Stock faible</td>
            <td class="text-right">{{ $lowStock }} produit(s)</td>
            <td class="text-right">{{ number_format(($lowStock / $totalProducts) * 100, 1) }}%</td>
        </tr>
        <tr>
            <td>Rupture de stock</td>
            <td class="text-right">{{ $outOfStock }} produit(s)</td>
            <td class="text-right">{{ number_format(($outOfStock / $totalProducts) * 100, 1) }}%</td>
        </tr>
    </table>

    <div class="footer">
        <p>Rapport généré le {{ now()->format('d/m/Y à H:i') }} par {{ auth()->user()->name }}</p>
        <p>{{ config('app.name') }} - Système de Point de Vente</p>
    </div>
</body>
</html>
