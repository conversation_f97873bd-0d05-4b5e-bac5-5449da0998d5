<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-truck mr-2"></i>{{ $supplier->name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('suppliers.edit', $supplier) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-edit mr-2"></i>Modifier
                </a>
                <a href="{{ route('suppliers.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Statistiques du fournisseur -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-box text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total produits</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $totalProducts }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Produits actifs</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $activeProducts }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-warehouse text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Valeur stock</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($totalStockValue, 0, ',', ' ') }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-credit-card text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Crédit disponible</p>
                                <p class="text-2xl font-semibold text-green-600">{{ number_format($supplier->available_credit, 0, ',', ' ') }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- Informations du fournisseur -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-6">Informations du fournisseur</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Nom</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $supplier->name }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Entreprise</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $supplier->company_name ?? 'Non renseigné' }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Email</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $supplier->email ?? 'Non renseigné' }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Téléphone</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $supplier->phone ?? 'Non renseigné' }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Numéro de TVA</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $supplier->tax_number ?? 'Non renseigné' }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Statut</label>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $supplier->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $supplier->is_active ? 'Actif' : 'Inactif' }}
                                    </span>
                                </div>
                            </div>

                            @if($supplier->address)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700">Adresse</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $supplier->address }}</p>
                            </div>
                            @endif

                            @if($supplier->notes)
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700">Notes</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $supplier->notes }}</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Produits du fournisseur -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-6">
                                <i class="fas fa-box mr-2"></i>Produits récents
                            </h3>
                            
                            @if($supplier->products->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produit</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prix</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($supplier->products as $product)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">{{ $product->name }}</div>
                                                @if($product->barcode)
                                                <div class="text-sm text-gray-500">{{ $product->barcode }}</div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ number_format($product->price, 0, ',', ' ') }} FCFA
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="text-sm {{ $product->isLowStock() ? 'text-red-600 font-medium' : 'text-gray-900' }}">
                                                    {{ $product->stock_quantity }} {{ $product->unit }}
                                                </span>
                                                @if($product->isLowStock())
                                                <span class="block text-xs text-red-500">Stock faible</span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                    {{ $product->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                    {{ $product->is_active ? 'Actif' : 'Inactif' }}
                                                </span>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            
                            @if($totalProducts > 10)
                            <div class="mt-4 text-center">
                                <a href="{{ route('products.index', ['supplier' => $supplier->id]) }}" 
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    Voir tous les produits ({{ $totalProducts }})
                                </a>
                            </div>
                            @endif
                            @else
                            <div class="text-center py-8">
                                <i class="fas fa-box text-gray-300 text-4xl mb-4"></i>
                                <p class="text-gray-500">Aucun produit associé à ce fournisseur</p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Actions rapides -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Actions rapides</h3>
                            
                            <div class="space-y-3">
                                <a href="{{ route('products.create', ['supplier' => $supplier->id]) }}" 
                                   class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-plus mr-2"></i>Ajouter un produit
                                </a>

                                <a href="{{ route('suppliers.edit', $supplier) }}" 
                                   class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-edit mr-2"></i>Modifier le fournisseur
                                </a>

                                <button class="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                    <i class="fas fa-envelope mr-2"></i>Envoyer email
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Informations financières -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Informations financières</h3>
                            
                            <div class="space-y-4">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Limite de crédit</span>
                                    <span class="text-sm font-medium text-gray-900">
                                        {{ number_format($supplier->credit_limit, 0, ',', ' ') }} FCFA
                                    </span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Solde actuel</span>
                                    <span class="text-sm font-medium {{ $supplier->current_balance > 0 ? 'text-red-600' : 'text-green-600' }}">
                                        {{ number_format($supplier->current_balance, 0, ',', ' ') }} FCFA
                                    </span>
                                </div>

                                <div class="flex justify-between border-t pt-2">
                                    <span class="text-sm text-gray-500">Crédit disponible</span>
                                    <span class="text-sm font-medium text-green-600">
                                        {{ number_format($supplier->available_credit, 0, ',', ' ') }} FCFA
                                    </span>
                                </div>

                                @php
                                    $percentage = $supplier->credit_limit > 0 ? ($supplier->current_balance / $supplier->credit_limit) * 100 : 0;
                                @endphp
                                
                                <div class="mt-4">
                                    <div class="flex justify-between text-xs text-gray-600 mb-1">
                                        <span>Utilisation du crédit</span>
                                        <span>{{ number_format($percentage, 1) }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full {{ $percentage > 80 ? 'bg-red-600' : ($percentage > 60 ? 'bg-yellow-600' : 'bg-green-600') }}" 
                                             style="width: {{ min($percentage, 100) }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations système -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Informations système</h3>
                            
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Créé le:</span>
                                    <span class="text-gray-900">{{ $supplier->created_at->format('d/m/Y') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Modifié le:</span>
                                    <span class="text-gray-900">{{ $supplier->updated_at->format('d/m/Y') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
