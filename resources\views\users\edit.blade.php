<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-user-edit mr-3 text-blue-600"></i>Modifier l'Utilisateur
                </h2>
                <p class="text-sm text-gray-600 mt-1">{{ $user->name }} - {{ $user->email }}</p>
            </div>
            <div class="flex space-x-2">
                <x-button variant="secondary" href="{{ route('users.show', $user) }}" icon="fas fa-eye">
                    Voir
                </x-button>
                <x-button variant="secondary" href="{{ route('users.index') }}" icon="fas fa-arrow-left">
                    Retour
                </x-button>
            </div>
        </div>
    </x-slot>

    <div class="py-4 sm:py-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <x-card title="Modifier les informations" icon="fas fa-user-edit">
                <form action="{{ route('users.update', $user) }}" method="POST" class="space-y-6">
                    @csrf
                    @method('PUT')
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Section Informations personnelles -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                                <i class="fas fa-id-card mr-2 text-blue-600"></i>Informations personnelles
                            </h3>
                        </div>

                        <!-- Nom -->
                        <x-form-field 
                            label="Nom complet" 
                            name="name" 
                            :required="true"
                            icon="fas fa-user"
                            placeholder="Nom et prénom de l'utilisateur"
                            :value="old('name', $user->name)"
                        />

                        <!-- Email -->
                        <x-form-field 
                            label="Adresse email" 
                            name="email" 
                            type="email"
                            :required="true"
                            icon="fas fa-envelope"
                            placeholder="<EMAIL>"
                            :value="old('email', $user->email)"
                            help="L'email servira d'identifiant de connexion"
                        />

                        <!-- Téléphone -->
                        <x-form-field 
                            label="Téléphone" 
                            name="phone" 
                            icon="fas fa-phone"
                            placeholder="+33 1 23 45 67 89"
                            :value="old('phone', $user->phone)"
                        />

                        <!-- Rôle -->
                        <x-form-field 
                            label="Rôle" 
                            name="role" 
                            type="select"
                            :required="true"
                            icon="fas fa-user-tag"
                            :options="$roles->pluck('name', 'name')->toArray()"
                            :value="old('role', $user->roles->first()?->name)"
                            help="Détermine les permissions de l'utilisateur"
                        />

                        <!-- Section Sécurité -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                                <i class="fas fa-shield-alt mr-2 text-green-600"></i>Sécurité et accès
                            </h3>
                        </div>

                        <!-- Mot de passe -->
                        <x-form-field 
                            label="Nouveau mot de passe" 
                            name="password" 
                            type="password"
                            icon="fas fa-lock"
                            placeholder="••••••••"
                            help="Laissez vide pour conserver le mot de passe actuel"
                        />

                        <!-- Confirmation mot de passe -->
                        <x-form-field 
                            label="Confirmer le nouveau mot de passe" 
                            name="password_confirmation" 
                            type="password"
                            icon="fas fa-lock"
                            placeholder="••••••••"
                            help="Requis uniquement si vous changez le mot de passe"
                        />

                        <!-- Section Paramètres -->
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                                <i class="fas fa-cog mr-2 text-purple-600"></i>Paramètres du compte
                            </h3>
                        </div>

                        <!-- Statut -->
                        <div class="lg:col-span-2">
                            <x-form-field 
                                label="Statut du compte" 
                                name="is_active" 
                                type="checkbox"
                                placeholder="Compte actif"
                                :value="old('is_active', $user->is_active)"
                                help="Les comptes inactifs ne peuvent pas se connecter"
                            />
                        </div>
                    </div>

                    <!-- Informations sur le compte -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">
                            <i class="fas fa-info-circle mr-2"></i>Informations du compte
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Statut :</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        <i class="fas {{ $user->is_active ? 'fa-check-circle' : 'fa-times-circle' }} mr-1"></i>
                                        {{ $user->is_active ? 'Actif' : 'Inactif' }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Rôle :</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-user-tag mr-1"></i>
                                        {{ $user->roles->first()?->name ?? 'Aucun' }}
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Email vérifié :</span>
                                    @if($user->email_verified_at)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Vérifié
                                    </span>
                                    @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        Non vérifié
                                    </span>
                                    @endif
                                </div>
                            </div>
                            <div class="space-y-2 text-sm text-gray-600">
                                <p><strong>Créé le :</strong> {{ $user->created_at->format('d/m/Y à H:i') }}</p>
                                <p><strong>Dernière modification :</strong> {{ $user->updated_at->format('d/m/Y à H:i') }}</p>
                                @if($user->email_verified_at)
                                <p><strong>Email vérifié le :</strong> {{ $user->email_verified_at->format('d/m/Y à H:i') }}</p>
                                @endif
                                @if($user->sales()->count() > 0)
                                <p><strong>Nombre de ventes :</strong> {{ number_format($user->sales()->count()) }}</p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    @if($user->id !== auth()->id())
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-yellow-900 mb-3">
                            <i class="fas fa-bolt mr-2"></i>Actions rapides
                        </h4>
                        <div class="flex flex-wrap gap-3">
                            <form action="{{ route('users.reset-password', $user) }}" method="POST" class="inline" onsubmit="return confirmPasswordReset()">
                                @csrf
                                @method('PATCH')
                                <x-button
                                    type="submit"
                                    variant="warning"
                                    size="sm"
                                    icon="fas fa-key"
                                >
                                    Réinitialiser le mot de passe
                                </x-button>
                            </form>

                            <form action="{{ route('users.toggle', $user) }}" method="POST" class="inline" onsubmit="return confirmToggleStatus('{{ $user->is_active ? 'désactiver' : 'activer' }}', '{{ $user->name }}')">
                                @csrf
                                @method('PATCH')
                                <x-button
                                    type="submit"
                                    variant="{{ $user->is_active ? 'warning' : 'success' }}"
                                    size="sm"
                                    icon="fas {{ $user->is_active ? 'fa-user-slash' : 'fa-user-check' }}"
                                >
                                    {{ $user->is_active ? 'Désactiver' : 'Activer' }} le compte
                                </x-button>
                            </form>
                        </div>
                    </div>
                    @endif

                    <!-- Boutons d'action -->
                    <div class="flex flex-col sm:flex-row sm:justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                        <x-button variant="secondary" href="{{ route('users.show', $user) }}" icon="fas fa-times">
                            Annuler
                        </x-button>
                        <x-button type="submit" variant="primary" icon="fas fa-save">
                            Mettre à jour
                        </x-button>
                    </div>
                </form>
            </x-card>
        </div>
    </div>

    @push('scripts')
    <script>
        // JavaScript simplifié et fonctionnel
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ Script utilisateur chargé');

            // Validation simple des mots de passe
            const passwordField = document.querySelector('input[name="password"]');
            const passwordConfirmField = document.querySelector('input[name="password_confirmation"]');

            if (passwordField && passwordConfirmField) {
                function validatePasswords() {
                    if (passwordField.value && passwordConfirmField.value) {
                        if (passwordField.value !== passwordConfirmField.value) {
                            passwordConfirmField.setCustomValidity('Les mots de passe ne correspondent pas');
                        } else {
                            passwordConfirmField.setCustomValidity('');
                        }
                    } else {
                        passwordConfirmField.setCustomValidity('');
                    }
                }

                passwordField.addEventListener('input', validatePasswords);
                passwordConfirmField.addEventListener('input', validatePasswords);
            }
        });

        // Confirmation pour la réinitialisation du mot de passe
        function confirmPasswordReset() {
            return confirm('Êtes-vous sûr de vouloir réinitialiser le mot de passe ?\n\nLe nouveau mot de passe sera : password123\n\nL\'utilisateur devra le changer lors de sa prochaine connexion.');
        }

        // Confirmation pour changer le statut
        function confirmToggleStatus(action, userName) {
            return confirm(`Êtes-vous sûr de vouloir ${action} le compte de "${userName}" ?\n\nCette action prendra effet immédiatement.`);
        }
    </script>
    @endpush
</x-app-layout>
