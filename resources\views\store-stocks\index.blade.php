<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-boxes mr-3 text-blue-600"></i>Gestion des Stocks
                    @if($store)
                        <span class="text-lg text-blue-600 font-medium">- {{ $store->name }}</span>
                    @endif
                </h2>
                <p class="text-sm text-gray-600 mt-1">Gérez les stocks de vos produits par magasin</p>
            </div>
            <div class="flex space-x-2">
                <x-button href="{{ route('store-stocks.create', ['store_id' => request('store_id')]) }}" 
                         variant="primary" icon="fas fa-plus">
                    Ajouter Stock
                </x-button>
                <x-button onclick="openBulkStockModal()" variant="secondary" icon="fas fa-layer-group">
                    Actions en lot
                </x-button>
                @if($store)
                    <x-button href="{{ route('stores.show', $store) }}" variant="outline" icon="fas fa-store">
                        Retour au magasin
                    </x-button>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Alertes de stock -->
            @if($criticalStocks->count() > 0)
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                        <h3 class="text-lg font-medium text-red-800">
                            {{ $criticalStocks->count() }} produit(s) en rupture de stock
                        </h3>
                    </div>
                    <p class="text-red-700 mt-1">Ces produits nécessitent un réapprovisionnement urgent.</p>
                </div>
            @endif

            @if($lowStocks->count() > 0)
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-yellow-600 mr-2"></i>
                        <h3 class="text-lg font-medium text-yellow-800">
                            {{ $lowStocks->count() }} produit(s) en stock faible
                        </h3>
                    </div>
                    <p class="text-yellow-700 mt-1">Ces produits approchent du seuil minimum.</p>
                </div>
            @endif

            <!-- Filtres et recherche -->
            <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                <form method="GET" action="{{ route('store-stocks.index') }}" class="space-y-4">
                    @if(request('store_id'))
                        <input type="hidden" name="store_id" value="{{ request('store_id') }}">
                    @endif
                    
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Rechercher un produit..."
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        @if(!request('store_id'))
                            <div>
                                <select name="store_id" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Tous les magasins</option>
                                    @foreach($stores as $storeOption)
                                        <option value="{{ $storeOption->id }}" {{ request('store_id') == $storeOption->id ? 'selected' : '' }}>
                                            {{ $storeOption->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        @endif
                        
                        <div>
                            <select name="status" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Tous les statuts</option>
                                <option value="in_stock" {{ request('status') === 'in_stock' ? 'selected' : '' }}>En stock</option>
                                <option value="low_stock" {{ request('status') === 'low_stock' ? 'selected' : '' }}>Stock faible</option>
                                <option value="out_of_stock" {{ request('status') === 'out_of_stock' ? 'selected' : '' }}>Rupture</option>
                                <option value="overstock" {{ request('status') === 'overstock' ? 'selected' : '' }}>Surstock</option>
                            </select>
                        </div>
                        
                        <div>
                            <select name="category" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Toutes catégories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="flex space-x-2">
                            <x-button type="submit" variant="secondary" icon="fas fa-search" class="flex-1">
                                Filtrer
                            </x-button>
                            @if(request()->hasAny(['search', 'store_id', 'status', 'category']))
                                <x-button href="{{ route('store-stocks.index') }}" variant="outline" icon="fas fa-times">
                                    Reset
                                </x-button>
                            @endif
                        </div>
                    </div>
                </form>
            </div>

            <!-- Statistiques rapides -->
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-full">
                            <i class="fas fa-boxes text-blue-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-600">Total Produits</p>
                            <p class="text-xl font-bold text-gray-900">{{ $totalProducts }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-full">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-600">En Stock</p>
                            <p class="text-xl font-bold text-gray-900">{{ $inStockCount }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-full">
                            <i class="fas fa-exclamation-circle text-yellow-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-600">Stock Faible</p>
                            <p class="text-xl font-bold text-gray-900">{{ $lowStockCount }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="p-2 bg-red-100 rounded-full">
                            <i class="fas fa-times-circle text-red-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-600">Rupture</p>
                            <p class="text-xl font-bold text-gray-900">{{ $outOfStockCount }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-full">
                            <i class="fas fa-coins text-purple-600"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-600">Valeur Stock</p>
                            <p class="text-xl font-bold text-gray-900">{{ number_format($totalStockValue) }} FCFA</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des stocks -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                @if($storeStocks->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" id="selectAll" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Produit
                                    </th>
                                    @if(!request('store_id'))
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Magasin
                                        </th>
                                    @endif
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Stock Actuel
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Seuils
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Valeur
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Emplacement
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Statut
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($storeStocks as $stock)
                                    <tr class="hover:bg-gray-50 {{ $stock->getStatus() === 'out_of_stock' ? 'bg-red-50' : ($stock->getStatus() === 'low_stock' ? 'bg-yellow-50' : '') }}">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" name="selected_stocks[]" value="{{ $stock->id }}" 
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded stock-checkbox">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    @if($stock->product->image)
                                                        <img class="h-10 w-10 rounded-full object-cover" src="{{ $stock->product->image }}" alt="">
                                                    @else
                                                        <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                            <i class="fas fa-box text-gray-600"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ $stock->product->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $stock->product->barcode }}</div>
                                                    @if($stock->product->category)
                                                        <div class="text-xs text-blue-600">{{ $stock->product->category->name }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        @if(!request('store_id'))
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $stock->store->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $stock->store->code }}</div>
                                            </td>
                                        @endif
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            <div class="text-lg font-bold {{ $stock->quantity <= 0 ? 'text-red-600' : ($stock->quantity <= $stock->min_stock_level ? 'text-yellow-600' : 'text-gray-900') }}">
                                                {{ $stock->quantity }}
                                            </div>
                                            <div class="text-xs text-gray-500">{{ $stock->product->unit ?? 'unité(s)' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-500">
                                            <div>Min: {{ $stock->min_stock_level }}</div>
                                            @if($stock->max_stock_level)
                                                <div>Max: {{ $stock->max_stock_level }}</div>
                                            @endif
                                            @if($stock->reorder_point)
                                                <div class="text-blue-600">Réappro: {{ $stock->reorder_point }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            @php
                                                $price = $stock->product->getCurrentPrice($stock->store_id);
                                                $value = $price ? $stock->quantity * $price->cost_price : 0;
                                            @endphp
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ number_format($value, 0, ',', ' ') }} FCFA
                                            </div>
                                            @if($price)
                                                <div class="text-xs text-gray-500">
                                                    {{ number_format($price->cost_price, 0, ',', ' ') }} FCFA/unité
                                                </div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $stock->location ?: 'Non défini' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            @php $status = $stock->getStatus(); @endphp
                                            @if($status === 'out_of_stock')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i class="fas fa-times-circle mr-1 text-xs"></i>Rupture
                                                </span>
                                            @elseif($status === 'low_stock')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    <i class="fas fa-exclamation-triangle mr-1 text-xs"></i>Stock faible
                                                </span>
                                            @elseif($status === 'overstock')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                    <i class="fas fa-arrow-up mr-1 text-xs"></i>Surstock
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle mr-1 text-xs"></i>En stock
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex items-center justify-end space-x-1">
                                                <x-button onclick="openQuickStockModal({{ $stock->id }}, '{{ $stock->product->name }}', {{ $stock->quantity }})" 
                                                         variant="outline" size="sm" icon="fas fa-plus-minus" title="Ajustement rapide">
                                                </x-button>
                                                <x-button href="{{ route('store-stocks.show', $stock) }}" 
                                                         variant="outline" size="sm" icon="fas fa-eye" title="Voir détails">
                                                </x-button>
                                                <x-button href="{{ route('store-stocks.edit', $stock) }}" 
                                                         variant="secondary" size="sm" icon="fas fa-edit" title="Modifier">
                                                </x-button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($storeStocks->hasPages())
                        <div class="px-6 py-4 border-t border-gray-200">
                            {{ $storeStocks->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-boxes text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun stock trouvé</h3>
                        <p class="text-gray-500 mb-6">
                            @if(request()->hasAny(['search', 'store_id', 'status', 'category']))
                                Aucun stock ne correspond à vos critères de recherche.
                            @else
                                Commencez par ajouter des stocks à vos produits.
                            @endif
                        </p>
                        @if(!request()->hasAny(['search', 'store_id', 'status', 'category']))
                            <x-button href="{{ route('store-stocks.create', ['store_id' => request('store_id')]) }}" 
                                     variant="primary" icon="fas fa-plus">
                                Ajouter le premier stock
                            </x-button>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Modal d'ajustement rapide -->
    <div id="quickStockModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <form id="quickStockForm" method="POST">
                    @csrf
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            Ajustement rapide du stock
                        </h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Produit</label>
                                <p id="productName" class="text-gray-900 font-medium"></p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Stock actuel</label>
                                <p id="currentStock" class="text-lg font-bold text-gray-900"></p>
                            </div>
                            
                            <div>
                                <label for="adjustment_type" class="block text-sm font-medium text-gray-700 mb-1">Type d'ajustement</label>
                                <select id="adjustment_type" name="adjustment_type" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="add">Ajouter au stock</option>
                                    <option value="remove">Retirer du stock</option>
                                    <option value="set">Définir le stock</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">Quantité</label>
                                <input type="number" id="quantity" name="quantity" min="0" step="1" required
                                       class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            
                            <div>
                                <label for="reason" class="block text-sm font-medium text-gray-700 mb-1">Motif</label>
                                <select id="reason" name="reason" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="restock">Réapprovisionnement</option>
                                    <option value="sale">Vente</option>
                                    <option value="damage">Produit endommagé</option>
                                    <option value="theft">Vol</option>
                                    <option value="inventory">Inventaire</option>
                                    <option value="other">Autre</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes (optionnel)</label>
                                <textarea id="notes" name="notes" rows="2" 
                                         class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                         placeholder="Notes sur cet ajustement..."></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-2">
                        <button type="button" onclick="closeQuickStockModal()" 
                               class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Annuler
                        </button>
                        <button type="submit" 
                               class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">
                            Ajuster le stock
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            // Gestion de la sélection multiple
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.stock-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            function openQuickStockModal(stockId, productName, currentStock) {
                document.getElementById('productName').textContent = productName;
                document.getElementById('currentStock').textContent = currentStock;
                document.getElementById('quickStockForm').action = `/store-stocks/${stockId}/adjust`;
                document.getElementById('quickStockModal').classList.remove('hidden');
                document.getElementById('quantity').focus();
            }
            
            function closeQuickStockModal() {
                document.getElementById('quickStockModal').classList.add('hidden');
                document.getElementById('quickStockForm').reset();
            }
            
            function openBulkStockModal() {
                alert('Fonctionnalité d\'actions en lot à implémenter');
            }
            
            // Fermer le modal en cliquant à l'extérieur
            document.getElementById('quickStockModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeQuickStockModal();
                }
            });
        </script>
    @endpush
</x-app-layout>
