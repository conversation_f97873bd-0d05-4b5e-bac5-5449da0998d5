<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Notifications\CreditOverdueAlert;
use App\Notifications\CreditDueSoonAlert;
use Carbon\Carbon;

class Credit extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'sale_id',
        'customer_id',
        'store_id',
        'total_amount',
        'amount_paid',
        'remaining_balance',
        'due_date',
        'status', // 'pending', 'partial', 'paid', 'overdue', 'cancelled'
        'notes',
        'payment_terms', // conditions de paiement
        'created_by',
        'last_payment_date',
    ];

    protected function casts(): array
    {
        return [
            'total_amount' => 'decimal:2',
            'amount_paid' => 'decimal:2',
            'remaining_balance' => 'decimal:2',
            'due_date' => 'date',
            'last_payment_date' => 'datetime',
            'deleted_at' => 'datetime',
        ];
    }

    // Relations améliorées
    public function sale()
    {
        return $this->belongsTo(Sale::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function payments()
    {
        return $this->hasMany(CreditPayment::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Méthodes utilitaires
    public function addPayment($amount, $paymentMethod = 'cash', $reference = null)
    {
        if ($amount > $this->remaining_balance) {
            throw new \Exception("Le montant du paiement dépasse le solde restant");
        }

        $payment = $this->payments()->create([
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'reference' => $reference,
            'user_id' => auth()->id()
        ]);

        $this->amount_paid += $amount;
        $this->remaining_balance = $this->total_amount - $this->amount_paid;
        $this->last_payment_date = now();
        $this->updateStatus();

        // Mettre à jour le solde du client
        $this->customer->updateBalance();

        return $payment;
    }

    public function updateStatus()
    {
        $oldStatus = $this->status;

        if ($this->remaining_balance <= 0) {
            $this->status = 'paid';
        } elseif ($this->due_date < now()) {
            $this->status = 'overdue';
        } elseif ($this->amount_paid > 0) {
            $this->status = 'partial';
        } else {
            $this->status = 'pending';
        }

        if ($oldStatus !== $this->status) {
            $this->notifyStatusChange($oldStatus);
        }

        $this->save();
    }

    public function notifyStatusChange($oldStatus)
    {
        if ($this->status === 'overdue') {
            $this->customer->notify(new CreditOverdueAlert($this));
        } elseif ($this->status === 'pending' && $this->daysUntilDue <= 7) {
            $this->customer->notify(new CreditDueSoonAlert($this));
        }
    }

    // Scopes
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->where('status', '!=', 'paid');
    }

    public function scopeDueSoon($query, $days = 7)
    {
        return $query->whereBetween('due_date', [now(), now()->addDays($days)])
                    ->where('status', '!=', 'paid');
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Accesseurs
    public function getDaysUntilDueAttribute()
    {
        return now()->diffInDays($this->due_date, false);
    }

    public function getIsOverdueAttribute()
    {
        return $this->status === 'overdue';
    }

    public function getPaymentProgressAttribute()
    {
        return ($this->amount_paid / $this->total_amount) * 100;
    }
}
