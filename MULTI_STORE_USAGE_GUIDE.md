# Guide d'utilisation du système multi-magasins

## Vue d'ensemble

Le système POS a été évolué pour supporter plusieurs magasins avec une gestion indépendante des stocks, prix et calcul des bénéfices par magasin.

## Fonctionnalités principales

### 1. Gestion des magasins
- Création et gestion de plusieurs magasins
- Assignation des utilisateurs à des magasins spécifiques
- Configuration individuelle par magasin (taux de taxe, devise, etc.)

### 2. Gestion des stocks par magasin
- Stock indépendant pour chaque produit dans chaque magasin
- Seuils de stock personnalisables par magasin
- Transferts de stock entre magasins
- Historique des mouvements de stock par magasin

### 3. Gestion des prix par magasin
- Prix de vente et coût d'achat différents par magasin
- Historique des prix avec dates d'effet
- Prix de gros, prix minimum, prix de détail
- Copie de prix entre magasins

### 4. Calcul des bénéfices
- Calcul automatique du bénéfice par article vendu
- Marge bénéficiaire en pourcentage
- Rapports de bénéfices par magasin
- Suivi des performances par vendeur et par magasin

## Utilisation

### Configuration initiale

1. **Exécuter les migrations** :
```bash
php artisan migrate
```

2. **Peupler avec des données de test** :
```bash
php artisan db:seed
```

### Gestion des magasins

#### Créer un nouveau magasin
1. Accéder à "Gestion des magasins" (réservé aux super admins)
2. Cliquer sur "Nouveau magasin"
3. Remplir les informations : nom, code, adresse, responsable, etc.
4. Définir le taux de taxe par défaut et la devise

#### Assigner des utilisateurs à un magasin
1. Dans la fiche du magasin, section "Utilisateurs"
2. Sélectionner les utilisateurs à assigner
3. Valider l'assignation

#### Initialiser un magasin avec les données d'un autre
1. Dans la fiche du nouveau magasin
2. Utiliser "Initialiser depuis un autre magasin"
3. Sélectionner le magasin source
4. Les prix seront copiés, les stocks initialisés à 0

### Gestion des prix

#### Définir les prix pour un produit dans un magasin
1. Accéder à "Gestion des prix"
2. Sélectionner le magasin (si super admin)
3. Cliquer sur "Nouveau prix"
4. Remplir :
   - Produit
   - Prix de coût (pour calcul du bénéfice)
   - Prix de vente
   - Prix minimum (optionnel)
   - Prix de gros (optionnel)
   - Date d'effet

#### Copier les prix entre magasins
1. Dans "Gestion des prix"
2. Utiliser "Copier les prix"
3. Sélectionner magasin source et destination
4. Seuls les produits sans prix dans la destination seront copiés

### Gestion des stocks

#### Ajouter un produit au stock d'un magasin
1. Accéder à "Stocks par magasin"
2. Sélectionner le magasin
3. Cliquer sur "Nouveau stock"
4. Définir :
   - Produit
   - Quantité initiale
   - Seuil minimum
   - Seuil maximum
   - Point de réapprovisionnement
   - Emplacement

#### Ajuster les stocks
1. Dans la fiche d'un stock
2. Utiliser "Ajuster le stock"
3. Choisir le type d'ajustement :
   - Ajouter : augmente le stock
   - Retirer : diminue le stock
   - Définir : fixe une quantité exacte
4. Indiquer la raison et une référence

#### Transférer du stock entre magasins
1. Accéder à "Transferts de stock" (super admin uniquement)
2. Sélectionner produit, magasin source et destination
3. Indiquer la quantité à transférer
4. Ajouter une raison et référence

### Utilisation du POS

#### Vente avec calcul automatique des bénéfices
1. L'utilisateur ne voit que les produits de son magasin
2. Les prix affichés sont ceux configurés pour le magasin
3. Le stock vérifié est celui du magasin
4. Le bénéfice est calculé automatiquement :
   - Bénéfice = (Prix de vente - Prix de coût) × Quantité
   - Marge = Bénéfice / Prix de vente × 100

#### Recherche de clients
- Seuls les clients du magasin de l'utilisateur sont affichés
- Les admins voient tous les clients de leur magasin

### Dashboard et rapports

#### Dashboard par magasin
- Chaque utilisateur voit les statistiques de son magasin
- Ventes du jour/mois
- Produits en rupture de stock
- Bénéfices réalisés
- Top des vendeurs du magasin

#### Rapports de bénéfices
- Bénéfices par période
- Bénéfices par produit
- Bénéfices par vendeur
- Comparaison entre magasins (super admin)

## Rôles et permissions

### Super Admin
- Accès à tous les magasins
- Création et gestion des magasins
- Assignation des utilisateurs
- Transferts entre magasins
- Vue globale des rapports

### Admin de magasin
- Accès uniquement à son magasin
- Gestion des prix de son magasin
- Gestion des stocks de son magasin
- Rapports de son magasin

### Vendeur
- Accès POS pour son magasin
- Vente avec calcul automatique des bénéfices
- Consultation des stocks de son magasin

## Points importants

### Migration des données existantes
- Un magasin "MAIN" est créé automatiquement
- Toutes les données existantes sont associées à ce magasin
- Les stocks produits sont migrés vers store_stocks
- Les utilisateurs sans magasin assigné doivent être configurés

### Calcul des bénéfices
- Nécessite la définition des prix de coût
- Le bénéfice est calculé au moment de la vente
- Stocké dans sale_items pour historique
- Permet le suivi des performances

### Cohérence des données
- Un produit doit avoir un prix dans un magasin pour être vendu
- Un produit doit avoir un stock dans un magasin pour être vendu
- Les mouvements de stock sont tracés par magasin
- Les clients appartiennent à un magasin spécifique

## Prochaines étapes

1. **Créer les vues** pour l'interface utilisateur
2. **Ajouter les rapports** de bénéfices détaillés
3. **Implémenter les notifications** de stock bas par magasin
4. **Ajouter la synchronisation** entre magasins si nécessaire
