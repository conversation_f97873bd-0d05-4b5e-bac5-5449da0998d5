<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'label',
    'name',
    'type' => 'text',
    'required' => false,
    'placeholder' => '',
    'value' => '',
    'options' => [],
    'help' => null,
    'icon' => null,
    'rows' => 3,
    'step' => null,
    'min' => null,
    'max' => null,
    'multiple' => false,
    'accept' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'label',
    'name',
    'type' => 'text',
    'required' => false,
    'placeholder' => '',
    'value' => '',
    'options' => [],
    'help' => null,
    'icon' => null,
    'rows' => 3,
    'step' => null,
    'min' => null,
    'max' => null,
    'multiple' => false,
    'accept' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
$hasError = $errors->has($name);
$inputClasses = "w-full px-3 py-2.5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors " . 
                ($hasError ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500');

if ($icon) {
    $inputClasses .= ' pl-10';
}
?>

<div <?php echo e($attributes->merge(['class' => 'space-y-2'])); ?>>
    <!-- Label -->
    <label for="<?php echo e($name); ?>" class="block text-sm font-medium text-gray-700">
        <?php echo e($label); ?>

        <?php if($required): ?>
            <span class="text-red-500 ml-1">*</span>
        <?php endif; ?>
    </label>

    <!-- Input Container -->
    <div class="relative">
        <?php if($icon): ?>
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i class="<?php echo e($icon); ?> text-gray-400"></i>
        </div>
        <?php endif; ?>

        <?php switch($type):
            case ('textarea'): ?>
                <textarea 
                    name="<?php echo e($name); ?>" 
                    id="<?php echo e($name); ?>"
                    rows="<?php echo e($rows); ?>"
                    placeholder="<?php echo e($placeholder); ?>"
                    <?php echo e($required ? 'required' : ''); ?>

                    class="<?php echo e($inputClasses); ?>"
                ><?php echo e(old($name, $value)); ?></textarea>
                <?php break; ?>

            <?php case ('select'): ?>
                <select 
                    name="<?php echo e($name); ?><?php echo e($multiple ? '[]' : ''); ?>" 
                    id="<?php echo e($name); ?>"
                    <?php echo e($required ? 'required' : ''); ?>

                    <?php echo e($multiple ? 'multiple' : ''); ?>

                    class="<?php echo e($inputClasses); ?>"
                >
                    <?php if(!$multiple && !$required): ?>
                        <option value=""><?php echo e($placeholder ?: 'Sélectionner une option'); ?></option>
                    <?php endif; ?>
                    <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optionValue => $optionLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($optionValue); ?>" 
                                <?php echo e((is_array(old($name, $value)) ? in_array($optionValue, old($name, $value)) : old($name, $value) == $optionValue) ? 'selected' : ''); ?>>
                            <?php echo e($optionLabel); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php break; ?>

            <?php case ('checkbox'): ?>
                <div class="flex items-center">
                    <input 
                        type="checkbox" 
                        name="<?php echo e($name); ?>" 
                        id="<?php echo e($name); ?>"
                        value="1"
                        <?php echo e(old($name, $value) ? 'checked' : ''); ?>

                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    >
                    <label for="<?php echo e($name); ?>" class="ml-2 block text-sm text-gray-900">
                        <?php echo e($placeholder); ?>

                    </label>
                </div>
                <?php break; ?>

            <?php case ('file'): ?>
                <input 
                    type="file" 
                    name="<?php echo e($name); ?><?php echo e($multiple ? '[]' : ''); ?>" 
                    id="<?php echo e($name); ?>"
                    <?php echo e($required ? 'required' : ''); ?>

                    <?php echo e($multiple ? 'multiple' : ''); ?>

                    <?php echo e($accept ? 'accept=' . $accept : ''); ?>

                    class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                >
                <?php break; ?>

            <?php case ('number'): ?>
                <input 
                    type="number" 
                    name="<?php echo e($name); ?>" 
                    id="<?php echo e($name); ?>"
                    value="<?php echo e(old($name, $value)); ?>"
                    placeholder="<?php echo e($placeholder); ?>"
                    <?php echo e($required ? 'required' : ''); ?>

                    <?php echo e($min !== null ? 'min=' . $min : ''); ?>

                    <?php echo e($max !== null ? 'max=' . $max : ''); ?>

                    <?php echo e($step !== null ? 'step=' . $step : ''); ?>

                    class="<?php echo e($inputClasses); ?>"
                >
                <?php break; ?>

            <?php case ('email'): ?>
                <input 
                    type="email" 
                    name="<?php echo e($name); ?>" 
                    id="<?php echo e($name); ?>"
                    value="<?php echo e(old($name, $value)); ?>"
                    placeholder="<?php echo e($placeholder); ?>"
                    <?php echo e($required ? 'required' : ''); ?>

                    class="<?php echo e($inputClasses); ?>"
                >
                <?php break; ?>

            <?php case ('password'): ?>
                <input 
                    type="password" 
                    name="<?php echo e($name); ?>" 
                    id="<?php echo e($name); ?>"
                    placeholder="<?php echo e($placeholder); ?>"
                    <?php echo e($required ? 'required' : ''); ?>

                    class="<?php echo e($inputClasses); ?>"
                >
                <?php break; ?>

            <?php case ('date'): ?>
                <input 
                    type="date" 
                    name="<?php echo e($name); ?>" 
                    id="<?php echo e($name); ?>"
                    value="<?php echo e(old($name, $value)); ?>"
                    <?php echo e($required ? 'required' : ''); ?>

                    class="<?php echo e($inputClasses); ?>"
                >
                <?php break; ?>

            <?php default: ?>
                <input 
                    type="<?php echo e($type); ?>" 
                    name="<?php echo e($name); ?>" 
                    id="<?php echo e($name); ?>"
                    value="<?php echo e(old($name, $value)); ?>"
                    placeholder="<?php echo e($placeholder); ?>"
                    <?php echo e($required ? 'required' : ''); ?>

                    class="<?php echo e($inputClasses); ?>"
                >
        <?php endswitch; ?>
    </div>

    <!-- Help Text -->
    <?php if($help): ?>
    <p class="text-xs text-gray-500"><?php echo e($help); ?></p>
    <?php endif; ?>

    <!-- Error Message -->
    <?php $__errorArgs = [$name];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
    <p class="text-sm text-red-600 flex items-center">
        <i class="fas fa-exclamation-circle mr-1"></i>
        <?php echo e($message); ?>

    </p>
    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
</div>
<?php /**PATH D:\UNDP\pos\pos\resources\views/components/form-field.blade.php ENDPATH**/ ?>