<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-users mr-2"></i>{{ __('Gestion des Clients') }}
            </h2>
            @can('create_customers')
                <a href="{{ route('customers.create') }}"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>Nouveau Client
                </a>
            @endcan
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">

                    <!-- Filtres et recherche -->
                    <form method="GET" class="mb-6 flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="{{ request('search') }}" placeholder="Rechercher un client..."
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex gap-2">
                            <select name="status"
                                class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Tous les clients</option>
                                <option value="active" @if(request('status')=='active') selected @endif>Clients actifs</option>
                                <option value="with_debt" @if(request('status')=='with_debt') selected @endif>Avec dette</option>
                                <option value="inactive" @if(request('status')=='inactive') selected @endif>Inactifs</option>
                            </select>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold px-4 rounded-lg transition-colors">
                                <i class="fas fa-search mr-1"></i>Filtrer
                            </button>
                            @if(request('search') || request('status'))
                            <a href="{{ route('customers.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-bold px-4 rounded-lg transition-colors">
                                <i class="fas fa-times"></i>
                            </a>
                            @endif
                        </div>
                    </form>

                    <!-- Statistiques rapides -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-users text-blue-600 text-xl mr-3"></i>
                                <div>
                                    <p class="text-sm text-gray-600">Total Clients</p>
                                    <p class="text-xl font-bold text-blue-600">{{ $customers->total() }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-600 text-xl mr-3"></i>
                                <div>
                                    <p class="text-sm text-gray-600">Clients Actifs</p>
                                    <p class="text-xl font-bold text-green-600">
                                        {{ $customers->where('is_active', true)->count() }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-credit-card text-yellow-600 text-xl mr-3"></i>
                                <div>
                                    <p class="text-sm text-gray-600">Avec Dette</p>
                                    <p class="text-xl font-bold text-yellow-600">
                                        {{ $customers->where('current_balance', '>', 0)->count() }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-money-bill-wave text-purple-600 text-xl mr-3"></i>
                                <div>
                                    <p class="text-sm text-gray-600">Total Crédits</p>
                                    <p class="text-xl font-bold text-purple-600">
                                        {{ number_format($customers->sum('current_balance'), 0, ',', ' ') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Table des clients -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Client
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Contact
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Échéance & Dette
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Ventes
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Statut
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($customers as $customer)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div
                                                        class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                                                        <i class="fas fa-user text-gray-400"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ $customer->name }}
                                                    </div>
                                                    @if ($customer->address)
                                                        <div class="text-sm text-gray-500">
                                                            {{ Str::limit($customer->address, 30) }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if ($customer->email)
                                                <div class="text-sm text-gray-900">{{ $customer->email }}</div>
                                            @endif
                                            @if ($customer->phone)
                                                <div class="text-sm text-gray-500">{{ $customer->phone }}</div>
                                            @endif
                                            @if (!$customer->email && !$customer->phone)
                                                <span class="text-sm text-gray-400">Non renseigné</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                @if($customer->due_date)
                                                    Échéance: {{ $customer->due_date->format('d/m/Y') }}
                                                @else
                                                    <span class="text-gray-400">Pas d'échéance</span>
                                                @endif
                                            </div>
                                            <div class="text-sm
                                            @if ($customer->current_balance > 0)
                                                @switch($customer->due_date_status)
                                                    @case('overdue') text-red-600 font-medium @break
                                                    @case('due_soon') text-orange-600 font-medium @break
                                                    @default text-blue-600 @break
                                                @endswitch
                                            @else text-green-600 @endif"
                                                {!! $customer->current_balance > 0 ? 'data-state="debt"' : 'data-state="no-debt"' !!}>
                                                Dette: {{ number_format($customer->current_balance, 0, ',', ' ') }}
                                                FCFA
                                                @if($customer->current_balance > 0 && $customer->due_date)
                                                    @switch($customer->due_date_status)
                                                        @case('overdue')
                                                            <span class="ml-1 text-xs">(En retard)</span>
                                                            @break
                                                        @case('due_soon')
                                                            <span class="ml-1 text-xs">(Bientôt dû)</span>
                                                            @break
                                                    @endswitch
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $customer->sales_count }} vente(s)
                                            </div>
                                            <div class="text-sm text-gray-500">{{ $customer->credits_count }} crédit(s)
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if ($customer->is_active)
                                                <span
                                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Actif
                                                </span>
                                            @else
                                                <span
                                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    Inactif
                                                </span>
                                            @endif

                                            @if ($customer->current_balance > 0)
                                                <span
                                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-1">
                                                    Dette
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('customers.show', $customer) }}"
                                                    class="text-blue-600 hover:text-blue-900" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('pos.index', ['customer' => $customer->id]) }}"
                                                    class="text-green-600 hover:text-green-900" title="Nouvelle vente">
                                                    <i class="fas fa-cash-register"></i>
                                                </a>
                                                @can('edit_customers')
                                                    <a href="{{ route('customers.edit', $customer) }}"
                                                        class="text-indigo-600 hover:text-indigo-900" title="Modifier">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endcan
                                                @if (Auth::user()->roles->whereIn('name', ['superAdmin', 'admin'])->count() > 0)
                                                    @if ($customer->current_balance > 0)
                                                        <a href="{{ route('customers.credits', $customer) }}"
                                                            class="text-yellow-600 hover:text-yellow-900"
                                                            title="Crédits">
                                                            <i class="fas fa-credit-card"></i>
                                                        </a>
                                                    @endif
                                                @endif
                                                @can('delete_customers')
                                                    <form action="{{ route('customers.destroy', $customer) }}"
                                                        method="POST" class="inline"
                                                        onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce client?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900"
                                                            title="Supprimer">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            Aucun client trouvé
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $customers->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
