<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => null,
    'subtitle' => null,
    'icon' => null,
    'padding' => 'p-6',
    'shadow' => 'shadow-soft',
    'hover' => false,
    'border' => true,
    'background' => 'bg-white'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => null,
    'subtitle' => null,
    'icon' => null,
    'padding' => 'p-6',
    'shadow' => 'shadow-soft',
    'hover' => false,
    'border' => true,
    'background' => 'bg-white'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div <?php echo e($attributes->merge([
    'class' => "
        {$background} 
        {$shadow} 
        rounded-xl 
        " . ($border ? 'border border-gray-100' : '') . "
        " . ($hover ? 'hover:shadow-medium transition-all duration-200 hover:-translate-y-1' : '') . "
        overflow-hidden
    "
])); ?>>
    <?php if($title || $subtitle || $icon): ?>
    <div class="border-b border-gray-100 px-6 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <?php if($icon): ?>
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="<?php echo e($icon); ?> text-white"></i>
                    </div>
                </div>
                <?php endif; ?>
                
                <div>
                    <?php if($title): ?>
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo e($title); ?></h3>
                    <?php endif; ?>
                    
                    <?php if($subtitle): ?>
                    <p class="text-sm text-gray-500"><?php echo e($subtitle); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if(isset($actions)): ?>
            <div class="flex items-center space-x-2">
                <?php echo e($actions); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <div class="<?php echo e($padding); ?>">
        <?php echo e($slot); ?>

    </div>

    <?php if(isset($footer)): ?>
    <div class="border-t border-gray-100 px-6 py-4 bg-gray-50">
        <?php echo e($footer); ?>

    </div>
    <?php endif; ?>
</div>
<?php /**PATH D:\UNDP\pos\pos\resources\views/components/card.blade.php ENDPATH**/ ?>