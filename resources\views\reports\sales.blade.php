<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-chart-line mr-2"></i>Rapport des Ventes
            </h2>
            <div class="flex space-x-2">
                <form action="{{ route('reports.sales') }}" method="POST" class="inline">
                    @csrf
                    <input type="hidden" name="date_from" value="{{ $dateFrom->format('Y-m-d') }}">
                    <input type="hidden" name="date_to" value="{{ $dateTo->format('Y-m-d') }}">
                    <input type="hidden" name="format" value="pdf">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                        <i class="fas fa-file-pdf mr-2"></i>PDF
                    </button>
                </form>
                {{-- <form action="{{ route('reports.sales') }}" method="POST" class="inline">
                    @csrf
                    <input type="hidden" name="date_from" value="{{ $dateFrom->format('Y-m-d') }}">
                    <input type="hidden" name="date_to" value="{{ $dateTo->format('Y-m-d') }}">
                    <input type="hidden" name="format" value="excel">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                        <i class="fas fa-file-excel mr-2"></i>Excel
                    </button>
                </form> --}}
                <a href="{{ route('reports.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Période du rapport -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-calendar text-blue-600 mr-3"></i>
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">Période du rapport</h3>
                        <p class="text-blue-700">
                            Du {{ $dateFrom->format('d/m/Y') }} au {{ $dateTo->format('d/m/Y') }}
                            ({{ $dateFrom->diffInDays($dateTo) + 1 }} jour(s))
                        </p>
                    </div>
                </div>
            </div>

            <!-- Statistiques générales -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-euro-sign text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total des ventes</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($totalSales, 0, ',', ' ') }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-receipt text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Nombre de ventes</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $sales->count() }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-percentage text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total remises</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($totalDiscount, 0, ',', ' ') }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-calculator text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total TVA</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($totalTax, 0, ',', ' ') }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Répartition par mode de paiement -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-credit-card mr-2"></i>Répartition par mode de paiement
                        </h3>
                        <div class="space-y-4">
                            @foreach($salesByPaymentMethod as $method => $data)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full mr-3 
                                        {{ $method === 'cash' ? 'bg-green-500' : '' }}
                                        {{ $method === 'mobile_money' ? 'bg-blue-500' : '' }}
                                        {{ $method === 'credit' ? 'bg-yellow-500' : '' }}"></div>
                                    <span class="text-sm font-medium text-gray-900">
                                        {{ $method === 'cash' ? 'Espèces' : '' }}
                                        {{ $method === 'mobile_money' ? 'Mobile Money' : '' }}
                                        {{ $method === 'credit' ? 'Crédit' : '' }}
                                    </span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-900">{{ number_format($data['total'], 0, ',', ' ') }} FCFA</div>
                                    <div class="text-xs text-gray-500">{{ $data['count'] }} vente(s)</div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-chart-pie mr-2"></i>Statistiques détaillées
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">Vente moyenne</span>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ $sales->count() > 0 ? number_format($totalSales / $sales->count(), 0, ',', ' ') : 0 }} FCFA
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">Vente la plus élevée</span>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ $sales->count() > 0 ? number_format($sales->max('total_amount'), 0, ',', ' ') : 0 }} FCFA
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">Vente la plus faible</span>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ $sales->count() > 0 ? number_format($sales->min('total_amount'), 0, ',', ' ') : 0 }} FCFA
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">Clients uniques</span>
                                <span class="text-sm font-medium text-gray-900">
                                    {{ $sales->whereNotNull('customer_id')->unique('customer_id')->count() }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste détaillée des ventes -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">
                        <i class="fas fa-list mr-2"></i>Détail des ventes
                    </h3>
                    
                    @if($sales->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        N° Vente
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Client
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Montant
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Paiement
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Caissier
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($sales as $sale)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $sale->sale_number }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $sale->created_at->format('d/m/Y H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $sale->customer ? $sale->customer->name : 'Client anonyme' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</div>
                                        @if($sale->discount_amount > 0)
                                        <div class="text-xs text-gray-500">Remise: {{ number_format($sale->discount_amount, 0, ',', ' ') }} FCFA</div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $sale->payment_method === 'cash' ? 'bg-green-100 text-green-800' : '' }}
                                            {{ $sale->payment_method === 'mobile_money' ? 'bg-blue-100 text-blue-800' : '' }}
                                            {{ $sale->payment_method === 'credit' ? 'bg-yellow-100 text-yellow-800' : '' }}">
                                            {{ $sale->payment_method === 'cash' ? 'Espèces' : '' }}
                                            {{ $sale->payment_method === 'mobile_money' ? 'Mobile Money' : '' }}
                                            {{ $sale->payment_method === 'credit' ? 'Crédit' : '' }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $sale->user->name }}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-8">
                        <i class="fas fa-receipt text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500">Aucune vente trouvée pour cette période</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
