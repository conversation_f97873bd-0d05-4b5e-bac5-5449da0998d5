<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-edit mr-3 text-blue-600"></i>Modifier le magasin
                </h2>
                <p class="text-sm text-gray-600 mt-1">{{ $store->name }} ({{ $store->code }})</p>
            </div>
            <div class="flex space-x-2">
                <x-button href="{{ route('stores.show', $store) }}" variant="outline" icon="fas fa-eye">
                    Voir les détails
                </x-button>
                <x-button href="{{ route('stores.index') }}" variant="outline" icon="fas fa-arrow-left">
                    Retour à la liste
                </x-button>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <form method="POST" action="{{ route('stores.update', $store) }}" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Informations de base -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>Informations de base
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field>
                            <x-input-label for="name" value="Nom du magasin *" />
                            <x-text-input id="name" name="name" type="text" 
                                         value="{{ old('name', $store->name) }}" required autofocus 
                                         placeholder="Ex: Magasin Centre-ville" />
                            <x-input-error :messages="$errors->get('name')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="code" value="Code magasin *" />
                            <x-text-input id="code" name="code" type="text" 
                                         value="{{ old('code', $store->code) }}" required 
                                         placeholder="Ex: MAG001" />
                            <x-input-error :messages="$errors->get('code')" />
                            <p class="text-xs text-gray-500 mt-1">Code unique pour identifier le magasin</p>
                        </x-form-field>

                        <x-form-field class="md:col-span-2">
                            <x-input-label for="description" value="Description" />
                            <textarea id="description" name="description" rows="3"
                                     class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                     placeholder="Description du magasin...">{{ old('description', $store->description) }}</textarea>
                            <x-input-error :messages="$errors->get('description')" />
                        </x-form-field>
                    </div>
                </div>

                <!-- Adresse et contact -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-map-marker-alt mr-2 text-green-600"></i>Adresse et contact
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field class="md:col-span-2">
                            <x-input-label for="address" value="Adresse complète" />
                            <textarea id="address" name="address" rows="2"
                                     class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                     placeholder="Adresse complète du magasin...">{{ old('address', $store->address) }}</textarea>
                            <x-input-error :messages="$errors->get('address')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="phone" value="Téléphone" />
                            <x-text-input id="phone" name="phone" type="tel" 
                                         value="{{ old('phone', $store->phone) }}" 
                                         placeholder="Ex: +225 01 02 03 04 05" />
                            <x-input-error :messages="$errors->get('phone')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="email" value="Email" />
                            <x-text-input id="email" name="email" type="email" 
                                         value="{{ old('email', $store->email) }}" 
                                         placeholder="Ex: <EMAIL>" />
                            <x-input-error :messages="$errors->get('email')" />
                        </x-form-field>
                    </div>
                </div>

                <!-- Responsable -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-user-tie mr-2 text-purple-600"></i>Responsable du magasin
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field>
                            <x-input-label for="manager_name" value="Nom du responsable" />
                            <x-text-input id="manager_name" name="manager_name" type="text" 
                                         value="{{ old('manager_name', $store->manager_name) }}" 
                                         placeholder="Nom complet du responsable" />
                            <x-input-error :messages="$errors->get('manager_name')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="manager_phone" value="Téléphone du responsable" />
                            <x-text-input id="manager_phone" name="manager_phone" type="tel" 
                                         value="{{ old('manager_phone', $store->manager_phone) }}" 
                                         placeholder="Téléphone du responsable" />
                            <x-input-error :messages="$errors->get('manager_phone')" />
                        </x-form-field>
                    </div>
                </div>

                <!-- Configuration -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-cog mr-2 text-orange-600"></i>Configuration
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <x-form-field>
                            <x-input-label for="default_tax_rate" value="Taux de taxe par défaut (%)" />
                            <x-text-input id="default_tax_rate" name="default_tax_rate" type="number" 
                                         step="0.01" min="0" max="100"
                                         value="{{ old('default_tax_rate', $store->default_tax_rate) }}" />
                            <x-input-error :messages="$errors->get('default_tax_rate')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="currency" value="Devise" />
                            <select id="currency" name="currency" 
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="FCFA" {{ old('currency', $store->currency) === 'FCFA' ? 'selected' : '' }}>FCFA</option>
                                <option value="EUR" {{ old('currency', $store->currency) === 'EUR' ? 'selected' : '' }}>Euro (EUR)</option>
                                <option value="USD" {{ old('currency', $store->currency) === 'USD' ? 'selected' : '' }}>Dollar (USD)</option>
                            </select>
                            <x-input-error :messages="$errors->get('currency')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="timezone" value="Fuseau horaire" />
                            <select id="timezone" name="timezone" 
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="Africa/Abidjan" {{ old('timezone', $store->timezone) === 'Africa/Abidjan' ? 'selected' : '' }}>
                                    Abidjan (GMT+0)
                                </option>
                                <option value="Europe/Paris" {{ old('timezone', $store->timezone) === 'Europe/Paris' ? 'selected' : '' }}>
                                    Paris (GMT+1)
                                </option>
                            </select>
                            <x-input-error :messages="$errors->get('timezone')" />
                        </x-form-field>
                    </div>

                    <div class="mt-6">
                        <div class="flex items-center">
                            <input id="is_active" name="is_active" type="checkbox" value="1" 
                                   {{ old('is_active', $store->is_active) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Magasin actif
                            </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Un magasin inactif ne peut pas effectuer de ventes</p>
                    </div>
                </div>

                <!-- Statistiques (lecture seule) -->
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-chart-bar mr-2 text-gray-600"></i>Statistiques (lecture seule)
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ number_format($store->getTotalSales()) }}</div>
                            <div class="text-sm text-gray-600">Ventes totales (FCFA)</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ number_format($store->getTotalProfit()) }}</div>
                            <div class="text-sm text-gray-600">Bénéfices (FCFA)</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600">{{ $store->getTotalProducts() }}</div>
                            <div class="text-sm text-gray-600">Produits en stock</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ $store->users->count() }}</div>
                            <div class="text-sm text-gray-600">Utilisateurs assignés</div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <p class="text-xs text-gray-500">
                            Créé le {{ $store->created_at->format('d/m/Y à H:i') }} • 
                            Dernière modification le {{ $store->updated_at->format('d/m/Y à H:i') }}
                        </p>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-between">
                    <div>
                        @if($store->users->count() == 0 && $store->sales->count() == 0)
                            <form method="POST" action="{{ route('stores.destroy', $store) }}" 
                                  onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce magasin ? Cette action est irréversible.')" 
                                  class="inline">
                                @csrf
                                @method('DELETE')
                                <x-button type="submit" variant="danger" icon="fas fa-trash">
                                    Supprimer le magasin
                                </x-button>
                            </form>
                        @endif
                    </div>
                    
                    <div class="flex space-x-4">
                        <x-button href="{{ route('stores.show', $store) }}" variant="outline">
                            Annuler
                        </x-button>
                        <x-button type="submit" variant="primary" icon="fas fa-save">
                            Enregistrer les modifications
                        </x-button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</x-app-layout>
