<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use App\Models\Store;
use App\Models\Product;
use App\Models\Sale;
use App\Models\StoreStock;

class QueryOptimizationService
{
    /**
     * Optimiser les requêtes de produits avec stocks par magasin
     */
    public function getProductsWithStockForStore($storeId, $filters = [])
    {
        $query = Product::select([
                'products.id',
                'products.name',
                'products.barcode',
                'products.unit',
                'store_stocks.quantity',
                'store_stocks.min_stock_level',
                'product_prices.selling_price',
                'product_prices.cost_price'
            ])
            ->join('store_stocks', function($join) use ($storeId) {
                $join->on('products.id', '=', 'store_stocks.product_id')
                     ->where('store_stocks.store_id', '=', $storeId)
                     ->where('store_stocks.is_active', '=', true);
            })
            ->leftJoin('product_prices', function($join) use ($storeId) {
                $join->on('products.id', '=', 'product_prices.product_id')
                     ->where('product_prices.store_id', '=', $storeId)
                     ->where('product_prices.is_active', '=', true);
            })
            ->where('products.is_active', true);

        // Appliquer les filtres
        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function($q) use ($search) {
                $q->where('products.name', 'LIKE', "%{$search}%")
                  ->orWhere('products.barcode', 'LIKE', "%{$search}%");
            });
        }

        if (isset($filters['low_stock']) && $filters['low_stock']) {
            $query->whereColumn('store_stocks.quantity', '<=', 'store_stocks.min_stock_level');
        }

        if (isset($filters['out_of_stock']) && $filters['out_of_stock']) {
            $query->where('store_stocks.quantity', '<=', 0);
        }

        return $query->orderBy('products.name');
    }

    /**
     * Requête optimisée pour les ventes avec détails
     */
    public function getSalesWithDetailsForStore($storeId, $dateRange = null, $limit = null)
    {
        $query = Sale::select([
                'sales.id',
                'sales.sale_number',
                'sales.total_amount',
                'sales.created_at',
                'customers.name as customer_name',
                'users.name as user_name',
                DB::raw('COUNT(sale_items.id) as items_count'),
                DB::raw('SUM(sale_items.profit_amount) as total_profit')
            ])
            ->leftJoin('customers', 'sales.customer_id', '=', 'customers.id')
            ->join('users', 'sales.user_id', '=', 'users.id')
            ->join('sale_items', 'sales.id', '=', 'sale_items.sale_id')
            ->where('sales.store_id', $storeId)
            ->groupBy([
                'sales.id', 
                'sales.sale_number', 
                'sales.total_amount', 
                'sales.created_at',
                'customers.name',
                'users.name'
            ]);

        if ($dateRange) {
            $query->whereBetween('sales.created_at', $dateRange);
        }

        if ($limit) {
            $query->limit($limit);
        }

        return $query->orderBy('sales.created_at', 'desc');
    }

    /**
     * Statistiques agrégées optimisées par magasin
     */
    public function getAggregatedStatsForStore($storeId, $period = 'month')
    {
        $dateRange = $this->getDateRange($period);
        
        // Utiliser une seule requête pour toutes les stats
        $stats = DB::select("
            SELECT 
                COUNT(DISTINCT s.id) as sales_count,
                COALESCE(SUM(s.total_amount), 0) as total_revenue,
                COALESCE(SUM(si.profit_amount), 0) as total_profit,
                COALESCE(AVG(s.total_amount), 0) as average_sale,
                COUNT(DISTINCT s.customer_id) as unique_customers,
                COUNT(DISTINCT si.product_id) as products_sold
            FROM sales s
            LEFT JOIN sale_items si ON s.id = si.sale_id
            WHERE s.store_id = ? 
            AND s.created_at BETWEEN ? AND ?
            AND s.status = 'completed'
        ", [$storeId, $dateRange[0], $dateRange[1]]);

        return $stats[0] ?? null;
    }

    /**
     * Requête optimisée pour les mouvements de stock
     */
    public function getStockMovementsForStore($storeId, $productId = null, $limit = 50)
    {
        $query = DB::table('stock_movements')
            ->select([
                'stock_movements.id',
                'stock_movements.type',
                'stock_movements.quantity',
                'stock_movements.reason',
                'stock_movements.created_at',
                'products.name as product_name',
                'users.name as user_name',
                'stock_movements.previous_stock',
                'stock_movements.new_stock'
            ])
            ->join('products', 'stock_movements.product_id', '=', 'products.id')
            ->join('users', 'stock_movements.user_id', '=', 'users.id')
            ->where('stock_movements.store_id', $storeId);

        if ($productId) {
            $query->where('stock_movements.product_id', $productId);
        }

        return $query->orderBy('stock_movements.created_at', 'desc')
                    ->limit($limit);
    }

    /**
     * Requête pour les alertes de stock par magasin
     */
    public function getStockAlertsForStore($storeId)
    {
        return DB::select("
            SELECT 
                p.id,
                p.name,
                p.barcode,
                ss.quantity,
                ss.min_stock_level,
                ss.max_stock_level,
                CASE 
                    WHEN ss.quantity <= 0 THEN 'out_of_stock'
                    WHEN ss.quantity <= ss.min_stock_level THEN 'low_stock'
                    WHEN ss.quantity >= ss.max_stock_level THEN 'overstock'
                    ELSE 'normal'
                END as alert_type,
                pp.selling_price,
                pp.cost_price
            FROM products p
            JOIN store_stocks ss ON p.id = ss.product_id
            LEFT JOIN product_prices pp ON p.id = pp.product_id AND pp.store_id = ss.store_id AND pp.is_active = 1
            WHERE ss.store_id = ?
            AND ss.is_active = 1
            AND p.is_active = 1
            AND (
                ss.quantity <= 0 
                OR ss.quantity <= ss.min_stock_level 
                OR ss.quantity >= ss.max_stock_level
            )
            ORDER BY 
                CASE 
                    WHEN ss.quantity <= 0 THEN 1
                    WHEN ss.quantity <= ss.min_stock_level THEN 2
                    WHEN ss.quantity >= ss.max_stock_level THEN 3
                    ELSE 4
                END,
                ss.quantity ASC
        ", [$storeId]);
    }

    /**
     * Comparaison de performance entre magasins
     */
    public function getStoresPerformanceComparison($period = 'month')
    {
        $dateRange = $this->getDateRange($period);
        
        return DB::select("
            SELECT 
                s.id as store_id,
                s.name as store_name,
                COUNT(DISTINCT sa.id) as sales_count,
                COALESCE(SUM(sa.total_amount), 0) as total_revenue,
                COALESCE(SUM(si.profit_amount), 0) as total_profit,
                COALESCE(AVG(sa.total_amount), 0) as average_sale,
                COUNT(DISTINCT sa.customer_id) as unique_customers,
                COALESCE(SUM(si.profit_amount) / NULLIF(SUM(si.cost_price * si.quantity), 0) * 100, 0) as profit_margin
            FROM stores s
            LEFT JOIN sales sa ON s.id = sa.store_id 
                AND sa.created_at BETWEEN ? AND ?
                AND sa.status = 'completed'
            LEFT JOIN sale_items si ON sa.id = si.sale_id
            WHERE s.is_active = 1
            GROUP BY s.id, s.name
            ORDER BY total_revenue DESC
        ", [$dateRange[0], $dateRange[1]]);
    }

    /**
     * Obtenir la plage de dates selon la période
     */
    private function getDateRange($period)
    {
        switch ($period) {
            case 'today':
                return [now()->startOfDay(), now()->endOfDay()];
            case 'week':
                return [now()->startOfWeek(), now()->endOfWeek()];
            case 'month':
                return [now()->startOfMonth(), now()->endOfMonth()];
            case 'year':
                return [now()->startOfYear(), now()->endOfYear()];
            default:
                return [now()->startOfMonth(), now()->endOfMonth()];
        }
    }

    /**
     * Optimiser les index de base de données
     */
    public function suggestIndexes()
    {
        return [
            'sales' => [
                'idx_sales_store_created' => ['store_id', 'created_at'],
                'idx_sales_store_status' => ['store_id', 'status'],
            ],
            'sale_items' => [
                'idx_sale_items_sale_product' => ['sale_id', 'product_id'],
            ],
            'store_stocks' => [
                'idx_store_stocks_store_product' => ['store_id', 'product_id'],
                'idx_store_stocks_quantity' => ['quantity'],
            ],
            'product_prices' => [
                'idx_product_prices_store_product_active' => ['store_id', 'product_id', 'is_active'],
            ],
            'stock_movements' => [
                'idx_stock_movements_store_created' => ['store_id', 'created_at'],
                'idx_stock_movements_product_store' => ['product_id', 'store_id'],
            ]
        ];
    }
}
