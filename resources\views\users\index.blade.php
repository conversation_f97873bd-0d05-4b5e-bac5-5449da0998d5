<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-users-cog mr-3 text-gray-600"></i>Gestion des Utilisateurs
                </h2>
                <p class="text-sm text-gray-600 mt-1"><PERSON><PERSON>rez les comptes et permissions de votre équipe</p>
            </div>
            <x-button variant="primary" href="{{ route('users.create') }}" icon="fas fa-user-plus">
                Nouvel Utilisateur
            </x-button>
        </div>
    </x-slot>

    <div class="py-4 sm:py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Statistiques -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
                <x-stat-card
                    title="Total utilisateurs"
                    :value="$totalUsers"
                    icon="fas fa-users"
                    color="blue"
                    description="Comptes enregistrés"
                />

                <x-stat-card
                    title="Utilisateurs actifs"
                    :value="$activeUsers"
                    icon="fas fa-user-check"
                    color="green"
                    :description="'Sur ' . $totalUsers . ' total'"
                    :progress="$totalUsers > 0 ? ($activeUsers / $totalUsers) * 100 : 0"
                />

                <x-stat-card
                    title="Utilisateurs inactifs"
                    :value="$inactiveUsers"
                    icon="fas fa-user-times"
                    color="red"
                    :description="$inactiveUsers > 0 ? 'Nécessitent attention' : 'Tous actifs'"
                />

                <x-stat-card
                    title="Rôles définis"
                    :value="$roleStats->count()"
                    icon="fas fa-user-tag"
                    color="purple"
                    description="Types d'accès"
                />
            </div>

            <!-- Messages de succès/erreur -->
            @if(session('success'))
                <x-alert type="success" class="mb-6">
                    {{ session('success') }}
                </x-alert>
            @endif

            @if(session('error'))
                <x-alert type="error" class="mb-6">
                    {{ session('error') }}
                </x-alert>
            @endif

            <!-- Répartition par rôles -->
            @if($roleStats->count() > 0)
            <div class="mb-6 sm:mb-8">
                <x-card title="Répartition par rôles" icon="fas fa-chart-pie">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        @foreach($roleStats as $role)
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900">{{ $role->users_count }}</div>
                            <div class="text-sm text-gray-600 capitalize">{{ $role->name }}</div>
                        </div>
                        @endforeach
                    </div>
                </x-card>
            </div>
            @endif

            <!-- Liste des utilisateurs -->
            <x-card title="Liste des utilisateurs" icon="fas fa-users" padding="p-0">
                @if($users->count() > 0)
                    <x-table 
                        searchable="true"
                        :headers="[
                            'Utilisateur',
                            'Email', 
                            'Téléphone',
                            'Rôle',
                            'Ventes',
                            'Statut',
                            'Actions'
                        ]"
                        :pagination="$users->links()"
                    >
                        @foreach($users as $user)
                        <tr class="hover:bg-gray-50 transition-colors">
                            <!-- Utilisateur -->
                            <td class="px-4 sm:px-6 py-4">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                                            <i class="fas fa-user text-white text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm font-semibold text-gray-900 truncate">{{ $user->name }}</p>
                                        <p class="text-xs text-gray-500">Créé le {{ $user->created_at->format('d/m/Y') }}</p>
                                    </div>
                                </div>
                            </td>

                            <!-- Email -->
                            <td class="px-4 sm:px-6 py-4">
                                <div class="text-sm text-gray-900">{{ $user->email }}</div>
                            </td>

                            <!-- Téléphone -->
                            <td class="px-4 sm:px-6 py-4">
                                <div class="text-sm text-gray-900">{{ $user->phone ?? 'Non renseigné' }}</div>
                            </td>

                            <!-- Rôle -->
                            <td class="px-4 sm:px-6 py-4">
                                @if($user->roles->count() > 0)
                                    @foreach($user->roles as $role)
                                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium
                                        @switch($role->name)
                                            @case('superAdmin') bg-red-100 text-red-800 @break
                                            @case('admin') bg-orange-100 text-orange-800 @break
                                            @case('manager') bg-blue-100 text-blue-800 @break
                                            @case('caissier') bg-green-100 text-green-800 @break
                                            @default bg-gray-100 text-gray-800
                                        @endswitch">
                                        {{ ucfirst($role->name) }}
                                    </span>
                                    @endforeach
                                @else
                                    <span class="text-xs text-gray-500">Aucun rôle</span>
                                @endif
                            </td>

                            <!-- Ventes -->
                            <td class="px-4 sm:px-6 py-4">
                                <div class="text-sm text-gray-900">{{ $user->sales_count ?? 0 }} vente(s)</div>
                            </td>

                            <!-- Statut -->
                            <td class="px-4 sm:px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium
                                    {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $user->is_active ? 'Actif' : 'Inactif' }}
                                </span>
                            </td>

                            <!-- Actions -->
                            <td class="px-4 sm:px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('users.show', $user) }}" 
                                       class="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors" 
                                       title="Voir">
                                        <i class="fas fa-eye text-sm"></i>
                                    </a>
                                    <a href="{{ route('users.edit', $user) }}" 
                                       class="p-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-lg transition-colors" 
                                       title="Modifier">
                                        <i class="fas fa-edit text-sm"></i>
                                    </a>
                                    
                                    @if($user->id !== auth()->id())
                                    <form action="{{ route('users.toggle', $user) }}" 
                                          method="POST" 
                                          class="inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" 
                                                class="p-2 {{ $user->is_active ? 'text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50' : 'text-green-600 hover:text-green-800 hover:bg-green-50' }} rounded-lg transition-colors" 
                                                title="{{ $user->is_active ? 'Désactiver' : 'Activer' }}">
                                            <i class="fas {{ $user->is_active ? 'fa-user-slash' : 'fa-user-check' }} text-sm"></i>
                                        </button>
                                    </form>

                                    <form action="{{ route('users.destroy', $user) }}" 
                                          method="POST" 
                                          class="inline"
                                          onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors" 
                                                title="Supprimer">
                                            <i class="fas fa-trash text-sm"></i>
                                        </button>
                                    </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </x-table>
                @else
                    <x-slot name="empty">
                        <div class="text-center py-12">
                            <i class="fas fa-users text-gray-300 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun utilisateur</h3>
                            <p class="text-gray-500 mb-6">Commencez par ajouter votre premier utilisateur.</p>
                            <x-button variant="primary" href="{{ route('users.create') }}" icon="fas fa-user-plus">
                                Ajouter un utilisateur
                            </x-button>
                        </div>
                    </x-slot>
                @endif
            </x-card>
        </div>
    </div>
</x-app-layout>
