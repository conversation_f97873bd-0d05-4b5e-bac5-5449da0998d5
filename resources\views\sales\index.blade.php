<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-receipt mr-2"></i>{{ __('Historique des Ventes') }}
            </h2>
            <a href="{{ route('pos.index') }}" 
               class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                <i class="fas fa-cash-register mr-2"></i>Nouvelle Vente
            </a>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Statistiques -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-green-50 p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-euro-sign text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total des Ventes</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ number_format($totalSales, 0, ',', ' ') }} FCFA</p>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-receipt text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Nombre de Ventes</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $salesCount }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-purple-50 p-6 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-chart-line text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Moyenne par Vente</p>
                            <p class="text-2xl font-semibold text-gray-900">
                                {{ $salesCount > 0 ? number_format($totalSales / $salesCount, 0, ',', ' ') : 0 }} FCFA
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    
                    <!-- Filtres -->
                    <form method="GET" class="mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                            <div>
                                <input type="text" 
                                       name="search"
                                       value="{{ request('search') }}"
                                       placeholder="Rechercher..." 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <input type="date" 
                                       name="date_from"
                                       value="{{ request('date_from') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <input type="date" 
                                       name="date_to"
                                       value="{{ request('date_to') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <select name="payment_method" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">Tous les paiements</option>
                                    <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>Espèces</option>
                                    <option value="mobile_money" {{ request('payment_method') == 'mobile_money' ? 'selected' : '' }}>Mobile Money</option>
                                    <option value="credit" {{ request('payment_method') == 'credit' ? 'selected' : '' }}>Crédit</option>
                                </select>
                            </div>
                            <div class="flex space-x-2">
                                <button type="submit" 
                                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-search mr-2"></i>Filtrer
                                </button>
                                <a href="{{ route('sales.index') }}" 
                                   class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-bold py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Table des ventes -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Vente
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Client
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Montant
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Paiement
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Caissier
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($sales as $sale)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $sale->sale_number }}</div>
                                        <div class="text-sm text-gray-500">{{ $sale->created_at->format('d/m/Y H:i') }}</div>
                                        <div class="text-xs text-gray-400">{{ $sale->saleItems->count() }} article(s)</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($sale->customer)
                                        <div class="text-sm text-gray-900">{{ $sale->customer->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $sale->customer->phone ?? 'Pas de téléphone' }}</div>
                                        @else
                                        <span class="text-sm text-gray-400">Client anonyme</span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</div>
                                        @if($sale->discount_amount > 0)
                                        <div class="text-sm text-gray-500">Remise: {{ number_format($sale->discount_amount, 0, ',', ' ') }} FCFA</div>
                                        @endif

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @switch($sale->payment_method)
                                            @case('cash')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-money-bill-wave mr-1"></i>Espèces
                                                </span>
                                                @break
                                            @case('mobile_money')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <i class="fas fa-mobile-alt mr-1"></i>Mobile Money
                                                </span>
                                                @break
                                            @case('credit')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    <i class="fas fa-credit-card mr-1"></i>Crédit
                                                </span>
                                                @break
                                        @endswitch
                                        
                                        @if($sale->payment_method !== 'credit')
                                        <div class="text-sm text-gray-500 mt-1">
                                            Reçu: {{ number_format($sale->amount_paid, 0, ',', ' ') }} FCFA
                                            @if($sale->change_amount > 0)
                                            <br>Rendu: {{ number_format($sale->change_amount, 0, ',', ' ') }} FCFA
                                            @endif
                                        </div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $sale->user->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $sale->user->email }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="{{ route('sales.show', $sale) }}" 
                                               class="text-blue-600 hover:text-blue-900" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('sales.receipt', $sale) }}" 
                                               target="_blank"
                                               class="text-green-600 hover:text-green-900" title="Imprimer reçu">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            @if($sale->credit)
                                            <a href="{{ route('credits.show', $sale->credit) }}" 
                                               class="text-yellow-600 hover:text-yellow-900" title="Voir crédit">
                                                <i class="fas fa-credit-card"></i>
                                            </a>
                                            @endif
                                            @can('delete_sales')
                                            @if($sale->status !== 'cancelled')
                                            <form action="{{ route('sales.destroy', $sale) }}" 
                                                  method="POST" 
                                                  class="inline"
                                                  onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette vente? Cette action remettra les produits en stock.')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="text-red-600 hover:text-red-900" 
                                                        title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            @endif
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                        Aucune vente trouvée
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $sales->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
