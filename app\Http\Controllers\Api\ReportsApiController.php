<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\SaleItem;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportsApiController extends Controller
{
    public function quickMetrics(Request $request)
    {
        $user = Auth::user();
        $userStore = $user->store;
        $canViewAllStores = $user->roles->pluck('name')->intersect(['superAdmin', 'admin'])->isNotEmpty();

        $today = Carbon::today();
        
        // Construction de la requête de base
        $query = Sale::whereDate('created_at', $today)
            ->where('status', 'completed');

        if (!$canViewAllStores && $userStore) {
            $query->where('store_id', $userStore->id);
        }

        $todaySales = $query->get();
        
        $todaySalesAmount = $todaySales->sum('total_amount');
        $todayProfit = $todaySales->sum('profit_amount');
        $todayTransactions = $todaySales->count();
        $todayMargin = $todaySalesAmount > 0 ? ($todayProfit / $todaySalesAmount * 100) : 0;

        return response()->json([
            'todaySales' => $todaySalesAmount,
            'todayProfit' => $todayProfit,
            'todayTransactions' => $todayTransactions,
            'todayMargin' => $todayMargin
        ]);
    }

    public function salesTrend(Request $request)
    {
        $user = Auth::user();
        $userStore = $user->store;
        $canViewAllStores = $user->roles->pluck('name')->intersect(['superAdmin', 'admin'])->isNotEmpty();

        $startDate = Carbon::now()->subDays(6)->startOfDay();
        $endDate = Carbon::now()->endOfDay();
        
        // Construction de la requête de base
        $query = Sale::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed');

        if (!$canViewAllStores && $userStore) {
            $query->where('store_id', $userStore->id);
        }

        $sales = $query->get();
        
        // Grouper par jour
        $dailyData = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dateKey = $date->format('Y-m-d');
            $dayName = $date->format('d/m');
            
            $daySales = $sales->filter(function($sale) use ($dateKey) {
                return $sale->created_at->format('Y-m-d') === $dateKey;
            });
            
            $dailyData[] = [
                'date' => $dayName,
                'sales' => $daySales->sum('total_amount'),
                'profits' => $daySales->sum('profit_amount')
            ];
        }

        return response()->json([
            'labels' => collect($dailyData)->pluck('date')->toArray(),
            'sales' => collect($dailyData)->pluck('sales')->toArray(),
            'profits' => collect($dailyData)->pluck('profits')->toArray()
        ]);
    }

    public function profitByCategory(Request $request)
    {
        $user = Auth::user();
        $userStore = $user->store;
        $canViewAllStores = $user->roles->pluck('name')->intersect(['superAdmin', 'admin'])->isNotEmpty();

        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfDay();
        
        // Requête pour obtenir les bénéfices par catégorie
        $query = SaleItem::select([
                'products.category',
                DB::raw('SUM(sale_items.profit_amount) as total_profit')
            ])
            ->join('products', 'sale_items.product_id', '=', 'products.id')
            ->whereHas('sale', function($q) use ($startDate, $endDate, $canViewAllStores, $userStore) {
                $q->whereBetween('created_at', [$startDate, $endDate])
                  ->where('status', 'completed');
                
                if (!$canViewAllStores && $userStore) {
                    $q->where('store_id', $userStore->id);
                }
            })
            ->groupBy('products.category')
            ->orderBy('total_profit', 'desc');

        $categoryData = $query->get();

        return response()->json([
            'labels' => $categoryData->pluck('category')->map(function($category) {
                return $category ?: 'Sans catégorie';
            })->toArray(),
            'profits' => $categoryData->pluck('total_profit')->toArray()
        ]);
    }
}
