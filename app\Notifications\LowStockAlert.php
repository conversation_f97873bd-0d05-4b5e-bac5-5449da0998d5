<?php

namespace App\Notifications;

use App\Models\Store;
use App\Models\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\DatabaseMessage;

class LowStockAlert extends Notification implements ShouldQueue
{
    use Queueable;

    protected $store;
    protected $product;
    protected $currentQuantity;

    /**
     * Create a new notification instance.
     */
    public function __construct(Store $store, Product $product, float $currentQuantity)
    {
        $this->store = $store;
        $this->product = $product;
        $this->currentQuantity = $currentQuantity;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        // Vérifie si les notifications sont activées dans les paramètres du magasin
        $notificationSettings = $this->store->getNotificationSettings();
        
        if (!$notificationSettings['low_stock_alerts'] ?? true) {
            return [];
        }

        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $minStock = $this->store->storeStocks()
            ->where('product_id', $this->product->id)
            ->value('min_stock_level');

        $storeStock = $this->store->storeStocks()
            ->where('product_id', $this->product->id)
            ->first();

        return (new MailMessage)
            ->subject('Alerte de Stock Bas - ' . $this->store->name)
            ->greeting('Bonjour ' . $notifiable->name)
            ->line("Cette notification vous informe d'un niveau de stock bas pour le produit suivant :")
            ->line('Produit : ' . $this->product->name)
            ->line('Magasin : ' . $this->store->name)
            ->line('Quantité actuelle : ' . $this->currentQuantity)
            ->line('Niveau minimum : ' . $minStock)
            ->line('Point de réapprovisionnement : ' . ($storeStock->reorder_point ?? 'Non défini'))
            ->line('Emplacement : ' . ($storeStock->location ?? 'Non défini'))
            ->action('Voir le produit', route('products.show', $this->product->id))
            ->line('Veuillez prendre les mesures nécessaires pour réapprovisionner le stock.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $count = is_countable($this->products) ? count($this->products) : $this->products->count();

        return [
            'type' => 'low_stock',
            'title' => 'Stock faible',
            'message' => "{$count} produit(s) ont un stock faible",
            'products_count' => $count,
            'url' => route('products.index'),
            'icon' => 'fas fa-exclamation-triangle',
            'color' => 'yellow'
        ];
    }

    private function getProductsList()
    {
        $products = is_countable($this->products) ? $this->products : $this->products->take(5);
        $list = '';

        foreach ($products as $product) {
            $list .= "- {$product->name} (Stock: {$product->stock_quantity})\n";
        }

        return $list;
    }
}
