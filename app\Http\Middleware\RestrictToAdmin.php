<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RestrictToAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        if (!$user || !in_array($user->roles->pluck('name')->toArray(), ['superAdmin', 'admin'])) {
            return redirect()->route('pos.index')->with('info', __('dashboard.redirect_pos'));
        }

        return $next($request);
    }
}
