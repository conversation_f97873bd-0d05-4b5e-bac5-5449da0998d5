<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Store;
use App\Models\ProductPrice;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProductPriceController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of product prices for a store.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $userStore = $user->store;

        // Super admins peuvent voir tous les magasins
        if ($user->hasRole('superAdmin')) {
            $stores = Store::active()->get();
            $selectedStoreId = $request->get('store_id', $userStore?->id);
        } else {
            $stores = collect([$userStore]);
            $selectedStoreId = $userStore->id;
        }

        if (!$selectedStoreId) {
            return redirect()->route('dashboard')
                ->with('error', 'Vous devez être assigné à un magasin.');
        }

        $selectedStore = Store::find($selectedStoreId);
        
        // Récupérer les prix avec pagination
        $prices = ProductPrice::with(['product', 'store'])
            ->where('store_id', $selectedStoreId)
            ->active()
            ->current()
            ->paginate(20);

        return view('product-prices.index', compact('prices', 'stores', 'selectedStore'));
    }

    /**
     * Show the form for creating a new product price.
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        $userStore = $user->store;

        $products = Product::active()->get();
        
        if ($user->hasRole('superAdmin')) {
            $stores = Store::active()->get();
        } else {
            $stores = collect([$userStore]);
        }

        $selectedProductId = $request->get('product_id');
        $selectedStoreId = $request->get('store_id', $userStore?->id);

        return view('product-prices.create', compact('products', 'stores', 'selectedProductId', 'selectedStoreId'));
    }

    /**
     * Store a newly created product price in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'store_id' => 'required|exists:stores,id',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'min_selling_price' => 'nullable|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'effective_from' => 'required|date',
            'effective_until' => 'nullable|date|after:effective_from',
            'notes' => 'nullable|string|max:500',
        ]);

        // Vérifier que l'utilisateur a accès à ce magasin
        $user = Auth::user();
        if (!$user->hasRole('superAdmin') && $user->store_id != $request->store_id) {
            abort(403, 'Accès non autorisé à ce magasin.');
        }

        // Vérifier qu'il n'y a pas déjà un prix actif pour cette période
        $existingPrice = ProductPrice::where('product_id', $request->product_id)
            ->where('store_id', $request->store_id)
            ->active()
            ->where(function($query) use ($request) {
                $query->where(function($q) use ($request) {
                    // Le nouveau prix commence pendant une période existante
                    $q->where('effective_from', '<=', $request->effective_from)
                      ->where(function($subQ) use ($request) {
                          $subQ->whereNull('effective_until')
                               ->orWhere('effective_until', '>=', $request->effective_from);
                      });
                })->orWhere(function($q) use ($request) {
                    // Une période existante commence pendant le nouveau prix
                    if ($request->effective_until) {
                        $q->where('effective_from', '<=', $request->effective_until)
                          ->where('effective_from', '>=', $request->effective_from);
                    }
                });
            })
            ->exists();

        if ($existingPrice) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Il existe déjà un prix actif pour cette période.');
        }

        ProductPrice::create($request->all());

        return redirect()->route('product-prices.index', ['store_id' => $request->store_id])
            ->with('success', 'Prix créé avec succès.');
    }

    /**
     * Display the specified product price.
     */
    public function show(ProductPrice $productPrice)
    {
        $user = Auth::user();
        if (!$user->hasRole('superAdmin') && $user->store_id != $productPrice->store_id) {
            abort(403, 'Accès non autorisé.');
        }

        $productPrice->load(['product', 'store']);
        
        // Historique des prix pour ce produit dans ce magasin
        $priceHistory = ProductPrice::where('product_id', $productPrice->product_id)
            ->where('store_id', $productPrice->store_id)
            ->orderBy('effective_from', 'desc')
            ->get();

        return view('product-prices.show', compact('productPrice', 'priceHistory'));
    }

    /**
     * Show the form for editing the specified product price.
     */
    public function edit(ProductPrice $productPrice)
    {
        $user = Auth::user();
        if (!$user->hasRole('superAdmin') && $user->store_id != $productPrice->store_id) {
            abort(403, 'Accès non autorisé.');
        }

        return view('product-prices.edit', compact('productPrice'));
    }

    /**
     * Update the specified product price in storage.
     */
    public function update(Request $request, ProductPrice $productPrice)
    {
        $user = Auth::user();
        if (!$user->hasRole('superAdmin') && $user->store_id != $productPrice->store_id) {
            abort(403, 'Accès non autorisé.');
        }

        $request->validate([
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'min_selling_price' => 'nullable|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'effective_from' => 'required|date',
            'effective_until' => 'nullable|date|after:effective_from',
            'notes' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $productPrice->update($request->all());

        return redirect()->route('product-prices.index', ['store_id' => $productPrice->store_id])
            ->with('success', 'Prix mis à jour avec succès.');
    }

    /**
     * Remove the specified product price from storage.
     */
    public function destroy(ProductPrice $productPrice)
    {
        $user = Auth::user();
        if (!$user->hasRole('superAdmin') && $user->store_id != $productPrice->store_id) {
            abort(403, 'Accès non autorisé.');
        }

        $storeId = $productPrice->store_id;
        $productPrice->delete();

        return redirect()->route('product-prices.index', ['store_id' => $storeId])
            ->with('success', 'Prix supprimé avec succès.');
    }

    /**
     * Bulk update prices for multiple products
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'store_id' => 'required|exists:stores,id',
            'price_updates' => 'required|array',
            'price_updates.*.product_id' => 'required|exists:products,id',
            'price_updates.*.cost_price' => 'required|numeric|min:0',
            'price_updates.*.selling_price' => 'required|numeric|min:0',
        ]);

        $user = Auth::user();
        if (!$user->hasRole('superAdmin') && $user->store_id != $request->store_id) {
            abort(403, 'Accès non autorisé à ce magasin.');
        }

        DB::beginTransaction();
        try {
            foreach ($request->price_updates as $update) {
                // Désactiver les anciens prix
                ProductPrice::where('product_id', $update['product_id'])
                    ->where('store_id', $request->store_id)
                    ->active()
                    ->current()
                    ->update(['effective_until' => now()->subSecond()]);

                // Créer le nouveau prix
                ProductPrice::create([
                    'product_id' => $update['product_id'],
                    'store_id' => $request->store_id,
                    'cost_price' => $update['cost_price'],
                    'selling_price' => $update['selling_price'],
                    'min_selling_price' => $update['min_selling_price'] ?? null,
                    'wholesale_price' => $update['wholesale_price'] ?? null,
                    'tax_rate' => $update['tax_rate'] ?? null,
                    'is_active' => true,
                    'effective_from' => now(),
                    'notes' => 'Mise à jour en lot',
                ]);
            }

            DB::commit();

            return redirect()->route('product-prices.index', ['store_id' => $request->store_id])
                ->with('success', count($request->price_updates) . ' prix mis à jour avec succès.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Erreur lors de la mise à jour : ' . $e->getMessage());
        }
    }

    /**
     * Copy prices from one store to another
     */
    public function copyPrices(Request $request)
    {
        $request->validate([
            'source_store_id' => 'required|exists:stores,id',
            'target_store_id' => 'required|exists:stores,id|different:source_store_id',
        ]);

        $user = Auth::user();
        if (!$user->hasRole('superAdmin')) {
            abort(403, 'Seuls les super admins peuvent copier les prix entre magasins.');
        }

        DB::beginTransaction();
        try {
            $sourcePrices = ProductPrice::where('store_id', $request->source_store_id)
                ->active()
                ->current()
                ->get();

            $copiedCount = 0;
            foreach ($sourcePrices as $sourcePrice) {
                // Vérifier s'il existe déjà un prix pour ce produit dans le magasin cible
                $existingPrice = ProductPrice::where('product_id', $sourcePrice->product_id)
                    ->where('store_id', $request->target_store_id)
                    ->active()
                    ->current()
                    ->first();

                if (!$existingPrice) {
                    ProductPrice::create([
                        'product_id' => $sourcePrice->product_id,
                        'store_id' => $request->target_store_id,
                        'cost_price' => $sourcePrice->cost_price,
                        'selling_price' => $sourcePrice->selling_price,
                        'min_selling_price' => $sourcePrice->min_selling_price,
                        'wholesale_price' => $sourcePrice->wholesale_price,
                        'tax_rate' => $sourcePrice->tax_rate,
                        'is_active' => true,
                        'effective_from' => now(),
                        'notes' => 'Copié depuis magasin ID: ' . $request->source_store_id,
                    ]);
                    $copiedCount++;
                }
            }

            DB::commit();

            return redirect()->route('product-prices.index', ['store_id' => $request->target_store_id])
                ->with('success', "{$copiedCount} prix copiés avec succès.");

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Erreur lors de la copie : ' . $e->getMessage());
        }
    }
}
