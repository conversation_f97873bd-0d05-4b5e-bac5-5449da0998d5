<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Store;
use App\Models\ProductPrice;
use App\Models\StoreStock;
use App\Models\User;
use App\Models\Sale;
use App\Models\Product;

class ValidateMultiStoreSetup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'multistore:validate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Valide la configuration du système multi-magasins';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Validation du système multi-magasins...');
        $this->newLine();

        $errors = 0;
        $warnings = 0;

        // 1. Vérifier les tables
        $errors += $this->validateTables();
        
        // 2. Vérifier les colonnes ajoutées
        $errors += $this->validateColumns();
        
        // 3. Vérifier les modèles
        $errors += $this->validateModels();
        
        // 4. Vérifier les données (si la DB est accessible)
        try {
            $warnings += $this->validateData();
        } catch (\Exception $e) {
            $this->warn("⚠️  Impossible de valider les données : " . $e->getMessage());
            $warnings++;
        }

        $this->newLine();
        
        if ($errors === 0 && $warnings === 0) {
            $this->info('✅ Validation réussie ! Le système multi-magasins est correctement configuré.');
        } elseif ($errors === 0) {
            $this->warn("⚠️  Validation terminée avec {$warnings} avertissement(s).");
        } else {
            $this->error("❌ Validation échouée avec {$errors} erreur(s) et {$warnings} avertissement(s).");
            return 1;
        }

        return 0;
    }

    private function validateTables()
    {
        $this->info('📋 Vérification des tables...');
        $errors = 0;

        $requiredTables = [
            'stores' => 'Table des magasins',
            'product_prices' => 'Table des prix par magasin',
            'store_stocks' => 'Table des stocks par magasin'
        ];

        foreach ($requiredTables as $table => $description) {
            if (Schema::hasTable($table)) {
                $this->line("  ✅ {$description} ({$table})");
            } else {
                $this->error("  ❌ {$description} ({$table}) - MANQUANTE");
                $errors++;
            }
        }

        return $errors;
    }

    private function validateColumns()
    {
        $this->info('🔧 Vérification des colonnes ajoutées...');
        $errors = 0;

        $requiredColumns = [
            'users' => ['store_id'],
            'sales' => ['store_id'],
            'stock_movements' => ['store_id'],
            'customers' => ['store_id'],
            'sale_items' => ['cost_price', 'profit_amount', 'profit_margin']
        ];

        foreach ($requiredColumns as $table => $columns) {
            if (!Schema::hasTable($table)) {
                $this->error("  ❌ Table {$table} n'existe pas");
                $errors++;
                continue;
            }

            foreach ($columns as $column) {
                if (Schema::hasColumn($table, $column)) {
                    $this->line("  ✅ {$table}.{$column}");
                } else {
                    $this->error("  ❌ {$table}.{$column} - MANQUANTE");
                    $errors++;
                }
            }
        }

        return $errors;
    }

    private function validateModels()
    {
        $this->info('🏗️  Vérification des modèles...');
        $errors = 0;

        // Vérifier que les modèles existent et ont les bonnes relations
        $models = [
            Store::class => ['users', 'sales', 'stockMovements', 'customers', 'storeStocks'],
            ProductPrice::class => ['product', 'store'],
            StoreStock::class => ['product', 'store']
        ];

        foreach ($models as $modelClass => $relations) {
            if (class_exists($modelClass)) {
                $this->line("  ✅ Modèle {$modelClass}");
                
                // Vérifier les relations (basique)
                try {
                    $model = new $modelClass;
                    foreach ($relations as $relation) {
                        if (method_exists($model, $relation)) {
                            $this->line("    ✅ Relation {$relation}");
                        } else {
                            $this->error("    ❌ Relation {$relation} manquante");
                            $errors++;
                        }
                    }
                } catch (\Exception $e) {
                    $this->error("  ❌ Erreur lors de l'instanciation de {$modelClass}: " . $e->getMessage());
                    $errors++;
                }
            } else {
                $this->error("  ❌ Modèle {$modelClass} n'existe pas");
                $errors++;
            }
        }

        return $errors;
    }

    private function validateData()
    {
        $this->info('📊 Vérification des données...');
        $warnings = 0;

        try {
            // Vérifier qu'il y a au moins un magasin
            $storeCount = Store::count();
            if ($storeCount > 0) {
                $this->line("  ✅ {$storeCount} magasin(s) configuré(s)");
            } else {
                $this->warn("  ⚠️  Aucun magasin configuré");
                $warnings++;
            }

            // Vérifier les utilisateurs assignés
            $usersWithStore = User::whereNotNull('store_id')->count();
            $totalUsers = User::count();
            if ($usersWithStore === $totalUsers) {
                $this->line("  ✅ Tous les utilisateurs ({$totalUsers}) sont assignés à un magasin");
            } else {
                $this->warn("  ⚠️  {$usersWithStore}/{$totalUsers} utilisateurs assignés à un magasin");
                $warnings++;
            }

            // Vérifier les prix configurés
            $productsWithPrices = ProductPrice::distinct('product_id')->count();
            $totalProducts = Product::count();
            if ($productsWithPrices > 0) {
                $this->line("  ✅ {$productsWithPrices}/{$totalProducts} produits ont des prix configurés");
                if ($productsWithPrices < $totalProducts) {
                    $this->warn("  ⚠️  Certains produits n'ont pas de prix configurés");
                    $warnings++;
                }
            } else {
                $this->warn("  ⚠️  Aucun prix configuré pour les produits");
                $warnings++;
            }

            // Vérifier les stocks configurés
            $productsWithStock = StoreStock::distinct('product_id')->count();
            if ($productsWithStock > 0) {
                $this->line("  ✅ {$productsWithStock}/{$totalProducts} produits ont des stocks configurés");
                if ($productsWithStock < $totalProducts) {
                    $this->warn("  ⚠️  Certains produits n'ont pas de stocks configurés");
                    $warnings++;
                }
            } else {
                $this->warn("  ⚠️  Aucun stock configuré pour les produits");
                $warnings++;
            }

        } catch (\Exception $e) {
            throw $e;
        }

        return $warnings;
    }
}
