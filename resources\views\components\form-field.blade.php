@props([
    'label',
    'name',
    'type' => 'text',
    'required' => false,
    'placeholder' => '',
    'value' => '',
    'options' => [],
    'help' => null,
    'icon' => null,
    'rows' => 3,
    'step' => null,
    'min' => null,
    'max' => null,
    'multiple' => false,
    'accept' => null
])

@php
$hasError = $errors->has($name);
$inputClasses = "w-full px-3 py-2.5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors " . 
                ($hasError ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500');

if ($icon) {
    $inputClasses .= ' pl-10';
}
@endphp

<div {{ $attributes->merge(['class' => 'space-y-2']) }}>
    <!-- Label -->
    <label for="{{ $name }}" class="block text-sm font-medium text-gray-700">
        {{ $label }}
        @if($required)
            <span class="text-red-500 ml-1">*</span>
        @endif
    </label>

    <!-- Input Container -->
    <div class="relative">
        @if($icon)
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i class="{{ $icon }} text-gray-400"></i>
        </div>
        @endif

        @switch($type)
            @case('textarea')
                <textarea 
                    name="{{ $name }}" 
                    id="{{ $name }}"
                    rows="{{ $rows }}"
                    placeholder="{{ $placeholder }}"
                    {{ $required ? 'required' : '' }}
                    class="{{ $inputClasses }}"
                >{{ old($name, $value) }}</textarea>
                @break

            @case('select')
                <select 
                    name="{{ $name }}{{ $multiple ? '[]' : '' }}" 
                    id="{{ $name }}"
                    {{ $required ? 'required' : '' }}
                    {{ $multiple ? 'multiple' : '' }}
                    class="{{ $inputClasses }}"
                >
                    @if(!$multiple && !$required)
                        <option value="">{{ $placeholder ?: 'Sélectionner une option' }}</option>
                    @endif
                    @foreach($options as $optionValue => $optionLabel)
                        <option value="{{ $optionValue }}" 
                                {{ (is_array(old($name, $value)) ? in_array($optionValue, old($name, $value)) : old($name, $value) == $optionValue) ? 'selected' : '' }}>
                            {{ $optionLabel }}
                        </option>
                    @endforeach
                </select>
                @break

            @case('checkbox')
                <div class="flex items-center">
                    <input 
                        type="checkbox" 
                        name="{{ $name }}" 
                        id="{{ $name }}"
                        value="1"
                        {{ old($name, $value) ? 'checked' : '' }}
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    >
                    <label for="{{ $name }}" class="ml-2 block text-sm text-gray-900">
                        {{ $placeholder }}
                    </label>
                </div>
                @break

            @case('file')
                <input 
                    type="file" 
                    name="{{ $name }}{{ $multiple ? '[]' : '' }}" 
                    id="{{ $name }}"
                    {{ $required ? 'required' : '' }}
                    {{ $multiple ? 'multiple' : '' }}
                    {{ $accept ? 'accept=' . $accept : '' }}
                    class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                >
                @break

            @case('number')
                <input 
                    type="number" 
                    name="{{ $name }}" 
                    id="{{ $name }}"
                    value="{{ old($name, $value) }}"
                    placeholder="{{ $placeholder }}"
                    {{ $required ? 'required' : '' }}
                    {{ $min !== null ? 'min=' . $min : '' }}
                    {{ $max !== null ? 'max=' . $max : '' }}
                    {{ $step !== null ? 'step=' . $step : '' }}
                    class="{{ $inputClasses }}"
                >
                @break

            @case('email')
                <input 
                    type="email" 
                    name="{{ $name }}" 
                    id="{{ $name }}"
                    value="{{ old($name, $value) }}"
                    placeholder="{{ $placeholder }}"
                    {{ $required ? 'required' : '' }}
                    class="{{ $inputClasses }}"
                >
                @break

            @case('password')
                <input 
                    type="password" 
                    name="{{ $name }}" 
                    id="{{ $name }}"
                    placeholder="{{ $placeholder }}"
                    {{ $required ? 'required' : '' }}
                    class="{{ $inputClasses }}"
                >
                @break

            @case('date')
                <input 
                    type="date" 
                    name="{{ $name }}" 
                    id="{{ $name }}"
                    value="{{ old($name, $value) }}"
                    {{ $required ? 'required' : '' }}
                    class="{{ $inputClasses }}"
                >
                @break

            @default
                <input 
                    type="{{ $type }}" 
                    name="{{ $name }}" 
                    id="{{ $name }}"
                    value="{{ old($name, $value) }}"
                    placeholder="{{ $placeholder }}"
                    {{ $required ? 'required' : '' }}
                    class="{{ $inputClasses }}"
                >
        @endswitch
    </div>

    <!-- Help Text -->
    @if($help)
    <p class="text-xs text-gray-500">{{ $help }}</p>
    @endif

    <!-- Error Message -->
    @error($name)
    <p class="text-sm text-red-600 flex items-center">
        <i class="fas fa-exclamation-circle mr-1"></i>
        {{ $message }}
    </p>
    @enderror
</div>
