<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Store;
use App\Models\ProductPrice;

class ProductPriceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::all();
        $stores = Store::all();

        // Exemples de prix pour différents types de produits
        $priceTemplates = [
            // Produits alimentaires
            'alimentaire' => [
                'cost_price_range' => [100, 500],
                'margin_range' => [20, 40], // 20% à 40% de marge
                'wholesale_discount' => 10, // 10% de réduction pour le prix de gros
            ],
            // Produits de beauté
            'beaute' => [
                'cost_price_range' => [200, 1000],
                'margin_range' => [30, 60],
                'wholesale_discount' => 15,
            ],
            // Produits électroniques
            'electronique' => [
                'cost_price_range' => [1000, 10000],
                'margin_range' => [15, 30],
                'wholesale_discount' => 5,
            ],
            // Produits de base
            'default' => [
                'cost_price_range' => [50, 800],
                'margin_range' => [25, 45],
                'wholesale_discount' => 12,
            ]
        ];

        foreach ($products as $product) {
            foreach ($stores as $store) {
                // Déterminer le type de produit basé sur le nom (simplification)
                $productType = $this->getProductType($product->name);
                $template = $priceTemplates[$productType] ?? $priceTemplates['default'];

                // Générer un prix de coût aléatoire
                $costPrice = rand($template['cost_price_range'][0], $template['cost_price_range'][1]);
                
                // Calculer le prix de vente avec une marge aléatoire
                $marginPercent = rand($template['margin_range'][0], $template['margin_range'][1]);
                $sellingPrice = $costPrice * (1 + $marginPercent / 100);
                
                // Prix de gros (réduction sur le prix de vente)
                $wholesalePrice = $sellingPrice * (1 - $template['wholesale_discount'] / 100);
                
                // Prix minimum (coût + 10%)
                $minSellingPrice = $costPrice * 1.10;

                // Variation des prix entre magasins (±5%)
                $storeVariation = rand(-5, 5) / 100;
                $sellingPrice = $sellingPrice * (1 + $storeVariation);
                $wholesalePrice = $wholesalePrice * (1 + $storeVariation);

                ProductPrice::create([
                    'product_id' => $product->id,
                    'store_id' => $store->id,
                    'cost_price' => round($costPrice, 2),
                    'selling_price' => round($sellingPrice, 2),
                    'min_selling_price' => round($minSellingPrice, 2),
                    'wholesale_price' => round($wholesalePrice, 2),
                    'tax_rate' => $store->default_tax_rate,
                    'is_active' => true,
                    'effective_from' => now(),
                    'effective_until' => null,
                    'notes' => 'Prix initial généré automatiquement',
                ]);
            }
        }
    }

    /**
     * Déterminer le type de produit basé sur son nom
     */
    private function getProductType($productName)
    {
        $productName = strtolower($productName);
        
        if (str_contains($productName, 'riz') || str_contains($productName, 'mil') || 
            str_contains($productName, 'huile') || str_contains($productName, 'sucre') ||
            str_contains($productName, 'farine') || str_contains($productName, 'pain')) {
            return 'alimentaire';
        }
        
        if (str_contains($productName, 'savon') || str_contains($productName, 'shampoo') || 
            str_contains($productName, 'crème') || str_contains($productName, 'parfum') ||
            str_contains($productName, 'beauté') || str_contains($productName, 'cosmétique')) {
            return 'beaute';
        }
        
        if (str_contains($productName, 'téléphone') || str_contains($productName, 'ordinateur') || 
            str_contains($productName, 'tablette') || str_contains($productName, 'électronique') ||
            str_contains($productName, 'chargeur') || str_contains($productName, 'écouteur')) {
            return 'electronique';
        }
        
        return 'default';
    }
}
