<x-guest-layout>
    <!-- Welcome message -->
    <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-white mb-2">Bienvenue !</h2>
        <p class="text-white/80 text-sm">Connectez-vous à votre compte</p>
    </div>

    <!-- Session Status -->
    @if(session('status'))
        <div class="mb-4 p-3 bg-green-500/20 border border-green-500/30 rounded-lg text-green-100 text-sm">
            <i class="fas fa-check-circle mr-2"></i>{{ session('status') }}
        </div>
    @endif

    <!-- Login Form -->
    <form method="POST" action="{{ route('login') }}" class="space-y-6">
        @csrf

        <!-- Email Address -->
        <div class="space-y-2">
            <label for="email" class="block text-sm font-medium text-white">
                <i class="fas fa-envelope mr-2"></i>Adresse email
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-envelope text-white/40"></i>
                </div>
                <input
                    id="email"
                    name="email"
                    type="email"
                    value="{{ old('email') }}"
                    required
                    autofocus
                    autocomplete="username"
                    placeholder="<EMAIL>"
                    class="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50 transition-all"
                >
            </div>
            @error('email')
                <p class="text-red-300 text-sm flex items-center">
                    <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                </p>
            @enderror
        </div>

        <!-- Password -->
        <div class="space-y-2">
            <label for="password" class="block text-sm font-medium text-white">
                <i class="fas fa-lock mr-2"></i>Mot de passe
            </label>
            <div class="relative" x-data="{ showPassword: false }">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-lock text-white/40"></i>
                </div>
                <input
                    id="password"
                    name="password"
                    :type="showPassword ? 'text' : 'password'"
                    required
                    autocomplete="current-password"
                    placeholder="••••••••"
                    class="w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50 transition-all"
                >
                <button
                    type="button"
                    @click="showPassword = !showPassword"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-white/40 hover:text-white/60 transition-colors"
                >
                    <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                </button>
            </div>
            @error('password')
                <p class="text-red-300 text-sm flex items-center">
                    <i class="fas fa-exclamation-circle mr-1"></i>{{ $message }}
                </p>
            @enderror
        </div>

        <!-- Remember Me & Forgot Password -->
        {{-- <div class="flex items-center justify-between">
            <label for="remember_me" class="flex items-center">
                <input
                    id="remember_me"
                    type="checkbox"
                    name="remember"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-white/30 rounded bg-white/10"
                >
                <span class="ml-2 text-sm text-white/80">Se souvenir de moi</span>
            </label>

            @if (Route::has('password.request'))
                <a href="{{ route('password.request') }}"
                   class="text-sm text-white/80 hover:text-white underline transition-colors">
                    Mot de passe oublié ?
                </a>
            @endif
        </div> --}}

        <!-- Login Button -->
        <button
            type="submit"
            class="w-full bg-white text-gray-900 font-semibold py-3 px-4 rounded-lg hover:bg-white/90 focus:outline-none focus:ring-2 focus:ring-white/50 transition-all transform hover:scale-[1.02] active:scale-[0.98]"
        >
            <i class="fas fa-sign-in-alt mr-2"></i>Se connecter
        </button>

        <!-- Register Link -->
        {{-- @if (Route::has('register'))
        <div class="text-center pt-4 border-t border-white/20">
            <p class="text-white/60 text-sm">
                Pas encore de compte ?
                <a href="{{ route('register') }}"
                   class="text-white hover:text-white/80 font-medium underline transition-colors ml-1">
                    Créer un compte
                </a>
            </p>
        </div>
        @endif --}}
    </form>

    <!-- Demo credentials (for development) -->
    {{-- @if(app()->environment('local'))
    <div class="mt-6 p-4 bg-yellow-500/20 border border-yellow-500/30 rounded-lg">
        <h4 class="text-yellow-100 font-medium text-sm mb-2">
            <i class="fas fa-info-circle mr-2"></i>Comptes de démonstration
        </h4>
        <div class="space-y-1 text-xs text-yellow-100/80">
            <p><strong>Admin:</strong> <EMAIL> / password</p>
            <p><strong>Manager:</strong> <EMAIL> / password</p>
            <p><strong>Caissier:</strong> <EMAIL> / password</p>
        </div>
    </div>
    @endif --}}
</x-guest-layout>
