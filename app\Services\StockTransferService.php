<?php

namespace App\Services;

use App\Models\Store;
use App\Models\Product;
use App\Models\StoreStock;
use App\Models\StockMovement;
use App\Models\StockTransfer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Exception;

class StockTransferService
{
    /**
     * Créer une demande de transfert de stock
     */
    public function createTransferRequest(
        int $fromStoreId,
        int $toStoreId,
        array $items,
        string $reason = null,
        string $priority = 'normal'
    ) {
        try {
            DB::beginTransaction();

            // Valider les magasins
            $fromStore = Store::findOrFail($fromStoreId);
            $toStore = Store::findOrFail($toStoreId);

            // Vérifier les permissions
            if (!Auth::user()->can('transferStock', $fromStore, $toStore)) {
                throw new Exception('Permission insuffisante pour effectuer ce transfert');
            }

            // Créer le transfert principal
            $transfer = StockTransfer::create([
                'transfer_number' => $this->generateTransferNumber(),
                'from_store_id' => $fromStoreId,
                'to_store_id' => $toStoreId,
                'requested_by' => Auth::id(),
                'status' => 'pending',
                'priority' => $priority,
                'reason' => $reason,
                'total_items' => count($items),
                'requested_at' => now()
            ]);

            $totalValue = 0;

            // Ajouter les articles au transfert
            foreach ($items as $item) {
                $product = Product::findOrFail($item['product_id']);
                $quantity = $item['quantity'];

                // Vérifier le stock disponible
                $fromStock = StoreStock::where('store_id', $fromStoreId)
                    ->where('product_id', $product->id)
                    ->first();

                if (!$fromStock || $fromStock->quantity < $quantity) {
                    throw new Exception("Stock insuffisant pour {$product->name} dans le magasin source");
                }

                // Obtenir le prix de coût
                $costPrice = $product->getCurrentPriceInStore($fromStoreId)?->cost_price ?? 0;
                $itemValue = $costPrice * $quantity;
                $totalValue += $itemValue;

                // Créer l'item de transfert
                $transfer->items()->create([
                    'product_id' => $product->id,
                    'quantity_requested' => $quantity,
                    'quantity_approved' => null,
                    'quantity_sent' => 0,
                    'quantity_received' => 0,
                    'unit_cost' => $costPrice,
                    'total_cost' => $itemValue,
                    'status' => 'pending'
                ]);
            }

            // Mettre à jour la valeur totale
            $transfer->update(['total_value' => $totalValue]);

            DB::commit();

            // Notifier les responsables
            $this->notifyTransferRequest($transfer);

            return $transfer;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Approuver une demande de transfert
     */
    public function approveTransfer(StockTransfer $transfer, array $approvedQuantities = [])
    {
        try {
            DB::beginTransaction();

            if ($transfer->status !== 'pending') {
                throw new Exception('Ce transfert ne peut plus être approuvé');
            }

            $transfer->update([
                'status' => 'approved',
                'approved_by' => Auth::id(),
                'approved_at' => now()
            ]);

            // Mettre à jour les quantités approuvées
            foreach ($transfer->items as $item) {
                $approvedQty = $approvedQuantities[$item->id] ?? $item->quantity_requested;
                
                // Vérifier que la quantité approuvée ne dépasse pas la demande
                if ($approvedQty > $item->quantity_requested) {
                    $approvedQty = $item->quantity_requested;
                }

                $item->update([
                    'quantity_approved' => $approvedQty,
                    'status' => $approvedQty > 0 ? 'approved' : 'rejected'
                ]);
            }

            DB::commit();

            // Notifier l'approbation
            $this->notifyTransferApproval($transfer);

            return $transfer;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Expédier les articles approuvés
     */
    public function shipTransfer(StockTransfer $transfer)
    {
        try {
            DB::beginTransaction();

            if ($transfer->status !== 'approved') {
                throw new Exception('Ce transfert doit être approuvé avant expédition');
            }

            foreach ($transfer->items()->where('status', 'approved')->get() as $item) {
                $quantityToShip = $item->quantity_approved;

                if ($quantityToShip <= 0) continue;

                // Vérifier le stock disponible
                $fromStock = StoreStock::where('store_id', $transfer->from_store_id)
                    ->where('product_id', $item->product_id)
                    ->first();

                if (!$fromStock || $fromStock->quantity < $quantityToShip) {
                    throw new Exception("Stock insuffisant pour expédier {$item->product->name}");
                }

                // Retirer du stock source
                $fromStock->removeStock($quantityToShip, "Transfert vers {$transfer->toStore->name} - #{$transfer->transfer_number}");

                // Mettre à jour l'item
                $item->update([
                    'quantity_sent' => $quantityToShip,
                    'status' => 'shipped',
                    'shipped_at' => now()
                ]);
            }

            $transfer->update([
                'status' => 'shipped',
                'shipped_by' => Auth::id(),
                'shipped_at' => now()
            ]);

            DB::commit();

            // Notifier l'expédition
            $this->notifyTransferShipment($transfer);

            return $transfer;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Recevoir les articles expédiés
     */
    public function receiveTransfer(StockTransfer $transfer, array $receivedQuantities = [])
    {
        try {
            DB::beginTransaction();

            if ($transfer->status !== 'shipped') {
                throw new Exception('Ce transfert doit être expédié avant réception');
            }

            foreach ($transfer->items()->where('status', 'shipped')->get() as $item) {
                $receivedQty = $receivedQuantities[$item->id] ?? $item->quantity_sent;
                
                // Vérifier que la quantité reçue ne dépasse pas la quantité expédiée
                if ($receivedQty > $item->quantity_sent) {
                    $receivedQty = $item->quantity_sent;
                }

                if ($receivedQty > 0) {
                    // Ajouter au stock destination
                    $toStock = StoreStock::firstOrCreate(
                        [
                            'store_id' => $transfer->to_store_id,
                            'product_id' => $item->product_id
                        ],
                        [
                            'quantity' => 0,
                            'min_stock_level' => 0,
                            'is_active' => true
                        ]
                    );

                    $toStock->addStock($receivedQty, "Transfert depuis {$transfer->fromStore->name} - #{$transfer->transfer_number}");
                }

                // Mettre à jour l'item
                $item->update([
                    'quantity_received' => $receivedQty,
                    'status' => 'received',
                    'received_at' => now()
                ]);
            }

            $transfer->update([
                'status' => 'completed',
                'received_by' => Auth::id(),
                'received_at' => now()
            ]);

            DB::commit();

            // Notifier la réception
            $this->notifyTransferCompletion($transfer);

            return $transfer;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Annuler un transfert
     */
    public function cancelTransfer(StockTransfer $transfer, string $reason)
    {
        try {
            DB::beginTransaction();

            if (in_array($transfer->status, ['completed', 'cancelled'])) {
                throw new Exception('Ce transfert ne peut plus être annulé');
            }

            // Si le transfert était expédié, remettre le stock
            if ($transfer->status === 'shipped') {
                foreach ($transfer->items()->where('status', 'shipped')->get() as $item) {
                    if ($item->quantity_sent > 0) {
                        $fromStock = StoreStock::where('store_id', $transfer->from_store_id)
                            ->where('product_id', $item->product_id)
                            ->first();

                        if ($fromStock) {
                            $fromStock->addStock($item->quantity_sent, "Annulation transfert #{$transfer->transfer_number} - {$reason}");
                        }
                    }
                }
            }

            $transfer->update([
                'status' => 'cancelled',
                'cancelled_by' => Auth::id(),
                'cancelled_at' => now(),
                'cancellation_reason' => $reason
            ]);

            $transfer->items()->update(['status' => 'cancelled']);

            DB::commit();

            // Notifier l'annulation
            $this->notifyTransferCancellation($transfer);

            return $transfer;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Générer un numéro de transfert unique
     */
    private function generateTransferNumber(): string
    {
        $prefix = 'TRF';
        $date = now()->format('Ymd');
        $sequence = StockTransfer::whereDate('created_at', today())->count() + 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Notifier la demande de transfert
     */
    private function notifyTransferRequest(StockTransfer $transfer)
    {
        // Implémenter les notifications (email, SMS, etc.)
        // Notifier les managers du magasin destination
    }

    /**
     * Notifier l'approbation du transfert
     */
    private function notifyTransferApproval(StockTransfer $transfer)
    {
        // Implémenter les notifications
    }

    /**
     * Notifier l'expédition du transfert
     */
    private function notifyTransferShipment(StockTransfer $transfer)
    {
        // Implémenter les notifications
    }

    /**
     * Notifier la finalisation du transfert
     */
    private function notifyTransferCompletion(StockTransfer $transfer)
    {
        // Implémenter les notifications
    }

    /**
     * Notifier l'annulation du transfert
     */
    private function notifyTransferCancellation(StockTransfer $transfer)
    {
        // Implémenter les notifications
    }
}
