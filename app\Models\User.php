<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'is_active',
        'store_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relations
     */
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function managedStores()
    {
        return $this->hasMany(Store::class, 'manager_id');
    }

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Vérifie si l'utilisateur est assigné à un magasin
     */
    public function hasStore(): bool
    {
        return $this->store_id !== null;
    }

    /**
     * Vérifie si l'utilisateur est le gérant d'un magasin
     */
    public function isStoreManager($storeId = null): bool
    {
        if ($storeId) {
            return $this->managedStores()->where('id', $storeId)->exists();
        }
        return $this->managedStores()->exists();
    }

    /**
     * Obtenir tous les magasins accessibles pour l'utilisateur
     */
    public function getAccessibleStores()
    {
        if ($this->hasRole('superAdmin')) {
            return Store::active()->get();
        }

        if ($this->store_id) {
            return Store::where('id', $this->store_id)->get();
        }

        return collect();
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function creditPayments()
    {
        return $this->hasMany(CreditPayment::class);
    }

}
