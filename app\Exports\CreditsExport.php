<?php

namespace App\Exports;

use App\Models\Credit;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CreditsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $status;

    public function __construct($status = null)
    {
        $this->status = $status;
    }

    public function collection()
    {
        $query = Credit::with(['customer', 'sale']);

        if ($this->status) {
            if ($this->status === 'overdue') {
                $query->overdue();
            } else {
                $query->where('status', $this->status);
            }
        }

        return $query->get();
    }

    public function headings(): array
    {
        return [
            'N° Vente',
            'Date création',
            'Client',
            'Téléphone client',
            'Montant total (FCFA)',
            'Montant payé (FCFA)',
            'Reste à payer (FCFA)',
            'Date échéance',
            'Jours de retard',
            'Statut',
            'Notes'
        ];
    }

    public function map($credit): array
    {
        $daysOverdue = 0;
        if ($credit->due_date && $credit->due_date->isPast() && $credit->status === 'active') {
            $daysOverdue = $credit->due_date->diffInDays(now());
        }

        return [
            $credit->sale->sale_number,
            $credit->created_at->format('d/m/Y H:i'),
            $credit->customer->name,
            $credit->customer->phone ?? 'Non renseigné',
            $credit->total_amount,
            $credit->amount_paid,
            $credit->remaining_balance,
            $credit->due_date ? $credit->due_date->format('d/m/Y') : 'Non définie',
            $daysOverdue > 0 ? $daysOverdue : '',
            $this->getStatusLabel($credit->status, $credit->isOverdue()),
            $credit->notes
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style pour l'en-tête
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F59E0B'],
                ],
            ],
        ];
    }

    private function getStatusLabel($status, $isOverdue)
    {
        if ($status === 'active' && $isOverdue) {
            return 'En retard';
        }

        return match($status) {
            'active' => 'Actif',
            'paid' => 'Payé',
            'cancelled' => 'Annulé',
            default => ucfirst($status)
        };
    }
}
