<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-receipt mr-2"></i>Détails de la vente - {{ $sale->sale_number }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('pos.receipt', $sale) }}" 
                   target="_blank"
                   class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>Imprimer reçu
                </a>
                <a href="{{ route('sales.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Informations générales -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-info-circle mr-2"></i>Informations générales
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Numéro de vente</label>
                            <p class="mt-1 text-sm text-gray-900 font-medium">{{ $sale->sale_number }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Date et heure</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $sale->created_at->format('d/m/Y à H:i') }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Vendeur</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $sale->user->name }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Client</label>
                            <p class="mt-1 text-sm text-gray-900">
                                @if($sale->customer)
                                    <a href="{{ route('customers.show', $sale->customer) }}" 
                                       class="text-blue-600 hover:text-blue-800">
                                        {{ $sale->customer->name }}
                                    </a>
                                @else
                                    <span class="text-gray-400">Client anonyme</span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Articles vendus -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">
                                <i class="fas fa-shopping-cart mr-2"></i>Articles vendus
                            </h3>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Produit
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Prix unitaire
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Quantité
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Total
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($sale->saleItems as $item)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $item->product_name }}
                                                </div>
                                                @if($item->product)
                                                <div class="text-sm text-gray-500">
                                                    Code: {{ $item->product->barcode ?? 'N/A' }}
                                                </div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ number_format($item->unit_price, 0, ',', ' ') }} FCFA
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $item->quantity }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ number_format($item->total_price, 0, ',', ' ') }} FCFA
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Résumé et paiement -->
                <div class="space-y-6">
                    <!-- Totaux -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">
                                <i class="fas fa-calculator mr-2"></i>Résumé
                            </h3>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Sous-total:</span>
                                    <span class="text-sm font-medium">{{ number_format($sale->subtotal, 0, ',', ' ') }} FCFA</span>
                                </div>
                                
                                @if($sale->tax_amount > 0)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">TVA:</span>
                                    <span class="text-sm font-medium">{{ number_format($sale->tax_amount, 0, ',', ' ') }} FCFA</span>
                                </div>
                                @endif
                                
                                @if($sale->discount_amount > 0)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Remise:</span>
                                    <span class="text-sm font-medium text-red-600">-{{ number_format($sale->discount_amount, 0, ',', ' ') }} FCFA</span>
                                </div>
                                @endif
                                
                                <div class="border-t pt-3">
                                    <div class="flex justify-between">
                                        <span class="text-base font-medium text-gray-900">Total:</span>
                                        <span class="text-base font-bold text-gray-900">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations de paiement -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">
                                <i class="fas fa-credit-card mr-2"></i>Paiement
                            </h3>
                            
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Mode de paiement</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        @switch($sale->payment_method)
                                            @case('cash')
                                                <i class="fas fa-money-bill-wave text-green-600 mr-2"></i>Espèces
                                                @break
                                            @case('credit')
                                                <i class="fas fa-credit-card text-yellow-600 mr-2"></i>Crédit
                                                @break
                                            @default
                                                {{ ucfirst($sale->payment_method) }}
                                        @endswitch
                                    </p>
                                </div>
                                
                                @if($sale->payment_method !== 'credit')
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Montant reçu</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ number_format($sale->amount_paid, 0, ',', ' ') }} FCFA</p>
                                </div>
                                
                                @if($sale->change_amount > 0)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Monnaie rendue</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ number_format($sale->change_amount, 0, ',', ' ') }} FCFA</p>
                                </div>
                                @endif
                                @endif
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Statut</label>
                                    <p class="mt-1">
                                        @switch($sale->status)
                                            @case('completed')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle mr-1"></i>Terminée
                                                </span>
                                                @break
                                            @case('pending')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    <i class="fas fa-clock mr-1"></i>En attente
                                                </span>
                                                @break
                                            @case('cancelled')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i class="fas fa-times-circle mr-1"></i>Annulée
                                                </span>
                                                @break
                                        @endswitch
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations de crédit si applicable -->
                    @if($sale->payment_method === 'credit' && $sale->credit)
                    <div class="bg-yellow-50 overflow-hidden shadow-sm sm:rounded-lg border border-yellow-200">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-yellow-800 mb-4">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Informations de crédit
                            </h3>
                            
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-yellow-700">Montant total du crédit</label>
                                    <p class="mt-1 text-sm text-yellow-900 font-medium">{{ number_format($sale->credit->total_amount, 0, ',', ' ') }} FCFA</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-yellow-700">Montant payé</label>
                                    <p class="mt-1 text-sm text-yellow-900">{{ number_format($sale->credit->amount_paid, 0, ',', ' ') }} FCFA</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-yellow-700">Montant restant</label>
                                    <p class="mt-1 text-sm text-yellow-900 font-medium">{{ number_format($sale->credit->remaining_balance, 0, ',', ' ') }} FCFA</p>
                                </div>
                                
                                @if($sale->credit->due_date)
                                <div>
                                    <label class="block text-sm font-medium text-yellow-700">Date d'échéance</label>
                                    <p class="mt-1 text-sm text-yellow-900">{{ $sale->credit->due_date->format('d/m/Y') }}</p>
                                </div>
                                @endif
                                
                                <div>
                                    <label class="block text-sm font-medium text-yellow-700">Statut du crédit</label>
                                    <p class="mt-1">
                                        @switch($sale->credit->status)
                                            @case('active')
                                                @if($sale->credit->isOverdue())
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>En retard
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        <i class="fas fa-clock mr-1"></i>Actif
                                                    </span>
                                                @endif
                                                @break
                                            @case('paid')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle mr-1"></i>Payé
                                                </span>
                                                @break
                                        @endswitch
                                    </p>
                                </div>
                                
                                @if($sale->credit->status === 'active')
                                <div class="pt-3 border-t border-yellow-200">
                                    <a href="{{ route('credits.show', $sale->credit) }}" 
                                       class="inline-flex items-center px-3 py-2 border border-yellow-300 shadow-sm text-sm leading-4 font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                        <i class="fas fa-eye mr-2"></i>Voir le crédit
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Notes -->
            @if($sale->notes)
            <div class="mt-6 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-sticky-note mr-2"></i>Notes
                    </h3>
                    <p class="text-sm text-gray-700">{{ $sale->notes }}</p>
                </div>
            </div>
            @endif
        </div>
    </div>
</x-app-layout>
