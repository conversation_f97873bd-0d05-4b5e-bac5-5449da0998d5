<?php

namespace App\Policies;

use App\Models\Store;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class StorePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any stores.
     */
    public function viewAny(User $user)
    {
        return $user->hasAnyRole(['superAdmin', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can view the store.
     */
    public function view(User $user, Store $store)
    {
        // Super admin peut tout voir
        if ($user->hasRole('superAdmin')) {
            return true;
        }

        // Admin peut voir tous les magasins
        if ($user->hasRole('admin')) {
            return true;
        }

        // Manager peut voir seulement les magasins qu'il gère
        if ($user->hasRole('manager')) {
            return $user->managedStores()->where('id', $store->id)->exists();
        }

        // Employé peut voir seulement son magasin assigné
        return $user->store_id === $store->id;
    }

    /**
     * Determine whether the user can create stores.
     */
    public function create(User $user)
    {
        return $user->hasAnyRole(['superAdmin', 'admin']);
    }

    /**
     * Determine whether the user can update the store.
     */
    public function update(User $user, Store $store)
    {
        // Super admin peut tout modifier
        if ($user->hasRole('superAdmin')) {
            return true;
        }

        // Admin peut modifier tous les magasins
        if ($user->hasRole('admin')) {
            return true;
        }

        // Manager peut modifier seulement les magasins qu'il gère
        if ($user->hasRole('manager')) {
            return $user->managedStores()->where('id', $store->id)->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can delete the store.
     */
    public function delete(User $user, Store $store)
    {
        // Seuls les super admin peuvent supprimer des magasins
        if (!$user->hasRole('superAdmin')) {
            return false;
        }

        // Ne peut pas supprimer un magasin avec des ventes
        if ($store->sales()->exists()) {
            return false;
        }

        // Ne peut pas supprimer un magasin avec des utilisateurs assignés
        if ($store->users()->exists()) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can access store operations.
     */
    public function access(User $user, Store $store)
    {
        // Super admin peut accéder à tous les magasins
        if ($user->hasRole('superAdmin')) {
            return true;
        }

        // Vérifier si l'utilisateur est assigné à ce magasin
        if ($user->store_id === $store->id) {
            return true;
        }

        // Vérifier si l'utilisateur est manager de ce magasin
        if ($user->managedStores()->where('id', $store->id)->exists()) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can manage store stocks.
     */
    public function manageStock(User $user, Store $store)
    {
        if (!$this->access($user, $store)) {
            return false;
        }

        return $user->hasAnyRole(['superAdmin', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can manage store prices.
     */
    public function managePrices(User $user, Store $store)
    {
        if (!$this->access($user, $store)) {
            return false;
        }

        return $user->hasAnyRole(['superAdmin', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can view store reports.
     */
    public function viewReports(User $user, Store $store)
    {
        if (!$this->access($user, $store)) {
            return false;
        }

        return $user->hasAnyRole(['superAdmin', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can transfer stock between stores.
     */
    public function transferStock(User $user, Store $fromStore, Store $toStore = null)
    {
        // Super admin peut faire tous les transferts
        if ($user->hasRole('superAdmin')) {
            return true;
        }

        // Admin peut faire tous les transferts
        if ($user->hasRole('admin')) {
            return true;
        }

        // Manager peut transférer seulement depuis les magasins qu'il gère
        if ($user->hasRole('manager')) {
            $canAccessFrom = $user->managedStores()->where('id', $fromStore->id)->exists();
            $canAccessTo = $toStore ? $user->managedStores()->where('id', $toStore->id)->exists() : true;
            
            return $canAccessFrom && $canAccessTo;
        }

        return false;
    }

    /**
     * Determine whether the user can view financial data.
     */
    public function viewFinancials(User $user, Store $store)
    {
        if (!$this->access($user, $store)) {
            return false;
        }

        return $user->hasAnyRole(['superAdmin', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can manage store settings.
     */
    public function manageSettings(User $user, Store $store)
    {
        // Super admin peut gérer tous les paramètres
        if ($user->hasRole('superAdmin')) {
            return true;
        }

        // Admin peut gérer tous les paramètres
        if ($user->hasRole('admin')) {
            return true;
        }

        // Manager peut gérer seulement les paramètres de ses magasins
        if ($user->hasRole('manager')) {
            return $user->managedStores()->where('id', $store->id)->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can assign users to store.
     */
    public function assignUsers(User $user, Store $store)
    {
        return $user->hasAnyRole(['superAdmin', 'admin']) || 
               ($user->hasRole('manager') && $user->managedStores()->where('id', $store->id)->exists());
    }

    /**
     * Determine whether the user can view store analytics.
     */
    public function viewAnalytics(User $user, Store $store)
    {
        if (!$this->access($user, $store)) {
            return false;
        }

        return $user->hasAnyRole(['superAdmin', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can export store data.
     */
    public function exportData(User $user, Store $store)
    {
        if (!$this->access($user, $store)) {
            return false;
        }

        return $user->hasAnyRole(['superAdmin', 'admin', 'manager']);
    }
}
