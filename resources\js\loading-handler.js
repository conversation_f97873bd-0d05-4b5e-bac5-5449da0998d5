// Gestionnaire de chargement pour les actions
class LoadingHandler {
    constructor() {
        this.loadingTexts = {
            'show': 'Chargement des détails...',
            'edit': 'Chargement de l\'édition...',
            'new-sale': 'Redirection vers la vente...',
            'payment': 'Ouverture du paiement...',
            'history': 'Chargement de l\'historique...',
            'delete': 'Suppression...',
            'save': 'Enregistrement...',
            'submit': 'Envoi en cours...',
            'print': 'Préparation de l\'impression...',
            'export': 'Préparation de l\'export...',
            'default': 'Chargement...'
        };

        this.init();
    }

    setLoading(button, isLoading, customLoadingText) {
        if (isLoading) {
            button.dataset.originalHtml = button.innerHTML;
            button.disabled = true;
            button.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${customLoadingText || 'Chargement...'}`;
            button.classList.add('opacity-75', 'cursor-not-allowed');
        } else {
            button.innerHTML = button.dataset.originalHtml;
            button.disabled = false;
            button.classList.remove('opacity-75', 'cursor-not-allowed');
        }
    }

    handleActionClick(element, action) {
        if (element.dataset.processing) {
            return false;
        }

        const loadingText = this.loadingTexts[action] || this.loadingTexts['default'];
        element.dataset.processing = 'true';
        this.setLoading(element, true, loadingText);
        return true;
    }

    init() {
        // Protéger tous les liens avec data-action
        document.querySelectorAll('a[data-action]').forEach(link => {
            link.addEventListener('click', (e) => {
                if (!this.handleActionClick(link, link.dataset.action)) {
                    e.preventDefault();
                }
            });
        });

        // Protéger tous les boutons avec data-action
        document.querySelectorAll('button[data-action]').forEach(button => {
            if (!button.hasAttribute('data-no-loading')) {
                const originalClick = button.onclick;
                button.onclick = (e) => {
                    if (!this.handleActionClick(button, button.dataset.action)) {
                        e.preventDefault();
                        return;
                    }
                    if (originalClick) {
                        originalClick.call(button, e);
                    }
                };
            }
        });

        // Protéger tous les formulaires
        document.querySelectorAll('form').forEach(form => {
            if (!form.hasAttribute('data-no-loading')) {
                form.addEventListener('submit', (e) => {
                    const submitButton = form.querySelector('button[type="submit"]');
                    if (form.dataset.processing) {
                        e.preventDefault();
                        return;
                    }
                    if (submitButton) {
                        form.dataset.processing = 'true';
                        const action = submitButton.dataset.action || 'submit';
                        this.handleActionClick(submitButton, action);
                    }
                });
            }
        });

        // Réinitialiser l'état lors du chargement de la page
        window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
                document.querySelectorAll('[data-processing]').forEach(el => {
                    delete el.dataset.processing;
                    if (el.tagName === 'BUTTON') {
                        this.setLoading(el, false);
                    }
                });
            }
        });
    }
}

// Initialiser le gestionnaire au chargement du DOM
document.addEventListener('DOMContentLoaded', () => {
    window.loadingHandler = new LoadingHandler();
});
