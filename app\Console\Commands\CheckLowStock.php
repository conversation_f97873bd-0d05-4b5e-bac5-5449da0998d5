<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use App\Models\User;
use App\Notifications\LowStockAlert;
use Illuminate\Support\Facades\Notification;

class CheckLowStock extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:check-low-stock';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vérifier les produits avec un stock faible et envoyer des notifications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Vérification du stock faible...');

        // Récupérer les produits avec un stock faible
        $lowStockProducts = Product::lowStock()->active()->get();

        if ($lowStockProducts->isEmpty()) {
            $this->info('Aucun produit avec un stock faible trouvé.');
            return 0;
        }

        $this->warn("Trouvé {$lowStockProducts->count()} produit(s) avec un stock faible :");

        foreach ($lowStockProducts as $product) {
            $this->line("- {$product->name} : {$product->stock_quantity} {$product->unit} (Seuil: {$product->min_stock_level})");
        }

        // Envoyer la notification aux administrateurs
        $admins = User::role(['superAdmin', 'admin', 'manager'])->get();

        if ($admins->isNotEmpty()) {
            Notification::send($admins, new LowStockAlert($lowStockProducts));
            $this->info("Notification envoyée à {$admins->count()} administrateur(s).");
        }

        return 0;
    }
}
