<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport des Ventes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
        }
        .period {
            font-size: 14px;
            color: #888;
        }
        .stats-grid {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .stats-row {
            display: table-row;
        }
        .stats-cell {
            display: table-cell;
            width: 25%;
            padding: 15px;
            text-align: center;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .stats-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .stats-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin: 30px 0 15px 0;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        .payment-methods {
            margin-bottom: 30px;
        }
        .payment-method {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .payment-method:last-child {
            border-bottom: none;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 11px;
            text-transform: uppercase;
            color: #333;
        }
        td {
            font-size: 11px;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        .badge-cash {
            background-color: #d4edda;
            color: #155724;
        }
        .badge-mobile {
            background-color: #cce7ff;
            color: #004085;
        }
        .badge-credit {
            background-color: #fff3cd;
            color: #856404;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #888;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Système POS') }}</div>
        <div class="report-title">Rapport des Ventes</div>
        <div class="period">
            Période: {{ $dateFrom->format('d/m/Y') }} - {{ $dateTo->format('d/m/Y') }}
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="stats-grid">
        <div class="stats-row">
            <div class="stats-cell">
                <div class="stats-value">{{ number_format($totalSales, 0, ',', ' ') }} FCFA</div>
                <div class="stats-label">Total des ventes</div>
            </div>
            <div class="stats-cell">
                <div class="stats-value">{{ $sales->count() }}</div>
                <div class="stats-label">Nombre de ventes</div>
            </div>
            <div class="stats-cell">
                <div class="stats-value">{{ number_format($totalDiscount, 0, ',', ' ') }} FCFA</div>
                <div class="stats-label">Total remises</div>
            </div>
            <div class="stats-cell">
                <div class="stats-value">{{ number_format($totalTax, 0, ',', ' ') }} FCFA</div>
                <div class="stats-label">Total TVA</div>
            </div>
        </div>
    </div>

    <!-- Répartition par mode de paiement -->
    <div class="section-title">Répartition par mode de paiement</div>
    <div class="payment-methods">
        @foreach($salesByPaymentMethod as $method => $data)
        <div class="payment-method">
            <span>
                @switch($method)
                    @case('cash') Espèces @break
                    @case('mobile_money') Mobile Money @break
                    @case('credit') Crédit @break
                    @default {{ ucfirst($method) }}
                @endswitch
            </span>
            <span>{{ number_format($data['total'], 0, ',', ' ') }} FCFA ({{ $data['count'] }} vente(s))</span>
        </div>
        @endforeach
    </div>

    <!-- Détail des ventes -->
    <div class="section-title">Détail des ventes</div>
    @if($sales->count() > 0)
    <table>
        <thead>
            <tr>
                <th>N° Vente</th>
                <th>Date</th>
                <th>Client</th>
                <th class="text-right">Montant</th>
                <th class="text-center">Paiement</th>
                <th>Caissier</th>
            </tr>
        </thead>
        <tbody>
            @foreach($sales as $sale)
            <tr>
                <td>{{ $sale->sale_number }}</td>
                <td>{{ $sale->created_at->format('d/m/Y H:i') }}</td>
                <td>{{ $sale->customer ? $sale->customer->name : 'Client anonyme' }}</td>
                <td class="text-right">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</td>
                <td class="text-center">
                    <span class="badge 
                        @switch($sale->payment_method)
                            @case('cash') badge-cash @break
                            @case('mobile_money') badge-mobile @break
                            @case('credit') badge-credit @break
                        @endswitch">
                        @switch($sale->payment_method)
                            @case('cash') Espèces @break
                            @case('mobile_money') Mobile Money @break
                            @case('credit') Crédit @break
                            @default {{ ucfirst($sale->payment_method) }}
                        @endswitch
                    </span>
                </td>
                <td>{{ $sale->user->name }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @else
    <p style="text-align: center; color: #888; padding: 40px;">Aucune vente trouvée pour cette période</p>
    @endif

    <!-- Statistiques détaillées -->
    @if($sales->count() > 0)
    <div class="section-title">Statistiques détaillées</div>
    <table style="width: 60%; margin: 0 auto;">
        <tr>
            <td><strong>Vente moyenne:</strong></td>
            <td class="text-right">{{ number_format($totalSales / $sales->count(), 0, ',', ' ') }} FCFA</td>
        </tr>
        <tr>
            <td><strong>Vente la plus élevée:</strong></td>
            <td class="text-right">{{ number_format($sales->max('total_amount'), 0, ',', ' ') }} FCFA</td>
        </tr>
        <tr>
            <td><strong>Vente la plus faible:</strong></td>
            <td class="text-right">{{ number_format($sales->min('total_amount'), 0, ',', ' ') }} FCFA</td>
        </tr>
        <tr>
            <td><strong>Clients uniques:</strong></td>
            <td class="text-right">{{ $sales->whereNotNull('customer_id')->unique('customer_id')->count() }}</td>
        </tr>
    </table>
    @endif

    <div class="footer">
        <p>Rapport généré le {{ now()->format('d/m/Y à H:i') }} par {{ auth()->user()->name }}</p>
        <p>{{ config('app.name') }} - Système de Point de Vente</p>
    </div>
</body>
</html>
