<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Supprimer la colonne credit_limit
            $table->dropColumn('credit_limit');

            // Ajouter la colonne due_date
            $table->date('due_date')->nullable()->after('current_balance');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Remettre la colonne credit_limit
            $table->decimal('credit_limit', 10, 2)->default(0)->after('current_balance');

            // Supprimer la colonne due_date
            $table->dropColumn('due_date');
        });
    }
};
