<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-chart-bar mr-2"></i>{{ __('Analytics de Stock') }}
                </h2>
                @if($userStore && !$canViewAllStores)
                    <p class="text-sm text-gray-600 mt-1">
                        <i class="fas fa-store mr-1"></i>{{ $userStore->name }} ({{ $userStore->code }})
                    </p>
                @endif
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('reports.index') }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-arrow-left mr-1"></i>Retour
                </a>
                <button onclick="exportStockReport()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-download mr-1"></i>Exporter
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Filtres -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-filter mr-2"></i>Filtres
                    </h3>
                    
                    <form method="GET" action="{{ route('reports.stock-analytics') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        
                        @if($canViewAllStores)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Magasin</label>
                            <select name="store_id" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Tous les magasins</option>
                                @foreach($stores as $store)
                                    <option value="{{ $store->id }}" {{ $storeId == $store->id ? 'selected' : '' }}>
                                        {{ $store->name }} ({{ $store->code }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @endif
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Statut du Stock</label>
                            <select name="stock_status" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Tous les statuts</option>
                                <option value="normal" {{ request('stock_status') == 'normal' ? 'selected' : '' }}>Stock Normal</option>
                                <option value="low" {{ request('stock_status') == 'low' ? 'selected' : '' }}>Stock Faible</option>
                                <option value="out" {{ request('stock_status') == 'out' ? 'selected' : '' }}>Rupture de Stock</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Catégorie</label>
                            <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Toutes les catégories</option>
                                @foreach($stockAnalysis->pluck('category')->unique()->filter() as $category)
                                    <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                                        {{ $category }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="flex items-end">
                            <button type="submit" 
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                                <i class="fas fa-search mr-2"></i>Filtrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Métriques de stock -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-boxes text-green-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Produits en Stock</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $stockAnalysis->where('stock_status', 'normal')->count() }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Stock Faible</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $stockAnalysis->where('stock_status', 'low')->count() }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-times-circle text-red-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Rupture de Stock</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $stockAnalysis->where('stock_status', 'out')->count() }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-dollar-sign text-blue-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Valeur du Stock</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stockAnalysis->sum('stock_value'), 0, ',', ' ') }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Graphiques -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                
                <!-- Répartition par statut -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-chart-pie mr-2"></i>Répartition par Statut
                        </h3>
                        <div class="h-80">
                            <canvas id="stockStatusChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Valeur par catégorie -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-chart-bar mr-2"></i>Valeur par Catégorie
                        </h3>
                        <div class="h-80">
                            <canvas id="categoryValueChart"></canvas>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Tableau détaillé des stocks -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-table mr-2"></i>Détail des Stocks
                        </h3>
                        <div class="flex space-x-2">
                            <button onclick="toggleView('table')" id="tableViewBtn" 
                                    class="px-3 py-1 bg-blue-600 text-white rounded text-sm">
                                <i class="fas fa-table mr-1"></i>Tableau
                            </button>
                            <button onclick="toggleView('cards')" id="cardsViewBtn" 
                                    class="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm">
                                <i class="fas fa-th-large mr-1"></i>Cartes
                            </button>
                        </div>
                    </div>
                    
                    <!-- Vue tableau -->
                    <div id="tableView" class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produit</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Magasin</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seuil</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valeur</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emplacement</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($stockAnalysis as $stock)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">{{ $stock->product_name }}</div>
                                        <div class="text-sm text-gray-500">{{ $stock->category ?? 'Sans catégorie' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $stock->store_name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-medium {{ $stock->quantity <= 0 ? 'text-red-600' : ($stock->quantity <= $stock->low_stock_threshold ? 'text-yellow-600' : 'text-green-600') }}">
                                            {{ number_format($stock->quantity) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ number_format($stock->low_stock_threshold) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ number_format($stock->stock_value, 0, ',', ' ') }} FCFA
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($stock->stock_status == 'out')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <i class="fas fa-times-circle mr-1"></i>Rupture
                                            </span>
                                        @elseif($stock->stock_status == 'low')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <i class="fas fa-exclamation-triangle mr-1"></i>Faible
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <i class="fas fa-check-circle mr-1"></i>Normal
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $stock->location ?? 'Non défini' }}
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        Aucun stock trouvé avec les filtres sélectionnés
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Vue cartes -->
                    <div id="cardsView" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($stockAnalysis as $stock)
                        <div class="border rounded-lg p-4 {{ $stock->stock_status == 'out' ? 'border-red-200 bg-red-50' : ($stock->stock_status == 'low' ? 'border-yellow-200 bg-yellow-50' : 'border-green-200 bg-green-50') }}">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-medium text-gray-900">{{ $stock->product_name }}</h4>
                                @if($stock->stock_status == 'out')
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i>Rupture
                                    </span>
                                @elseif($stock->stock_status == 'low')
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>Faible
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Normal
                                    </span>
                                @endif
                            </div>
                            <p class="text-sm text-gray-600 mb-2">{{ $stock->category ?? 'Sans catégorie' }}</p>
                            <div class="space-y-1 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Stock:</span>
                                    <span class="font-medium {{ $stock->quantity <= 0 ? 'text-red-600' : ($stock->quantity <= $stock->low_stock_threshold ? 'text-yellow-600' : 'text-green-600') }}">
                                        {{ number_format($stock->quantity) }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Seuil:</span>
                                    <span>{{ number_format($stock->low_stock_threshold) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Valeur:</span>
                                    <span class="font-medium">{{ number_format($stock->stock_value, 0, ',', ' ') }} FCFA</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Magasin:</span>
                                    <span>{{ $stock->store_name }}</span>
                                </div>
                                @if($stock->location)
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Emplacement:</span>
                                    <span>{{ $stock->location }}</span>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeStockStatusChart();
            initializeCategoryValueChart();
        });

        function initializeStockStatusChart() {
            const ctx = document.getElementById('stockStatusChart').getContext('2d');
            const stockAnalysis = @json($stockAnalysis);
            
            const statusCounts = {
                normal: stockAnalysis.filter(item => item.stock_status === 'normal').length,
                low: stockAnalysis.filter(item => item.stock_status === 'low').length,
                out: stockAnalysis.filter(item => item.stock_status === 'out').length
            };
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Stock Normal', 'Stock Faible', 'Rupture de Stock'],
                    datasets: [{
                        data: [statusCounts.normal, statusCounts.low, statusCounts.out],
                        backgroundColor: ['#22c55e', '#eab308', '#ef4444']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function initializeCategoryValueChart() {
            const ctx = document.getElementById('categoryValueChart').getContext('2d');
            const stockAnalysis = @json($stockAnalysis);
            
            // Grouper par catégorie
            const categoryData = {};
            stockAnalysis.forEach(item => {
                const category = item.category || 'Sans catégorie';
                if (!categoryData[category]) {
                    categoryData[category] = 0;
                }
                categoryData[category] += parseFloat(item.stock_value || 0);
            });
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(categoryData),
                    datasets: [{
                        label: 'Valeur du Stock (FCFA)',
                        data: Object.values(categoryData),
                        backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        borderColor: 'rgb(59, 130, 246)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function toggleView(viewType) {
            const tableView = document.getElementById('tableView');
            const cardsView = document.getElementById('cardsView');
            const tableBtn = document.getElementById('tableViewBtn');
            const cardsBtn = document.getElementById('cardsViewBtn');
            
            if (viewType === 'table') {
                tableView.classList.remove('hidden');
                cardsView.classList.add('hidden');
                tableBtn.classList.remove('bg-gray-300', 'text-gray-700');
                tableBtn.classList.add('bg-blue-600', 'text-white');
                cardsBtn.classList.remove('bg-blue-600', 'text-white');
                cardsBtn.classList.add('bg-gray-300', 'text-gray-700');
            } else {
                tableView.classList.add('hidden');
                cardsView.classList.remove('hidden');
                cardsBtn.classList.remove('bg-gray-300', 'text-gray-700');
                cardsBtn.classList.add('bg-blue-600', 'text-white');
                tableBtn.classList.remove('bg-blue-600', 'text-white');
                tableBtn.classList.add('bg-gray-300', 'text-gray-700');
            }
        }

        function exportStockReport() {
            // Fonction d'export à implémenter
            alert('Fonction d\'export en cours de développement');
        }
    </script>
    @endpush
</x-app-layout>
