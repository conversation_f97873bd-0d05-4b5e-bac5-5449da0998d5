<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSaleRequest;
use App\Http\Requests\UpdateSaleRequest;
use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Store;
use App\Services\SaleService;
use App\Events\SaleCreated;
use App\Events\SaleCancelled;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class SaleController extends Controller
{
    protected $saleService;

    public function __construct(SaleService $saleService)
    {
        $this->saleService = $saleService;
        $this->middleware('auth');
        $this->middleware('check.store.access');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Sale::with(['customer', 'user', 'saleItems.product', 'store'])
            ->where('store_id', session('current_store_id'));

        // Appliquer les restrictions basées sur le rôle
        if (!auth()->user()->hasRole(['admin', 'super-admin'])) {
            $query->where('user_id', auth()->id());
        }

        // Filtres avancés
        $query->when($request->date_from, function($q) use ($request) {
            return $q->whereDate('created_at', '>=', $request->date_from);
        })
        ->when($request->date_to, function($q) use ($request) {
            return $q->whereDate('created_at', '<=', $request->date_to);
        })
        ->when($request->payment_method, function($q) use ($request) {
            return $q->where('payment_method', $request->payment_method);
        })
        ->when($request->customer_id, function($q) use ($request) {
            return $q->where('customer_id', $request->customer_id);
        })
        ->when($request->status, function($q) use ($request) {
            return $q->where('status', $request->status);
        })
        ->when($request->search, function($q) use ($request) {
            return $q->where(function($query) use ($request) {
                $query->where('sale_number', 'like', "%{$request->search}%")
                    ->orWhereHas('customer', function($q) use ($request) {
                        $q->where('name', 'like', "%{$request->search}%")
                            ->orWhere('phone', 'like', "%{$request->search}%");
                    });
            });
        });

        // Statistiques
        $statistics = [
            'total_sales' => $query->sum('total_amount'),
            'sales_count' => $query->count(),
            'average_sale' => $query->avg('total_amount'),
            'cash_sales' => $query->where('payment_method', 'cash')->sum('total_amount'),
            'credit_sales' => $query->where('payment_method', 'credit')->sum('total_amount')
        ];

        $sales = $query->latest()->paginate(20);
        $customers = Customer::active()->where('store_id', session('current_store_id'))->get();
        $paymentMethods = Sale::PAYMENT_METHODS;

        return view('sales.index', compact('sales', 'statistics', 'customers', 'paymentMethods'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $store = Store::findOrFail(session('current_store_id'));
        $customers = Customer::active()->where('store_id', $store->id)->get();
        $products = Product::whereHas('storeStocks', function($query) use ($store) {
            $query->where('store_id', $store->id)
                  ->where('quantity', '>', 0);
        })->with(['storeStocks' => function($query) use ($store) {
            $query->where('store_id', $store->id);
        }, 'productPrices' => function($query) use ($store) {
            $query->where('store_id', $store->id)
                  ->where('is_active', true);
        }])->get();

        return view('sales.create', compact('customers', 'products', 'store'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSaleRequest $request)
    {
        try {
            DB::beginTransaction();

            $sale = $this->saleService->createSale(
                $request->validated(),
                session('current_store_id'),
                auth()->id()
            );

            DB::commit();

            event(new SaleCreated($sale));

            return response()->json([
                'success' => true,
                'message' => 'Vente enregistrée avec succès',
                'sale' => $sale->load('items.product', 'customer', 'store')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Sale $sale)
    {
        $this->authorize('view', $sale);
        
        $sale->load(['customer', 'user', 'saleItems.product', 'credit', 'store']);
        
        return view('sales.show', compact('sale'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSaleRequest $request, Sale $sale)
    {
        $this->authorize('update', $sale);

        try {
            DB::beginTransaction();

            $sale = $this->saleService->updateSale($sale, $request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Vente mise à jour avec succès',
                'sale' => $sale->fresh()
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Cancel the specified sale.
     */
    public function cancel(Sale $sale)
    {
        $this->authorize('cancel', $sale);

        try {
            DB::beginTransaction();

            $this->saleService->cancelSale($sale);

            DB::commit();

            event(new SaleCancelled($sale));

            return response()->json([
                'success' => true,
                'message' => 'Vente annulée avec succès'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Show receipt for a sale
     */
    public function receipt(Sale $sale)
    {
        $this->authorize('view', $sale);
        
        $sale->load(['saleItems.product', 'customer', 'user', 'store']);
        
        return view('sales.receipt', compact('sale'));
    }

    public function dailyReport(Request $request)
    {
        $date = $request->date ?? now()->toDateString();
        $store = Store::findOrFail(session('current_store_id'));

        $report = $this->saleService->generateDailyReport($store, $date);

        return view('sales.reports.daily', compact('report', 'date', 'store'));
    }

    public function export(Request $request)
    {
        $this->authorize('export', Sale::class);

        return $this->saleService->exportSales(
            session('current_store_id'),
            $request->only(['date_from', 'date_to', 'format'])
        );
    }
}
