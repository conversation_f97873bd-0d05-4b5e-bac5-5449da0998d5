<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Supprimer les colonnes de stock qui sont maintenant gérées dans store_stocks
            $table->dropColumn(['stock_quantity', 'min_stock_level']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Restaurer les colonnes de stock
            $table->integer('stock_quantity')->default(0)->after('barcode');
            $table->integer('min_stock_level')->default(0)->after('stock_quantity');
        });
    }
};
