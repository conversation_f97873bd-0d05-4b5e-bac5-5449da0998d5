<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reçu - {{ $sale->sale_number }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            max-width: 300px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .receipt-info {
            margin-bottom: 15px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .items-table th,
        .items-table td {
            text-align: left;
            padding: 2px 0;
        }
        .items-table th {
            border-bottom: 1px solid #000;
            font-weight: bold;
        }
        .item-name {
            width: 40%;
        }
        .item-qty {
            width: 15%;
            text-align: center;
        }
        .item-unit-price {
            width: 20%;
            text-align: right;
        }
        .item-total {
            width: 25%;
            text-align: right;
        }
        .totals {
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-bottom: 15px;
        }
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        .total-final {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        .payment-info {
            margin-bottom: 15px;
        }
        .footer {
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 10px;
            font-size: 10px;
        }
        @media print {
            body {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ $sale->store->name ?? config('app.name') }}</div>
        @if($sale->store)
            <div>{{ $sale->store->address ?? 'Système de Point de Vente' }}</div>
            <div>Code magasin: {{ $sale->store->code }}</div>
            @if($sale->store->phone)
                <div>Tel: {{ $sale->store->phone }}</div>
            @else
                <div>Tel: +XXX XX XX XX XX</div>
            @endif
            @if($sale->store->email)
                <div>Email: {{ $sale->store->email }}</div>
            @endif
        @else
            <div>Système de Point de Vente</div>
            <div>Tel: +XXX XX XX XX XX</div>
        @endif
    </div>

    <div class="receipt-info">
        <div><strong>Reçu N°:</strong> {{ $sale->sale_number }}</div>
        <div><strong>Date:</strong> {{ $sale->created_at->format('d/m/Y H:i') }}</div>
        <div><strong>Caissier:</strong> {{ $sale->user->name }}</div>
        @if($sale->customer)
        <div><strong>Client:</strong> {{ $sale->customer->name }}</div>
        @endif
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th class="item-name">Article</th>
                <th class="item-qty">Qté</th>
                <th class="item-unit-price">P.U.</th>
                <th class="item-total">Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($sale->saleItems as $item)
            <tr>
                <td class="item-name">{{ $item->product_name }}</td>
                <td class="item-qty">{{ $item->quantity }}</td>
                <td class="item-unit-price">{{ number_format($item->unit_price, 0, ',', ' ') }}</td>
                <td class="item-total">{{ number_format($item->total_price, 0, ',', ' ') }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="totals">
        @if($sale->discount_amount > 0)
        <div class="total-line">
            <span>Total avant remise:</span>
            <span>{{ number_format($sale->subtotal, 0, ',', ' ') }} FCFA</span>
        </div>
        <div class="total-line">
            <span>Remise:</span>
            <span>-{{ number_format($sale->discount_amount, 0, ',', ' ') }} FCFA</span>
        </div>
        @endif
        <div class="total-line total-final">
            <span>TOTAL À PAYER:</span>
            <span>{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</span>
        </div>

        @if($sale->profit_amount && $sale->profit_amount > 0)
        <div class="total-line" style="font-size: 10px; color: #666; margin-top: 5px;">
            <span>Bénéfice réalisé:</span>
            <span>{{ number_format($sale->profit_amount, 0, ',', ' ') }} FCFA</span>
        </div>
        @endif
    </div>

    <div class="payment-info">
        <div><strong>Mode de paiement:</strong> 
            @switch($sale->payment_method)
                @case('cash')
                    Espèces
                    @break
                @case('mobile_money')
                    Mobile Money
                    @break
                @case('credit')
                    Crédit
                    @break
                @default
                    {{ ucfirst($sale->payment_method) }}
            @endswitch
        </div>
        
        @if($sale->payment_method !== 'credit')
        <div><strong>Montant reçu:</strong> {{ number_format($sale->amount_paid, 0, ',', ' ') }} FCFA</div>
        @if($sale->change_amount > 0)
        <div><strong>Monnaie:</strong> {{ number_format($sale->change_amount, 0, ',', ' ') }} FCFA</div>
        @endif
        @endif
        
        @if($sale->payment_method === 'credit')
        <div style="margin-top: 10px; padding: 5px; border: 1px solid #000;">
            <div><strong>VENTE À CRÉDIT</strong></div>
            <div>Montant dû: {{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</div>
            @if($sale->credit && $sale->credit->due_date)
            <div>Échéance: {{ $sale->credit->due_date->format('d/m/Y') }}</div>
            @endif
        </div>
        @endif
    </div>

    <div class="footer">
        <div>Merci pour votre visite!</div>
        <div>Conservez ce reçu</div>
        <div style="margin-top: 10px;">
            {{ now()->format('d/m/Y H:i:s') }}
        </div>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
