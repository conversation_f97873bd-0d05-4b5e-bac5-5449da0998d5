<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-chart-line mr-2"></i>{{ __('Rapports et Analytics') }}
                </h2>
                @if($userStore)
                    <p class="text-sm text-gray-600 mt-1">
                        <i class="fas fa-store mr-1"></i>{{ $userStore->name }} ({{ $userStore->code }})
                    </p>
                @endif
            </div>
            @if($canViewAllStores)
                <div class="text-sm bg-blue-50 px-3 py-2 rounded-lg">
                    <span class="text-blue-700 font-medium">Mode Admin:</span>
                    <span class="text-blue-900">Accès à tous les magasins</span>
                </div>
            @endif
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

            <!-- Navigation des rapports -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-tachometer-alt mr-2"></i>Tableau de Bord des Rapports
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                        <!-- Rapports de Bénéfices -->
                        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 border border-green-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="bg-green-500 rounded-lg p-3">
                                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-lg font-semibold text-green-900">Analytics de Bénéfices</h4>
                                        <p class="text-sm text-green-700">Marges, rentabilité, tendances</p>
                                    </div>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <a href="{{ route('reports.profit-analytics') }}"
                                   class="block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center">
                                    <i class="fas fa-chart-line mr-2"></i>Voir les Analytics
                                </a>
                                <p class="text-xs text-green-600 text-center">
                                    Analyse détaillée des profits et marges
                                </p>
                            </div>
                        </div>

                        <!-- Rapports de Stock -->
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 border border-blue-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="bg-blue-500 rounded-lg p-3">
                                        <i class="fas fa-boxes text-white text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-lg font-semibold text-blue-900">Analytics de Stock</h4>
                                        <p class="text-sm text-blue-700">Rotation, valeur, alertes</p>
                                    </div>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <a href="{{ route('reports.stock-analytics') }}"
                                   class="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center">
                                    <i class="fas fa-chart-bar mr-2"></i>Voir les Analytics
                                </a>
                                <p class="text-xs text-blue-600 text-center">
                                    Analyse des stocks et rotations
                                </p>
                            </div>
                        </div>

                        <!-- Rapports Existants -->
                        <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="bg-purple-500 rounded-lg p-3">
                                        <i class="fas fa-file-alt text-white text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-lg font-semibold text-purple-900">Rapports Classiques</h4>
                                        <p class="text-sm text-purple-700">Ventes, crédits, stock</p>
                                    </div>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <a href="{{ route('reports.sales') }}"
                                   class="block w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center">
                                    <i class="fas fa-file-pdf mr-2"></i>Rapports PDF
                                </a>
                                <p class="text-xs text-purple-600 text-center">
                                    Rapports traditionnels exportables
                                </p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Aperçu rapide des métriques -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-eye mr-2"></i>Aperçu Rapide - Aujourd'hui
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" id="quick-metrics">
                        <!-- Les métriques seront chargées via AJAX -->
                        <div class="text-center py-8">
                            <i class="fas fa-spinner fa-spin text-gray-400 text-2xl"></i>
                            <p class="text-gray-500 mt-2">Chargement des métriques...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphiques de tendances -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

                <!-- Tendance des ventes (7 derniers jours) -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-chart-line mr-2"></i>Tendance des Ventes (7 jours)
                        </h3>
                        <div class="h-64">
                            <canvas id="salesTrendChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Répartition des bénéfices par catégorie -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-chart-pie mr-2"></i>Bénéfices par Catégorie
                        </h3>
                        <div class="h-64">
                            <canvas id="profitCategoryChart"></canvas>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Actions rapides -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-bolt mr-2"></i>Actions Rapides
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">

                        <a href="{{ route('reports.profit-analytics') }}?period=monthly"
                           class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-calendar-alt text-gray-600 text-xl mr-3"></i>
                            <div>
                                <h4 class="font-medium text-gray-900">Rapport Mensuel</h4>
                                <p class="text-sm text-gray-600">Bénéfices du mois en cours</p>
                            </div>
                        </a>

                        <a href="{{ route('reports.stock-analytics') }}"
                           class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-xl mr-3"></i>
                            <div>
                                <h4 class="font-medium text-gray-900">Alertes Stock</h4>
                                <p class="text-sm text-gray-600">Produits en rupture/stock faible</p>
                            </div>
                        </a>

                        <a href="{{ route('reports.sales') }}"
                           class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-download text-green-600 text-xl mr-3"></i>
                            <div>
                                <h4 class="font-medium text-gray-900">Export Rapports</h4>
                                <p class="text-sm text-gray-600">Télécharger en Excel/PDF</p>
                            </div>
                        </a>

                    </div>
                </div>
            </div>

        </div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadQuickMetrics();
            loadSalesTrendChart();
            loadProfitCategoryChart();
        });

        async function loadQuickMetrics() {
            try {
                const response = await fetch('/api/reports/quick-metrics');
                const data = await response.json();

                document.getElementById('quick-metrics').innerHTML = `
                    <div class="bg-green-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-600">${data.todaySales.toLocaleString()} FCFA</div>
                        <div class="text-sm text-green-700">Ventes Aujourd'hui</div>
                    </div>
                    <div class="bg-blue-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600">${data.todayProfit.toLocaleString()} FCFA</div>
                        <div class="text-sm text-blue-700">Bénéfices Aujourd'hui</div>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-purple-600">${data.todayMargin.toFixed(1)}%</div>
                        <div class="text-sm text-purple-700">Marge Moyenne</div>
                    </div>
                    <div class="bg-orange-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-orange-600">${data.todayTransactions}</div>
                        <div class="text-sm text-orange-700">Transactions</div>
                    </div>
                `;
            } catch (error) {
                console.error('Erreur lors du chargement des métriques:', error);
                document.getElementById('quick-metrics').innerHTML = `
                    <div class="col-span-4 text-center py-8 text-red-500">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Erreur lors du chargement des métriques</p>
                    </div>
                `;
            }
        }

        async function loadSalesTrendChart() {
            try {
                const response = await fetch('/api/reports/sales-trend');
                const data = await response.json();

                const ctx = document.getElementById('salesTrendChart').getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            label: 'Ventes',
                            data: data.sales,
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4
                        }, {
                            label: 'Bénéfices',
                            data: data.profits,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Erreur lors du chargement du graphique des ventes:', error);
            }
        }

        async function loadProfitCategoryChart() {
            try {
                const response = await fetch('/api/reports/profit-by-category');
                const data = await response.json();

                const ctx = document.getElementById('profitCategoryChart').getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.labels,
                        datasets: [{
                            data: data.profits,
                            backgroundColor: [
                                '#ef4444', '#f97316', '#eab308', '#22c55e',
                                '#3b82f6', '#8b5cf6', '#ec4899', '#6b7280'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Erreur lors du chargement du graphique des catégories:', error);
            }
        }
    </script>
    @endpush
</x-app-layout>
