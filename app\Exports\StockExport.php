<?php

namespace App\Exports;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StockExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filter;

    public function __construct($filter = null)
    {
        $this->filter = $filter;
    }

    public function collection()
    {
        $query = Product::query();

        switch ($this->filter) {
            case 'low_stock':
                $query->lowStock();
                break;
            case 'out_of_stock':
                $query->where('stock_quantity', 0);
                break;
        }

        return $query->get();
    }

    public function headings(): array
    {
        return [
            'Nom du produit',
            'Code-barres',
            'Prix de vente (FCFA)',
            'Prix d\'achat (FCFA)',
            'Marge (%)',
            'Stock actuel',
            'Seuil d\'alerte',
            'Unité',
            'Valeur stock (FCFA)',
            'TVA (%)',
            'Statut stock',
            'Statut produit',
            'Date création'
        ];
    }

    public function map($product): array
    {
        $margin = 0;
        if ($product->cost_price > 0) {
            $margin = (($product->price - $product->cost_price) / $product->cost_price) * 100;
        }

        $stockValue = $product->stock_quantity * ($product->cost_price ?? 0);

        $stockStatus = 'Normal';
        if ($product->stock_quantity <= 0) {
            $stockStatus = 'Rupture';
        } elseif ($product->isLowStock()) {
            $stockStatus = 'Stock faible';
        }

        return [
            $product->name,
            $product->barcode ?? 'Non défini',
            $product->price,
            $product->cost_price ?? 0,
            round($margin, 2),
            $product->stock_quantity,
            $product->min_stock_level,
            $product->unit,
            $stockValue,
            $product->tax_rate,
            $stockStatus,
            $product->is_active ? 'Actif' : 'Inactif',
            $product->created_at->format('d/m/Y')
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style pour l'en-tête
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '059669'],
                ],
            ],
        ];
    }
}
