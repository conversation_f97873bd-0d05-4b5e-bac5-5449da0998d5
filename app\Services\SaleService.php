<?php

namespace App\Services;

use App\Models\Sale;
use App\Models\Store;
use App\Models\Product;
use App\Models\Customer;
use App\Models\StoreStock;
use App\Exports\SalesExport;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class SaleService
{
    public function createSale(array $data, int $storeId, int $userId)
    {
        // Vérifier le stock disponible
        $store = Store::findOrFail($storeId);
        foreach ($data['items'] as $item) {
            $stock = StoreStock::where('store_id', $storeId)
                ->where('product_id', $item['product_id'])
                ->first();

            if (!$stock || $stock->quantity < $item['quantity']) {
                throw new \Exception("Stock insuffisant pour le produit ID: {$item['product_id']}");
            }
        }

        // Créer la vente
        $sale = Sale::create([
            'sale_number' => 'SALE-' . Str::random(8),
            'store_id' => $storeId,
            'user_id' => $userId,
            'customer_id' => $data['customer_id'],
            'payment_method' => $data['payment_method'],
            'status' => 'pending',
            'notes' => $data['notes'] ?? null
        ]);

        $subtotal = 0;

        // Ajouter les articles et mettre à jour le stock
        foreach ($data['items'] as $item) {
            $product = Product::find($item['product_id']);
            $price = $product->getCurrentPriceInStore($storeId);

            $saleItem = $sale->items()->create([
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity'],
                'unit_price' => $price,
                'subtotal' => $price * $item['quantity']
            ]);

            $subtotal += $saleItem->subtotal;

            // Mettre à jour le stock
            StoreStock::where('store_id', $storeId)
                ->where('product_id', $item['product_id'])
                ->decrement('quantity', $item['quantity']);
        }

        // Calculer les totaux
        $sale->subtotal = $subtotal;
        $sale->discount_amount = $data['discount_amount'] ?? 0;
        $sale->tax_amount = $subtotal * ($store->default_tax_rate / 100);
        $sale->total_amount = $sale->subtotal + $sale->tax_amount - $sale->discount_amount;

        // Traiter le paiement
        if (isset($data['amount_paid'])) {
            $sale->amount_paid = $data['amount_paid'];
            $sale->change_amount = max(0, $data['amount_paid'] - $sale->total_amount);

            // Créer un crédit si nécessaire
            if ($data['payment_method'] === 'credit' || $sale->amount_paid < $sale->total_amount) {
                $creditAmount = $sale->total_amount - $sale->amount_paid;
                
                $customer = Customer::find($data['customer_id']);
                if ($customer->canReceiveCredit($creditAmount)) {
                    $customer->addCredit($creditAmount, $sale->id);
                } else {
                    throw new \Exception("Le client a dépassé sa limite de crédit");
                }
            }

            $sale->status = $sale->amount_paid >= $sale->total_amount ? 'completed' : 'partial';
        }

        $sale->save();

        return $sale;
    }

    public function updateSale(Sale $sale, array $data)
    {
        if ($sale->status === 'cancelled') {
            throw new \Exception("Impossible de modifier une vente annulée");
        }

        $sale->update([
            'notes' => $data['notes'] ?? $sale->notes,
            'status' => $data['status'] ?? $sale->status
        ]);

        return $sale;
    }

    public function cancelSale(Sale $sale)
    {
        if ($sale->status === 'cancelled') {
            throw new \Exception("Cette vente est déjà annulée");
        }

        if ($sale->credit && $sale->credit->amount_paid > 0) {
            throw new \Exception("Impossible d'annuler une vente avec des paiements de crédit");
        }

        // Restaurer le stock
        foreach ($sale->items as $item) {
            StoreStock::where('store_id', $sale->store_id)
                ->where('product_id', $item->product_id)
                ->increment('quantity', $item->quantity);
        }

        // Annuler le crédit associé si existe
        if ($sale->credit) {
            $sale->credit->delete();
        }

        $sale->update(['status' => 'cancelled']);

        return $sale;
    }

    public function generateDailyReport(Store $store, string $date)
    {
        $sales = Sale::where('store_id', $store->id)
            ->whereDate('created_at', $date)
            ->with('items.product')
            ->get();

        return [
            'total_sales' => $sales->count(),
            'total_revenue' => $sales->sum('total_amount'),
            'total_tax' => $sales->sum('tax_amount'),
            'total_discount' => $sales->sum('discount_amount'),
            'payment_methods' => $sales->groupBy('payment_method')
                ->map(fn($group) => [
                    'count' => $group->count(),
                    'amount' => $group->sum('total_amount')
                ]),
            'products_sold' => $sales->flatMap->items
                ->groupBy('product_id')
                ->map(fn($group) => [
                    'name' => $group->first()->product->name,
                    'quantity' => $group->sum('quantity'),
                    'revenue' => $group->sum('subtotal')
                ])
                ->sortByDesc('revenue')
                ->values(),
            'hourly_sales' => $sales->groupBy(function($sale) {
                return Carbon::parse($sale->created_at)->format('H:00');
            })
        ];
    }

    public function exportSales(int $storeId, array $filters)
    {
        return (new SalesExport($storeId, $filters))->download(
            'ventes_' . Carbon::now()->format('Y-m-d') . '.' . ($filters['format'] ?? 'xlsx')
        );
    }
}