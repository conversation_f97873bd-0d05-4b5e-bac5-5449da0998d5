<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-tachometer-alt mr-3 text-blue-600"></i><?php echo e(__('dashboard.title')); ?>

                    <?php if(isset($store)): ?>
                        <span class="text-lg text-blue-600 font-medium">- <?php echo e($store->name); ?></span>
                    <?php endif; ?>
                </h2>
                <p class="text-sm text-gray-600 mt-1">
                    <?php echo e(__('dashboard.subtitle')); ?>

                    <?php if(isset($store)): ?>
                        <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                            <i class="fas fa-store mr-1"></i><?php echo e($store->code); ?>

                        </span>
                    <?php endif; ?>
                </p>
            </div>
            <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                <div class="flex items-center text-sm text-gray-500 bg-gray-100 px-3 py-2 rounded-lg" data-time>
                    <i class="fas fa-calendar mr-2"></i><?php echo e(now()->format('d/m/Y H:i')); ?>

                </div>
                <select id="period-selector"
                    class="border-gray-300 rounded-lg text-sm focus:ring-blue-500 focus:border-blue-500">
                    <option value="today"><?php echo e(__('dashboard.periods.today')); ?></option>
                    <option value="month" selected><?php echo e(__('dashboard.periods.month')); ?></option>
                    <option value="year"><?php echo e(__('dashboard.periods.year')); ?></option>
                </select>
                <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['variant' => 'success','href' => ''.e(route('pos.index')).'','icon' => 'fas fa-cash-register']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'success','href' => ''.e(route('pos.index')).'','icon' => 'fas fa-cash-register']); ?>
                    <?php echo e(__('dashboard.new_sale')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-4 sm:py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Alertes -->
            <div id="dashboard-alerts" class="mb-6"></div>

            <!-- Contenu du tableau de bord chargé en AJAX -->
            <div id="dashboard-content" data-stats-url="<?php echo e(route('dashboard.stats')); ?>"
                data-charts-url="<?php echo e(route('dashboard.getChartData', ['type' => 'sales'])); ?>">
                <div class="animate-pulse">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
                        <?php for($i = 0; $i < 3; $i++): ?>
                            <div class="bg-white p-6 rounded-lg shadow-sm">
                                <div class="h-4 bg-gray-200 rounded-full w-1/3 mb-4"></div>
                                <div class="h-8 bg-gray-300 rounded-full w-2/3"></div>
                            </div>
                        <?php endfor; ?>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                        <?php for($i = 0; $i < 2; $i++): ?>
                            <div class="bg-white p-6 rounded-lg shadow-sm">
                                <div class="h-4 bg-gray-200 rounded-full w-1/4 mb-6"></div>
                                <div class="space-y-3">
                                    <?php for($j = 0; $j < 4; $j++): ?>
                                        <div class="h-4 bg-gray-100 rounded-full w-full"></div>
                                    <?php endfor; ?>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>

            <!-- Graphiques -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mt-6 sm:mt-8">
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-chart-line text-blue-500 mr-2"></i><?php echo e(__('dashboard.charts.sales')); ?>

                    </h3>
                    <select class="chart-period-selector border-gray-300 rounded-lg text-sm mb-4" data-chart="sales">
                        <option value="7days" selected><?php echo e(__('dashboard.periods.7days')); ?></option>
                        <option value="30days"><?php echo e(__('dashboard.periods.30days')); ?></option>
                        <option value="90days"><?php echo e(__('dashboard.periods.90days')); ?></option>
                    </select>
                    <canvas id="sales-chart"></canvas>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i
                            class="fas fa-credit-card text-yellow-500 mr-2"></i><?php echo e(__('dashboard.charts.payment_methods')); ?>

                    </h3>
                    <canvas id="payment-methods-chart"></canvas>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-crown text-yellow-500 mr-2"></i><?php echo e(__('dashboard.charts.top_products')); ?>

                    </h3>
                    <canvas id="top-products-chart"></canvas>
                </div>
                
            </div>

            <!-- Performances des employés -->
            <div class="bg-white p-6 rounded-lg shadow-sm mt-6 sm:mt-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-users text-green-500 mr-2"></i><?php echo e(__('dashboard.employee_performance')); ?>

                </h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-500">
                                    <?php echo e(__('dashboard.employee_name')); ?></th>
                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-500">
                                    <?php echo e(__('dashboard.sales_count')); ?></th>
                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-500">
                                    <?php echo e(__('dashboard.total_sales')); ?></th>
                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-500">
                                    Bénéfices</th>
                            </tr>
                        </thead>
                        <tbody id="employee-stats" class="divide-y divide-gray-200"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.4/dist/chart.umd.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const dashboardContent = document.getElementById('dashboard-content');
                const alertsContainer = document.getElementById('dashboard-alerts');
                const statsUrl = dashboardContent.dataset.statsUrl;
                const chartsUrl = dashboardContent.dataset.chartsUrl;
                const periodSelector = document.getElementById('period-selector');
                let salesChart, paymentMethodsChart, topProductsChart;

                const translations = {
                    kpis: {
                        sales: <?php echo json_encode(__('dashboard.kpis.sales') ?? 'Ventes', 15, 512) ?>,
                        due_amount: <?php echo json_encode(__('dashboard.kpis.due_amount') ?? 'Crédits en cours', 15, 512) ?>,
                        low_stock: <?php echo json_encode(__('dashboard.kpis.low_stock') ?? 'Stock faible', 15, 512) ?>,
                        today_sales: <?php echo json_encode(__('dashboard.kpis.today_sales') ?? 'Ventes aujourd\'hui', 15, 512) ?>,
                        today_credits: <?php echo json_encode(__('dashboard.kpis.today_credits') ?? 'Crédits aujourd\'hui', 15, 512) ?>,
                        average_order_value: <?php echo json_encode(__('dashboard.kpis.average_order_value') ?? 'Valeur moyenne des commandes', 15, 512) ?>,
                        orders_completed: <?php echo json_encode(__('dashboard.kpis.orders_completed') ?? '{1} commande complétée|[2, *] commandes complétées', 512) ?>,
                        active_credits: <?php echo json_encode(__('dashboard.kpis.active_credits') ?? '{1} crédit actif|[2, *] crédits actifs', 512) ?>,
                        total_products: <?php echo json_encode(__('dashboard.kpis.total_products') ?? 'sur :count produit(s) au total', 15, 512) ?>,
                        today_sales_count: <?php echo json_encode(__('dashboard.kpis.today_sales_count') ?? '{1} vente aujourd\'hui|[2, *] ventes aujourd\'hui', 512) ?>,
                        today_credits_count: <?php echo json_encode(__('dashboard.kpis.today_credits_count') ?? '{1} crédit aujourd\'hui|[2, *] crédits aujourd\'hui', 512) ?>,
                        average_order_value_desc: <?php echo json_encode(__('dashboard.kpis.average_order_value_desc') ?? 'Moyenne des commandes', 15, 512) ?>
                    },
                    charts: {
                        sales_amount: <?php echo json_encode(__('dashboard.charts.sales_amount') ?? 'Montant des ventes', 15, 512) ?>,
                        sales_count: <?php echo json_encode(__('dashboard.charts.sales_count') ?? 'Nombre de ventes', 15, 512) ?>,
                        sales_title: <?php echo json_encode(__('dashboard.charts.sales_title') ?? 'Ventes par période', 15, 512) ?>,
                        payment_methods: <?php echo json_encode(__('dashboard.charts.payment_methods') ?? 'Modes de paiement', 15, 512) ?>,
                        payment_methods_title: <?php echo json_encode(__('dashboard.charts.payment_methods_title') ?? 'Répartition des ventes par mode de paiement', 15, 512) ?>,
                        top_products_quantity: <?php echo json_encode(__('dashboard.charts.top_products_quantity') ?? 'Quantité vendue', 15, 512) ?>,
                        top_products_title: <?php echo json_encode(__('dashboard.charts.top_products_title') ?? 'Top 5 des produits les plus vendus', 15, 512) ?>
                    },
                    tables: {
                        best_sellers: <?php echo json_encode(__('dashboard.tables.best_sellers') ?? 'Meilleures ventes', 15, 512) ?>,
                        recent_sales: <?php echo json_encode(__('dashboard.tables.recent_sales') ?? 'Ventes récentes', 15, 512) ?>,
                        product: <?php echo json_encode(__('dashboard.tables.product') ?? 'Produit', 15, 512) ?>,
                        quantity: <?php echo json_encode(__('dashboard.tables.quantity') ?? 'Quantité', 15, 512) ?>,
                        total: <?php echo json_encode(__('dashboard.tables.total') ?? 'Total', 15, 512) ?>,
                        customer: <?php echo json_encode(__('dashboard.tables.customer') ?? 'Client', 15, 512) ?>,
                        date: <?php echo json_encode(__('dashboard.tables.date') ?? 'Date', 15, 512) ?>
                    },
                    errors: {
                        loading: <?php echo json_encode(__('dashboard.errors.loading') ?? 'Erreur de chargement', 15, 512) ?>,
                        loading_alerts: <?php echo json_encode(__('dashboard.errors.loading_alerts') ?? 'Erreur de chargement des alertes', 15, 512) ?>,
                        loading_chart: <?php echo json_encode(__('dashboard.errors.loading_chart') ?? 'Erreur de chargement du graphique', 15, 512) ?>,
                        loading_employee_stats: <?php echo json_encode(__('dashboard.errors.loading_employee_stats') ?? 'Erreur de chargement des performances des employés', 15, 512) ?>,
                        retry: <?php echo json_encode(__('dashboard.errors.retry') ?? 'Réessayer', 15, 512) ?>
                    }
                };
                console.log('Translations:', translations); // Log pour déboguer

                async function loadDashboardStats(period = 'month') {
                    try {
                        console.log('Chargement des stats pour la période:', period);
                        const response = await fetch(`${statsUrl}?period=${period}`);
                        console.log('Réponse stats:', response);
                        if (!response.ok) {
                            throw new Error('Une erreur est survenue lors du chargement des statistiques');
                        }

                        const data = await response.json();
                        console.log('Données stats:', data);
                        if (!data.success) {
                            throw new Error(data.error || 'Une erreur est survenue');
                        }

                        const stats = data.stats;
                        const averageOrderValue = isNaN(stats.averageOrderValue) || stats.averageOrderValue ===
                            null ? 0 : stats.averageOrderValue;
                        let content = `
                        <!-- KPIs -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
                            <!-- Ventes -->
                            <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">${translations.kpis.sales}</p>
                                        <p class="mt-1 text-3xl font-bold text-gray-900">${new Intl.NumberFormat('fr-FR').format(stats.salesValue)} FCFA</p>
                                    </div>
                                    <div class="p-3 bg-green-100 rounded-full">
                                        <i class="fas fa-chart-line text-xl text-green-600"></i>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">${stats.completedOrders} ${translations.kpis.orders_completed}</p>
                            </div>

                            <!-- Crédits en cours -->
                            <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">${translations.kpis.due_amount}</p>
                                        <p class="mt-1 text-3xl font-bold text-gray-900">${new Intl.NumberFormat('fr-FR').format(stats.dueAmount)} FCFA</p>
                                    </div>
                                    <div class="p-3 bg-yellow-100 rounded-full">
                                        <i class="fas fa-clock text-xl text-yellow-600"></i>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">${stats.pendingOrders} ${translations.kpis.active_credits}</p>
                            </div>

                            <!-- Stock -->
                            <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">${translations.kpis.low_stock}</p>
                                        <p class="mt-1 text-3xl font-bold text-gray-900">${stats.lowStockCount}</p>
                                    </div>
                                    <div class="p-3 bg-blue-100 rounded-full">
                                        <i class="fas fa-box text-xl text-blue-600"></i>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">${translations.kpis.total_products.replace(':count', stats.totalProducts)}</p>
                            </div>

                            <!-- Ventes du jour -->
                            <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">${translations.kpis.today_sales}</p>
                                        <p class="mt-1 text-3xl font-bold text-gray-900">${new Intl.NumberFormat('fr-FR').format(stats.todaySales)} FCFA</p>
                                    </div>
                                    <div class="p-3 bg-green-100 rounded-full">
                                        <i class="fas fa-cash-register text-xl text-green-600"></i>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">${stats.todaySalesCount} ${translations.kpis.today_sales_count}</p>
                            </div>

                            <!-- Crédits du jour -->
                            <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">${translations.kpis.today_credits}</p>
                                        <p class="mt-1 text-3xl font-bold text-gray-900">${new Intl.NumberFormat('fr-FR').format(stats.todayCredits)} FCFA</p>
                                    </div>
                                    <div class="p-3 bg-yellow-100 rounded-full">
                                        <i class="fas fa-credit-card text-xl text-yellow-600"></i>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">${stats.todayCreditsCount} ${translations.kpis.today_credits_count}</p>
                            </div>

                            <!-- Valeur moyenne des commandes -->
                            <div class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">${translations.kpis.average_order_value}</p>
                                        <p class="mt-1 text-3xl font-bold text-gray-900">${new Intl.NumberFormat('fr-FR').format(averageOrderValue)} FCFA</p>
                                    </div>
                                    <div class="p-3 bg-blue-100 rounded-full">
                                        <i class="fas fa-calculator text-xl text-blue-600"></i>
                                    </div>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">${translations.kpis.average_order_value_desc}</p>
                            </div>
                        </div>

                        <!-- Tables des meilleures ventes et ventes récentes -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                            <!-- Meilleures ventes -->
                            <div class="bg-white p-6 rounded-lg shadow-sm">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                    <i class="fas fa-crown text-yellow-500 mr-2"></i>${translations.tables.best_sellers}
                                </h3>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead>
                                            <tr>
                                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-500">${translations.tables.product}</th>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-500">${translations.tables.quantity}</th>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-500">${translations.tables.total}</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                            ${stats.bestSellers.map(item => `
                                                                <tr>
                                                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${item.name}</td>
                                                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-600">${item.total_quantity}</td>
                                                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right font-medium text-gray-900">
                                                                        ${new Intl.NumberFormat('fr-FR').format(item.total_amount)} FCFA
                                                                    </td>
                                                                </tr>
                                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Ventes récentes -->
                            <div class="bg-white p-6 rounded-lg shadow-sm">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                                    <i class="fas fa-clock text-blue-500 mr-2"></i>${translations.tables.recent_sales}
                                </h3>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead>
                                            <tr>
                                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-500">${translations.tables.customer}</th>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-500">${translations.tables.total}</th>
                                                <th class="px-4 py-2 text-right text-sm font-medium text-gray-500">${translations.tables.date}</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                            ${stats.recentSales.map(sale => `
                                                                <tr>
                                                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${sale.customer?.name || 'Client occasionnel'}</td>
                                                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right font-medium text-gray-900">
                                                                        ${new Intl.NumberFormat('fr-FR').format(sale.total_amount)} FCFA
                                                                    </td>
                                                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-600">
                                                                        ${new Date(sale.created_at).toLocaleDateString('fr-FR')}
                                                                    </td>
                                                                </tr>
                                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    `;

                        dashboardContent.innerHTML = content;
                    } catch (error) {
                        console.error('Erreur lors du chargement des statistiques:', error);
                        dashboardContent.innerHTML = `
                        <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle text-red-600 mr-3"></i>
                                <div>
                                    <h4 class="font-medium text-red-800">${translations.errors.loading}</h4>
                                    <p class="text-sm text-red-700">${error.message}</p>
                                    <button onclick="loadDashboardStats('${period}')" class="mt-2 text-sm font-medium text-red-700 hover:text-red-800 focus:outline-none">
                                        <i class="fas fa-sync-alt mr-1"></i>${translations.errors.retry}
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    }
                }

                async function loadAlerts() {
                    try {
                        console.log('Chargement des alertes...');
                        const response = await fetch('<?php echo e(route('dashboard.getAlerts')); ?>');
                        console.log('Réponse alerts:', response);
                        if (!response.ok) {
                            throw new Error('Une erreur est survenue lors du chargement des alertes');
                        }

                        const data = await response.json();
                        console.log('Données alerts:', data);
                        if (!data.alerts) {
                            throw new Error('Données d\'alertes manquantes');
                        }

                        alertsContainer.innerHTML = data.alerts.map(alert => `
                        <div class="bg-${alert.type === 'danger' ? 'red' : alert.type === 'warning' ? 'yellow' : 'blue'}-50 border-l-4 border-${alert.type === 'danger' ? 'red' : alert.type === 'warning' ? 'yellow' : 'blue'}-400 p-4 rounded-lg mb-4">
                            <div class="flex items-center">
                                <i class="${alert.icon} text-${alert.type === 'danger' ? 'red' : alert.type === 'warning' ? 'yellow' : 'blue'}-600 mr-3"></i>
                                <div>
                                    <h4 class="font-medium text-${alert.type === 'danger' ? 'red' : alert.type === 'warning' ? 'yellow' : 'blue'}-800">${alert.title}</h4>
                                    <p class="text-sm text-${alert.type === 'danger' ? 'red' : alert.type === 'warning' ? 'yellow' : 'blue'}-700">${alert.message}</p>
                                    <a href="${alert.action}" class="mt-2 text-sm font-medium text-${alert.type === 'danger' ? 'red' : alert.type === 'warning' ? 'yellow' : 'blue'}-700 hover:text-${alert.type === 'danger' ? 'red' : alert.type === 'warning' ? 'yellow' : 'blue'}-800">
                                        ${alert.action_text}
                                    </a>
                                </div>
                            </div>
                        </div>
                    `).join('');
                    } catch (error) {
                        console.error('Erreur lors du chargement des alertes:', error);
                        alertsContainer.innerHTML = `
                        <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle text-red-600 mr-3"></i>
                                <div>
                                    <h4 class="font-medium text-red-800">${translations.errors.loading_alerts}</h4>
                                    <p class="text-sm text-red-700">${error.message}</p>
                                    <button onclick="loadAlerts()" class="mt-2 text-sm font-medium text-red-700 hover:text-red-800 focus:outline-none">
                                        <i class="fas fa-sync-alt mr-1"></i>${translations.errors.retry}
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    }
                }

                async function loadEmployeeStats() {
                    try {
                        console.log('Chargement des performances des employés...');
                        const response = await fetch('<?php echo e(route('dashboard.getEmployeeStats')); ?>');
                        console.log('Réponse employee-stats:', response);
                        if (!response.ok) {
                            throw new Error(
                                'Une erreur est survenue lors du chargement des performances des employés');
                        }

                        const data = await response.json();
                        console.log('Données employee-stats:', data);
                        document.getElementById('employee-stats').innerHTML = data.map(item => `
                        <tr>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${item.name}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-600">${item.sales_count}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-right font-medium text-gray-900">
                                ${new Intl.NumberFormat('fr-FR').format(item.total_sales)} FCFA
                            </td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-right font-medium text-green-600">
                                ${new Intl.NumberFormat('fr-FR').format(item.total_profit || 0)} FCFA
                            </td>
                        </tr>
                    `).join('');
                    } catch (error) {
                        console.error('Erreur lors du chargement des performances des employés:', error);
                        document.getElementById('employee-stats').parentElement.innerHTML += `
                        <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg mt-4">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle text-red-600 mr-3"></i>
                                <div>
                                    <h4 class="font-medium text-red-800">${translations.errors.loading_employee_stats}</h4>
                                    <p class="text-sm text-red-700">${error.message}</p>
                                    <button onclick="loadEmployeeStats()" class="mt-2 text-sm font-medium text-red-700 hover:text-red-800 focus:outline-none">
                                        <i class="fas fa-sync-alt mr-1"></i>${translations.errors.retry}
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    }
                }

                async function loadChart(chartType, period = '7days') {
                    try {
                        console.log(`Chargement du graphique ${chartType} pour la période: ${period}`);
                        const response = await fetch(
                            `<?php echo e(route('dashboard.getChartData', ['type' => '__CHART_TYPE__'])); ?>`.replace(
                                '__CHART_TYPE__', chartType) + `?period=${period}`);
                        console.log(`Réponse ${chartType}:`, response);
                        if (!response.ok) {
                            throw new Error(`Une erreur est survenue lors du chargement du graphique ${chartType}`);
                        }

                        const data = await response.json();
                        console.log(`Données ${chartType}:`, data);

                        // Destruction des graphiques existants pour éviter les conflits
                        if (chartType === 'sales' && window.salesChart) {
                            window.salesChart.destroy();
                        } else if (chartType === 'payment-methods' && window.paymentMethodsChart) {
                            window.paymentMethodsChart.destroy();
                        } else if (chartType === 'top-products' && window.topProductsChart) {
                            window.topProductsChart.destroy();
                        }

                        // Rendu du graphique
                        if (chartType === 'sales') {
                            const ctx = document.getElementById('sales-chart').getContext('2d');
                            window.salesChart = new Chart(ctx, {
                                type: 'line',
                                data: {
                                    labels: data.map(item => item.date),
                                    datasets: [{
                                        label: translations.charts.sales_amount,
                                        data: data.map(item => item.amount),
                                        borderColor: '#3B82F6',
                                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                                        fill: true,
                                        tension: 0.4
                                    }, {
                                        label: translations.charts.sales_count,
                                        data: data.map(item => item.count),
                                        borderColor: '#10B981',
                                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                                        fill: true,
                                        tension: 0.4
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    plugins: {
                                        legend: {
                                            position: 'top'
                                        },
                                        title: {
                                            display: true,
                                            text: translations.charts.sales_title
                                        }
                                    },
                                    scales: {
                                        y: {
                                            beginAtZero: true
                                        }
                                    }
                                }
                            });
                        } else if (chartType === 'payment-methods') {
                            const ctx = document.getElementById('payment-methods-chart').getContext('2d');
                            window.paymentMethodsChart = new Chart(ctx, {
                                type: 'pie',
                                data: {
                                    labels: data.map(item => item.method),
                                    datasets: [{
                                        data: data.map(item => item.total),
                                        backgroundColor: data.map(item => item.color),
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    plugins: {
                                        legend: {
                                            position: 'top'
                                        },
                                        title: {
                                            display: true,
                                            text: translations.charts.payment_methods_title
                                        }
                                    }
                                }
                            });
                        } else if (chartType === 'top-products') {
                            const ctx = document.getElementById('top-products-chart').getContext('2d');
                            window.topProductsChart = new Chart(ctx, {
                                type: 'bar',
                                data: {
                                    labels: data.map(item => item.name),
                                    datasets: [{
                                        label: translations.charts.top_products_quantity,
                                        data: data.map(item => item.total_quantity),
                                        backgroundColor: '#3B82F6',
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    plugins: {
                                        legend: {
                                            position: 'top'
                                        },
                                        title: {
                                            display: true,
                                            text: translations.charts.top_products_title
                                        }
                                    },
                                    scales: {
                                        y: {
                                            beginAtZero: true
                                        }
                                    }
                                }
                            });
                        }
                    } catch (error) {
                        console.error(`Erreur lors du chargement du graphique ${chartType}:`, error);
                        const chartContainer = document.getElementById(`${chartType}-chart`).parentElement;
                        chartContainer.innerHTML += `
            <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg mt-4">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle text-red-600 mr-3"></i>
                    <div>
                        <h4 class="font-medium text-red-800">${translations.errors.loading_chart}</h4>
                        <p class="text-sm text-red-700">${error.message}</p>
                        <button onclick="loadChart('${chartType}', '${period}')" class="mt-2 text-sm font-medium text-red-700 hover:text-red-800 focus:outline-none">
                            <i class="fas fa-sync-alt mr-1"></i>${translations.errors.retry}
                        </button>
                    </div>
                </div>
            </div>
        `;
                    }
                }

                // Écouteur pour le sélecteur de période
                periodSelector.addEventListener('change', () => {
                    loadDashboardStats(periodSelector.value);
                });

                // Écouteurs pour les sélecteurs de période des graphiques
                document.querySelectorAll('.chart-period-selector').forEach(selector => {
                    selector.addEventListener('change', () => {
                        const chartType = selector.dataset.chart;
                        loadChart(chartType, selector.value);
                    });
                });

                // Chargement initial
                loadDashboardStats(periodSelector.value);
                loadAlerts();
                loadEmployeeStats();
                loadChart('sales', '7days');
                loadChart('payment-methods');
                loadChart('top-products');

                // Actualisation toutes les 15 minutes
                setInterval(() => {
                    loadDashboardStats(periodSelector.value);
                    loadAlerts();
                    loadEmployeeStats();
                    document.querySelectorAll('.chart-period-selector').forEach(selector => {
                        loadChart(selector.dataset.chart, selector.value);
                    });
                }, 15 * 60 * 1000);

                // Mise à jour de l'heure en temps réel
                const timeElement = document.querySelector('[data-time]');
                if (timeElement) {
                    setInterval(() => {
                        const now = new Date();
                        timeElement.innerHTML = `<i class="fas fa-calendar mr-2"></i>${now.toLocaleString('fr-FR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    })}`;
                    }, 60000); // Mise à jour toutes les minutes
                }
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\UNDP\pos\pos\resources\views/dashboard.blade.php ENDPATH**/ ?>