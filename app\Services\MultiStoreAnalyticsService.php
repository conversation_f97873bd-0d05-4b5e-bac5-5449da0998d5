<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\Store;
use App\Models\Sale;
use App\Models\Product;
use App\Models\StoreStock;
use Carbon\Carbon;

class MultiStoreAnalyticsService
{
    const CACHE_TTL = 3600; // 1 heure

    /**
     * Tableau de bord comparatif des magasins
     */
    public function getStoreComparisonDashboard($period = 'month')
    {
        $cacheKey = "store_comparison_dashboard_{$period}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($period) {
            $dateRange = $this->getDateRange($period);
            
            return [
                'performance_comparison' => $this->getPerformanceComparison($dateRange),
                'revenue_trends' => $this->getRevenueTrends($dateRange),
                'profit_analysis' => $this->getProfitAnalysis($dateRange),
                'stock_efficiency' => $this->getStockEfficiency(),
                'customer_analytics' => $this->getCustomerAnalytics($dateRange),
                'product_performance' => $this->getProductPerformance($dateRange),
                'operational_metrics' => $this->getOperationalMetrics($dateRange)
            ];
        });
    }

    /**
     * Comparaison de performance entre magasins
     */
    public function getPerformanceComparison($dateRange)
    {
        return DB::select("
            SELECT 
                s.id,
                s.name,
                s.city,
                COUNT(DISTINCT sa.id) as sales_count,
                COALESCE(SUM(sa.total_amount), 0) as total_revenue,
                COALESCE(AVG(sa.total_amount), 0) as avg_sale_value,
                COALESCE(SUM(si.profit_amount), 0) as total_profit,
                CASE 
                    WHEN SUM(si.cost_price * si.quantity) > 0 
                    THEN (SUM(si.profit_amount) / SUM(si.cost_price * si.quantity)) * 100 
                    ELSE 0 
                END as profit_margin,
                COUNT(DISTINCT sa.customer_id) as unique_customers,
                COUNT(DISTINCT si.product_id) as products_sold,
                COALESCE(SUM(sa.total_amount) / NULLIF(COUNT(DISTINCT sa.id), 0), 0) as revenue_per_sale,
                COALESCE(SUM(si.profit_amount) / NULLIF(COUNT(DISTINCT sa.id), 0), 0) as profit_per_sale
            FROM stores s
            LEFT JOIN sales sa ON s.id = sa.store_id 
                AND sa.created_at BETWEEN ? AND ?
                AND sa.status = 'completed'
            LEFT JOIN sale_items si ON sa.id = si.sale_id
            WHERE s.is_active = 1
            GROUP BY s.id, s.name, s.city
            ORDER BY total_revenue DESC
        ", [$dateRange[0], $dateRange[1]]);
    }

    /**
     * Tendances de revenus par magasin
     */
    public function getRevenueTrends($dateRange)
    {
        $stores = Store::active()->get();
        $trends = [];

        foreach ($stores as $store) {
            $dailyRevenue = DB::select("
                SELECT 
                    DATE(created_at) as date,
                    SUM(total_amount) as revenue,
                    COUNT(*) as sales_count
                FROM sales 
                WHERE store_id = ? 
                AND created_at BETWEEN ? AND ?
                AND status = 'completed'
                GROUP BY DATE(created_at)
                ORDER BY date
            ", [$store->id, $dateRange[0], $dateRange[1]]);

            $trends[$store->id] = [
                'store_name' => $store->name,
                'daily_data' => $dailyRevenue,
                'total_revenue' => array_sum(array_column($dailyRevenue, 'revenue')),
                'avg_daily_revenue' => count($dailyRevenue) > 0 ? array_sum(array_column($dailyRevenue, 'revenue')) / count($dailyRevenue) : 0
            ];
        }

        return $trends;
    }

    /**
     * Analyse des profits par magasin
     */
    public function getProfitAnalysis($dateRange)
    {
        return DB::select("
            SELECT 
                s.id,
                s.name,
                SUM(si.profit_amount) as total_profit,
                SUM(si.cost_price * si.quantity) as total_cost,
                SUM(si.total_price) as total_revenue,
                CASE 
                    WHEN SUM(si.total_price) > 0 
                    THEN (SUM(si.profit_amount) / SUM(si.total_price)) * 100 
                    ELSE 0 
                END as profit_margin_percentage,
                AVG(si.profit_amount) as avg_profit_per_item,
                COUNT(DISTINCT si.product_id) as profitable_products,
                SUM(CASE WHEN si.profit_amount > 0 THEN 1 ELSE 0 END) as profitable_sales_items,
                COUNT(si.id) as total_sales_items
            FROM stores s
            LEFT JOIN sales sa ON s.id = sa.store_id 
                AND sa.created_at BETWEEN ? AND ?
                AND sa.status = 'completed'
            LEFT JOIN sale_items si ON sa.id = si.sale_id
            WHERE s.is_active = 1
            GROUP BY s.id, s.name
            HAVING total_profit IS NOT NULL
            ORDER BY total_profit DESC
        ", [$dateRange[0], $dateRange[1]]);
    }

    /**
     * Efficacité des stocks par magasin
     */
    public function getStockEfficiency()
    {
        return DB::select("
            SELECT 
                s.id,
                s.name,
                COUNT(ss.id) as total_products,
                SUM(ss.quantity) as total_stock_quantity,
                SUM(ss.quantity * pp.cost_price) as total_stock_value,
                COUNT(CASE WHEN ss.quantity <= ss.min_stock_level THEN 1 END) as low_stock_items,
                COUNT(CASE WHEN ss.quantity <= 0 THEN 1 END) as out_of_stock_items,
                COUNT(CASE WHEN ss.quantity > ss.max_stock_level THEN 1 END) as overstock_items,
                AVG(ss.quantity) as avg_stock_per_product,
                (COUNT(CASE WHEN ss.quantity <= ss.min_stock_level THEN 1 END) * 100.0 / COUNT(ss.id)) as low_stock_percentage
            FROM stores s
            LEFT JOIN store_stocks ss ON s.id = ss.store_id AND ss.is_active = 1
            LEFT JOIN product_prices pp ON ss.product_id = pp.product_id 
                AND ss.store_id = pp.store_id 
                AND pp.is_active = 1
            WHERE s.is_active = 1
            GROUP BY s.id, s.name
            ORDER BY total_stock_value DESC
        ");
    }

    /**
     * Analytics des clients par magasin
     */
    public function getCustomerAnalytics($dateRange)
    {
        return DB::select("
            SELECT 
                s.id,
                s.name,
                COUNT(DISTINCT c.id) as total_customers,
                COUNT(DISTINCT sa.customer_id) as active_customers,
                AVG(sa.total_amount) as avg_customer_spend,
                SUM(sa.total_amount) / NULLIF(COUNT(DISTINCT sa.customer_id), 0) as revenue_per_customer,
                COUNT(sa.id) / NULLIF(COUNT(DISTINCT sa.customer_id), 0) as avg_visits_per_customer,
                COUNT(CASE WHEN c.type = 'vip' THEN 1 END) as vip_customers,
                COUNT(CASE WHEN c.type = 'wholesale' THEN 1 END) as wholesale_customers,
                SUM(CASE WHEN c.current_balance > 0 THEN c.current_balance ELSE 0 END) as total_credit_balance
            FROM stores s
            LEFT JOIN customers c ON s.id = c.store_id AND c.is_active = 1
            LEFT JOIN sales sa ON c.id = sa.customer_id 
                AND sa.created_at BETWEEN ? AND ?
                AND sa.status = 'completed'
            WHERE s.is_active = 1
            GROUP BY s.id, s.name
            ORDER BY total_customers DESC
        ", [$dateRange[0], $dateRange[1]]);
    }

    /**
     * Performance des produits par magasin
     */
    public function getProductPerformance($dateRange)
    {
        return DB::select("
            SELECT 
                s.id as store_id,
                s.name as store_name,
                p.id as product_id,
                p.name as product_name,
                SUM(si.quantity) as total_quantity_sold,
                SUM(si.total_price) as total_revenue,
                SUM(si.profit_amount) as total_profit,
                AVG(si.unit_price) as avg_selling_price,
                COUNT(DISTINCT sa.id) as sales_count,
                (SUM(si.profit_amount) / SUM(si.total_price)) * 100 as profit_margin,
                ROW_NUMBER() OVER (PARTITION BY s.id ORDER BY SUM(si.total_price) DESC) as rank_in_store
            FROM stores s
            JOIN sales sa ON s.id = sa.store_id 
                AND sa.created_at BETWEEN ? AND ?
                AND sa.status = 'completed'
            JOIN sale_items si ON sa.id = si.sale_id
            JOIN products p ON si.product_id = p.id
            WHERE s.is_active = 1
            GROUP BY s.id, s.name, p.id, p.name
            HAVING total_quantity_sold > 0
            ORDER BY s.id, total_revenue DESC
        ", [$dateRange[0], $dateRange[1]]);
    }

    /**
     * Métriques opérationnelles
     */
    public function getOperationalMetrics($dateRange)
    {
        return DB::select("
            SELECT 
                s.id,
                s.name,
                COUNT(DISTINCT u.id) as total_employees,
                COUNT(DISTINCT sa.user_id) as active_employees,
                COUNT(sa.id) / NULLIF(COUNT(DISTINCT sa.user_id), 0) as sales_per_employee,
                SUM(sa.total_amount) / NULLIF(COUNT(DISTINCT sa.user_id), 0) as revenue_per_employee,
                AVG(TIMESTAMPDIFF(MINUTE, sa.created_at, sa.updated_at)) as avg_sale_processing_time,
                COUNT(CASE WHEN sa.payment_method = 'cash' THEN 1 END) as cash_sales,
                COUNT(CASE WHEN sa.payment_method = 'credit' THEN 1 END) as credit_sales,
                (COUNT(CASE WHEN sa.payment_method = 'cash' THEN 1 END) * 100.0 / COUNT(sa.id)) as cash_sales_percentage
            FROM stores s
            LEFT JOIN users u ON s.id = u.store_id AND u.is_active = 1
            LEFT JOIN sales sa ON s.id = sa.store_id 
                AND sa.created_at BETWEEN ? AND ?
                AND sa.status = 'completed'
            WHERE s.is_active = 1
            GROUP BY s.id, s.name
            ORDER BY revenue_per_employee DESC
        ", [$dateRange[0], $dateRange[1]]);
    }

    /**
     * Analyse des tendances saisonnières
     */
    public function getSeasonalTrends($storeId = null, $months = 12)
    {
        $query = "
            SELECT 
                YEAR(sa.created_at) as year,
                MONTH(sa.created_at) as month,
                MONTHNAME(sa.created_at) as month_name,
                " . ($storeId ? "s.name as store_name," : "") . "
                COUNT(sa.id) as sales_count,
                SUM(sa.total_amount) as total_revenue,
                SUM(si.profit_amount) as total_profit,
                AVG(sa.total_amount) as avg_sale_value
            FROM sales sa
            JOIN stores s ON sa.store_id = s.id
            JOIN sale_items si ON sa.id = si.sale_id
            WHERE sa.created_at >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            AND sa.status = 'completed'
            " . ($storeId ? "AND sa.store_id = ?" : "") . "
            GROUP BY YEAR(sa.created_at), MONTH(sa.created_at)" . ($storeId ? ", s.name" : "") . "
            ORDER BY year DESC, month DESC
        ";

        $params = [$months];
        if ($storeId) {
            $params[] = $storeId;
        }

        return DB::select($query, $params);
    }

    /**
     * Rapport de rentabilité par magasin
     */
    public function getProfitabilityReport($dateRange)
    {
        return [
            'summary' => $this->getProfitabilitySummary($dateRange),
            'by_store' => $this->getProfitabilityByStore($dateRange),
            'by_product_category' => $this->getProfitabilityByCategory($dateRange),
            'trends' => $this->getProfitabilityTrends($dateRange)
        ];
    }

    /**
     * Obtenir la plage de dates selon la période
     */
    private function getDateRange($period)
    {
        switch ($period) {
            case 'today':
                return [Carbon::today(), Carbon::tomorrow()];
            case 'week':
                return [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()];
            case 'month':
                return [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
            case 'quarter':
                return [Carbon::now()->startOfQuarter(), Carbon::now()->endOfQuarter()];
            case 'year':
                return [Carbon::now()->startOfYear(), Carbon::now()->endOfYear()];
            default:
                return [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
        }
    }

    /**
     * Invalider le cache des analytics
     */
    public function invalidateCache()
    {
        $patterns = [
            'store_comparison_dashboard_*',
            'performance_comparison_*',
            'revenue_trends_*',
            'profit_analysis_*'
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }
}
