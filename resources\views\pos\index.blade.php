<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <i class="fas fa-cash-register mr-2"></i>{{ __('Point de Vente') }}
                </h2>
                @if($userStore)
                    <p class="text-sm text-gray-600 mt-1">
                        <i class="fas fa-store mr-1"></i>{{ $userStore->name }} ({{ $userStore->code }})
                        @if($userStore->address)
                            • {{ $userStore->address }}
                        @endif
                    </p>
                @endif
            </div>
            @if($userStore)
                <div class="flex items-center space-x-4 text-sm">
                    <div class="bg-blue-50 px-3 py-2 rounded-lg">
                        <span class="text-blue-700 font-medium">Produits en stock:</span>
                        <span class="text-blue-900 font-bold">{{ $storeStats['products_in_stock'] ?? 0 }}</span>
                    </div>
                    @if(($storeStats['low_stock_count'] ?? 0) > 0)
                        <div class="bg-yellow-50 px-3 py-2 rounded-lg">
                            <span class="text-yellow-700 font-medium">Stock faible:</span>
                            <span class="text-yellow-900 font-bold">{{ $storeStats['low_stock_count'] }}</span>
                        </div>
                    @endif
                    @if(($storeStats['out_of_stock_count'] ?? 0) > 0)
                        <div class="bg-red-50 px-3 py-2 rounded-lg">
                            <span class="text-red-700 font-medium">Rupture:</span>
                            <span class="text-red-900 font-bold">{{ $storeStats['out_of_stock_count'] }}</span>
                        </div>
                    @endif
                </div>
            @endif
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">

                <!-- Section Produits -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">
                                    <i class="fas fa-box mr-2"></i>Produits
                                </h3>
                                <div class="flex space-x-2">
                                    <input type="text" id="product-search" placeholder="Rechercher un produit..."
                                        class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <input type="text" id="barcode-input" placeholder="Scanner code-barres..."
                                        class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>

                            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto"
                                id="products-grid">
                                @foreach ($products as $product)
                                    @php
                                        $storeStock = $product->storeStocks->where('store_id', $userStore->id)->first();
                                        $currentPrice = $product->getCurrentPrice($userStore->id);
                                        $stockQuantity = $storeStock ? $storeStock->quantity : 0;
                                        $isLowStock = $storeStock ? $storeStock->isLowStock() : true;
                                        $isOutOfStock = $stockQuantity <= 0;
                                    @endphp
                                    <div class="product-card border border-gray-200 rounded-lg p-3 hover:shadow-md cursor-pointer transition-shadow {{ $isOutOfStock ? 'opacity-50 cursor-not-allowed' : '' }}"
                                        data-product-id="{{ $product->id }}"
                                        data-product-name="{{ $product->name }}"
                                        data-product-stock="{{ $stockQuantity }}"
                                        data-product-barcode="{{ $product->barcode }}"
                                        data-current-price="{{ $currentPrice ? $currentPrice->selling_price : 0 }}"
                                        data-cost-price="{{ $currentPrice ? $currentPrice->cost_price : 0 }}"
                                        data-has-price="{{ $currentPrice ? 'true' : 'false' }}"
                                        data-out-of-stock="{{ $isOutOfStock ? 'true' : 'false' }}">
                                        <div class="text-center">
                                            <div class="w-16 h-16 bg-gray-100 rounded-lg mx-auto mb-2 flex items-center justify-center relative">
                                                @if($product->image)
                                                    <img src="{{ $product->image }}" alt="{{ $product->name }}" class="w-full h-full object-cover rounded-lg">
                                                @else
                                                    <i class="fas fa-box text-gray-400 text-xl"></i>
                                                @endif
                                                @if($isOutOfStock)
                                                    <div class="absolute inset-0 bg-red-500 bg-opacity-20 rounded-lg flex items-center justify-center">
                                                        <i class="fas fa-times-circle text-red-600 text-lg"></i>
                                                    </div>
                                                @endif
                                            </div>
                                            <h4 class="font-medium text-sm text-gray-900 mb-1">{{ $product->name }}</h4>
                                            <div class="space-y-1">
                                                <p class="text-xs {{ $isOutOfStock ? 'text-red-600' : ($isLowStock ? 'text-yellow-600' : 'text-gray-500') }}">
                                                    Stock: {{ $stockQuantity }}
                                                    @if($storeStock && $storeStock->location)
                                                        <br><span class="text-blue-600">{{ $storeStock->location }}</span>
                                                    @endif
                                                </p>
                                                @if($currentPrice)
                                                    <p class="text-xs text-green-600 font-medium">
                                                        {{ number_format($currentPrice->selling_price, 0, ',', ' ') }} FCFA
                                                    </p>
                                                @else
                                                    <p class="text-xs text-orange-600">Prix non défini</p>
                                                @endif
                                            </div>
                                            @if($isOutOfStock)
                                                <span class="inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                                                    Rupture
                                                </span>
                                            @elseif($isLowStock)
                                                <span class="inline-block mt-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                                                    Stock faible
                                                </span>
                                            @elseif(!$currentPrice)
                                                <span class="inline-block mt-1 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">
                                                    Sans prix
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            @if($products->isEmpty())
                                <div class="text-center py-8">
                                    <i class="fas fa-box-open text-4xl text-gray-400 mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun produit disponible</h3>
                                    <p class="text-gray-500">Aucun produit n'est disponible dans ce magasin.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Section Panier -->
                <div class="lg:col-span-1">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-900">
                                    <i class="fas fa-shopping-cart mr-2"></i>Panier
                                </h3>
                                @if($userStore)
                                    <div class="text-xs text-gray-500">
                                        <i class="fas fa-store mr-1"></i>{{ $userStore->code }}
                                    </div>
                                @endif
                            </div>

                            <!-- Client Selection avec combobox -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Client</label>
                                <div class="relative">
                                    <div class="relative">
                                        <input type="text" id="customer-search"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Rechercher ou sélectionner un client..." autocomplete="off">
                                        <div id="customer-results"
                                            class="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md overflow-hidden hidden">
                                            <!-- Les résultats de recherche seront injectés ici -->
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="selected-customer-id" name="customer_id">
                                <!-- Balance du client -->
                                <div id="customer-balance" class="mt-2 hidden">
                                    <p class="text-sm text-gray-600">
                                        Balance actuelle: <span class="font-medium" id="balance-amount">0 FCFA</span>
                                    </p>
                                </div>
                            </div>

                            <!-- Payment Method -->
                            <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Mode de paiement</label>
                                <div class="flex space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="payment_method" value="cash" checked
                                            class="mr-2">
                                        <i class="fas fa-money-bill-wave mr-2 text-green-600"></i>Espèces
                                    </label>
                                    <label id="credit-payment-option" class="items-center" style="display: none;">
                                        <input type="radio" name="payment_method" value="credit" class="mr-2">
                                        <i class="fas fa-credit-card mr-2 text-yellow-600"></i>Crédit
                                    </label>
                                </div>
                            </div>

                            <!-- Date d'échéance (pour le crédit) -->
                            <div id="due-date-section" class="mb-4 hidden">
                                <label for="due-date" class="block text-sm font-medium text-gray-700 mb-2">
                                    Date d'échéance <span class="text-red-500">*</span>
                                </label>
                                <input type="date" id="due-date" name="due_date" min="{{ date('Y-m-d') }}"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- Cart Items -->
                            <div class="space-y-2 mb-4 max-h-64 overflow-y-auto" id="cart-items">
                                <p class="text-gray-500 text-center py-8">Panier vide</p>
                            </div>

                            <!-- Totals -->
                            <div class="border-t pt-4 space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>Total avant remise:</span>
                                    <span id="subtotal">0 FCFA</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>Remise:</span>
                                    <input type="number" id="discount-input" value="0" min="0"
                                        class="w-20 px-2 py-1 text-right border border-gray-300 rounded text-sm">
                                </div>
                                <div class="flex justify-between text-lg font-bold border-t pt-2">
                                    <span>Total à payer:</span>
                                    <span id="total-amount">0 FCFA</span>
                                </div>

                                <!-- Résumé des bénéfices -->
                                <div id="profit-summary" class="hidden"></div>
                            </div>

                            <!-- Amount Paid -->
                            <div class="mt-4" id="amount-paid-section">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Montant reçu</label>
                                <input type="number" id="amount-paid" min="0" step="0.01"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-sm text-gray-500 mt-1">
                                    Monnaie à rendre: <span id="change-amount" class="font-medium">0 FCFA</span>
                                </p>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-6 space-y-2">
                                <button id="process-sale"
                                    class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-check mr-2"></i>Finaliser la vente
                                </button>
                                <button id="clear-cart"
                                    class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-trash mr-2"></i>Vider le panier
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Prix et Quantité Produit -->
    <div id="price-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Ajouter au panier</h3>
                <form id="price-form" class="space-y-4" data-no-loading>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Produit</label>
                        <p id="modal-product-name" class="text-sm text-gray-900 font-medium"></p>
                        <div class="flex items-center justify-between text-xs mt-1">
                            <span id="modal-product-stock" class="text-gray-500"></span>
                            <span id="modal-product-location" class="text-blue-600"></span>
                        </div>
                    </div>

                    <!-- Informations de prix du magasin -->
                    <div id="store-price-info" class="p-3 bg-blue-50 rounded-lg hidden">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Prix défini pour ce magasin</h4>
                        <div class="grid grid-cols-2 gap-2 text-xs">
                            <div>
                                <span class="text-blue-700">Prix de vente:</span>
                                <span id="store-selling-price" class="text-blue-900 font-medium ml-1"></span>
                            </div>
                            <div>
                                <span class="text-blue-700">Prix coût:</span>
                                <span id="store-cost-price" class="text-blue-900 font-medium ml-1"></span>
                            </div>
                        </div>
                        <button type="button" onclick="useStorePrice()"
                               class="mt-2 text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">
                            Utiliser ce prix
                        </button>
                    </div>

                    <div>
                        <label for="unit-price" class="block text-sm font-medium text-gray-700 mb-2">
                            Prix unitaire (FCFA) <span class="text-red-500">*</span>
                        </label>
                        <input type="number" id="unit-price" required min="0" step="0.01"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="text-xs text-gray-500 mt-1">
                            <span id="profit-info" class="hidden">
                                Bénéfice estimé: <span id="estimated-profit" class="font-medium text-green-600"></span>
                                (<span id="profit-margin" class="text-green-600"></span>%)
                            </span>
                        </p>
                    </div>

                    <div>
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Quantité <span class="text-red-500">*</span>
                        </label>
                        <div class="flex items-center space-x-2">
                            <button type="button" onclick="decrementQuantity()"
                                class="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" id="quantity" required min="1" value="1"
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-center">
                            <button type="button" onclick="incrementQuantity()"
                                class="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg">
                        <p class="text-sm font-medium text-gray-700">Total pour cet article:</p>
                        <p id="modal-item-total" class="text-lg font-bold text-green-600">0 FCFA</p>
                    </div>

                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closePriceModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Ajouter au panier
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            // Variables globales
            let cart = [];
            let products = @json($products);
            let tempProductData = null;
            const clients = @json($clients);

            // Fonction clearCart manquante
            function clearCart(silent = false) {
                cart = [];
                updateCartDisplay();
                // Réinitialiser les champs
                document.getElementById('discount-input').value = '0';
                document.getElementById('amount-paid').value = '';
                document.getElementById('customer-search').value = '';
                document.getElementById('selected-customer-id').value = '';
                window.selectedCustomerBalance = undefined;
                // Masquer la balance client si affichée
                const customerBalance = document.getElementById('customer-balance');
                if (customerBalance) customerBalance.classList.add('hidden');
                document.querySelector('input[name="payment_method"][value="cash"]').checked = true;
                handleCustomerChange();

                if (!silent) {
                    notyf.success('Le panier a été vidé');
                }
            }

            // Initialisation
            document.addEventListener('DOMContentLoaded', function() {
                initializeEventListeners();
                updateCartDisplay();
                handleCustomerChange();
                initializeCustomerSelect();
            });

            // Fonction pour initialiser les écouteurs d'événements
            function initializeEventListeners() {
                // Recherche de produits
                document.getElementById('product-search').addEventListener('input', filterProducts);

                // Scanner code-barres
                document.getElementById('barcode-input').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        addProductByBarcode(this.value);
                        this.value = '';
                    }
                });

                // Clic sur produit
                document.querySelectorAll('.product-card').forEach(card => {
                    card.addEventListener('click', function() {
                        // Vérifier si le produit est en rupture de stock
                        if (this.dataset.outOfStock === 'true') {
                            notyf.error('Ce produit est en rupture de stock dans ce magasin');
                            return;
                        }

                        // Vérifier si le produit a un prix défini
                        if (this.dataset.hasPrice === 'false') {
                            notyf.error('Aucun prix défini pour ce produit dans ce magasin');
                            return;
                        }

                        showPriceModal(this.dataset);
                    });
                });

                // Mode de paiement
                document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
                    radio.addEventListener('change', function() {
                        togglePaymentFields(this.value);
                    });
                });

                // Montant reçu
                document.getElementById('amount-paid').addEventListener('input', calculateChange);

                // Remise
                document.getElementById('discount-input').addEventListener('input', updateTotals);

                // Formulaire de prix
                document.getElementById('price-form').addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const submitBtn = e.target.querySelector('button[type="submit"]');
                    submitBtn.disabled = true;

                    const price = parseFloat(document.getElementById('unit-price').value);
                    const quantity = parseInt(document.getElementById('quantity').value);
                    const maxStock = parseInt(tempProductData.productStock);

                    if (price <= 0) {
                        notyf.error('Le prix doit être supérieur à 0');
                        submitBtn.disabled = false;
                        return;
                    }

                    if (quantity <= 0) {
                        notyf.error('La quantité doit être supérieure à 0');
                        submitBtn.disabled = false;
                        return;
                    }

                    if (quantity > maxStock) {
                        notyf.error(`Stock insuffisant. Maximum disponible: ${maxStock}`);
                        submitBtn.disabled = false;
                        return;
                    }

                    try {
                        const success = addProductToCart({
                            ...tempProductData,
                            productPrice: price,
                            initialQuantity: quantity
                        });

                        if (success) {
                            notyf.success('Produit ajouté au panier');
                            closePriceModal();
                        }
                    } finally {
                        submitBtn.disabled = false;
                        submitBtn.textContent = 'Ajouter';
                    }
                });


                // Boutons d'action
                document.getElementById('process-sale').addEventListener('click', processSale);
                document.getElementById('clear-cart').addEventListener('click', clearCart);

                // Événements pour le calcul en temps réel dans la modale
                document.getElementById('unit-price').addEventListener('input', updateModalTotal);
                document.getElementById('quantity').addEventListener('input', updateModalTotal);
            }

            $('#addProductModal').on('show.bs.modal', function() {
                const submitBtn = document.querySelector('#price-form button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Ajouter';
                }
            });

            // Fonction pour traiter la vente
            function handleCustomerChange() {
                const selectedCustomerId = document.getElementById('selected-customer-id').value;
                const creditOption = document.getElementById('credit-payment-option');
                const customerBalance = document.getElementById('customer-balance');
                const balanceAmount = document.getElementById('balance-amount');
                creditOption.style.display = selectedCustomerId ? 'flex' : 'none';
                if (!selectedCustomerId) {
                    document.querySelector('input[name="payment_method"][value="cash"]').checked = true;
                    togglePaymentFields('cash');
                }
                if (selectedCustomerId && window.selectedCustomerBalance !== undefined) {
                    customerBalance.classList.remove('hidden');
                    balanceAmount.textContent = window.selectedCustomerBalance.toLocaleString() + ' FCFA';
                } else {
                    customerBalance.classList.add('hidden');
                }
            }

            // Fonction pour traiter la vente
            function togglePaymentFields(paymentMethod) {
                const amountPaidSection = document.getElementById('amount-paid-section');
                const dueDateSection = document.getElementById('due-date-section');

                if (paymentMethod === 'credit') {
                    amountPaidSection.classList.add('hidden');
                    dueDateSection.classList.remove('hidden');
                    // Rendre la date d'échéance obligatoire
                    document.getElementById('due-date').required = true;
                } else {
                    amountPaidSection.classList.remove('hidden');
                    dueDateSection.classList.add('hidden');
                    document.getElementById('due-date').required = false;
                }
            }

            // Fonction pour mettre à jour le total dans la modale
            function updateModalTotal() {
                const price = parseFloat(document.getElementById('unit-price').value) || 0;
                const quantity = parseInt(document.getElementById('quantity').value) || 0;
                const total = price * quantity;
                document.getElementById('modal-item-total').textContent = total.toLocaleString() + ' FCFA';

                // Mettre à jour les informations de bénéfice
                if (tempProductData && tempProductData.costPrice) {
                    updateProfitInfo(price, parseFloat(tempProductData.costPrice));
                }
            }

            // Fonction pour incrémenter la quantité
            function incrementQuantity() {
                const input = document.getElementById('quantity');
                const currentValue = parseInt(input.value) || 0;
                const maxStock = parseInt(tempProductData.productStock);
                if (currentValue < maxStock) {
                    input.value = currentValue + 1;
                    updateModalTotal();
                } else {
                    notyf.error(`Stock insuffisant. Maximum disponible: ${maxStock}`);
                }
            }

            // Fonction pour décrémenter la quantité
            function decrementQuantity() {
                const input = document.getElementById('quantity');
                const currentValue = parseInt(input.value) || 0;
                if (currentValue > 1) {
                    input.value = currentValue - 1;
                    updateModalTotal();
                }
            }

            // Fonction pour afficher la modale de prix et quantité
            function showPriceModal(productData) {
                tempProductData = productData;
                document.getElementById('modal-product-name').textContent = productData.productName;
                document.getElementById('modal-product-stock').textContent =
                    `Stock disponible: ${productData.productStock}`;

                // Afficher l'emplacement si disponible
                const locationElement = document.getElementById('modal-product-location');
                if (productData.productLocation) {
                    locationElement.textContent = productData.productLocation;
                    locationElement.style.display = 'inline';
                } else {
                    locationElement.style.display = 'none';
                }

                // Afficher les informations de prix du magasin
                const storePriceInfo = document.getElementById('store-price-info');
                const hasPrice = productData.hasPrice === 'true';

                if (hasPrice) {
                    const sellingPrice = parseFloat(productData.currentPrice);
                    const costPrice = parseFloat(productData.costPrice);

                    document.getElementById('store-selling-price').textContent =
                        sellingPrice.toLocaleString() + ' FCFA';
                    document.getElementById('store-cost-price').textContent =
                        costPrice.toLocaleString() + ' FCFA';

                    storePriceInfo.classList.remove('hidden');

                    // Pré-remplir avec le prix de vente du magasin
                    document.getElementById('unit-price').value = sellingPrice;
                    updateProfitInfo(sellingPrice, costPrice);
                } else {
                    storePriceInfo.classList.add('hidden');
                    document.getElementById('unit-price').value = '';
                }

                document.getElementById('quantity').value = '1';
                updateModalTotal();
                document.getElementById('price-modal').classList.remove('hidden');
                document.getElementById('unit-price').focus();
            }

            // Fonction pour utiliser le prix du magasin
            function useStorePrice() {
                if (tempProductData && tempProductData.hasPrice === 'true') {
                    const sellingPrice = parseFloat(tempProductData.currentPrice);
                    document.getElementById('unit-price').value = sellingPrice;
                    updateModalTotal();
                    updateProfitInfo(sellingPrice, parseFloat(tempProductData.costPrice));
                }
            }

            // Fonction pour mettre à jour les informations de bénéfice
            function updateProfitInfo(sellingPrice, costPrice) {
                if (costPrice && costPrice > 0) {
                    const quantity = parseInt(document.getElementById('quantity').value) || 1;
                    const profit = (sellingPrice - costPrice) * quantity;
                    const margin = ((sellingPrice - costPrice) / sellingPrice * 100);

                    document.getElementById('estimated-profit').textContent =
                        profit.toLocaleString() + ' FCFA';
                    document.getElementById('profit-margin').textContent =
                        margin.toFixed(1);
                    document.getElementById('profit-info').classList.remove('hidden');
                } else {
                    document.getElementById('profit-info').classList.add('hidden');
                }
            }

            // Fonction pour fermer la modale de prix et quantité
            function closePriceModal() {
                document.getElementById('price-modal').classList.add('hidden');
                document.getElementById('price-form').reset();
                tempProductData = null;
            }

            // Fonction pour traiter la vente
            function filterProducts() {
                const searchTerm = document.getElementById('product-search').value.toLowerCase();
                document.querySelectorAll('.product-card').forEach(card => {
                    const productName = card.dataset.productName.toLowerCase();
                    card.style.display = productName.includes(searchTerm) ? 'block' : 'none';
                });
            }

            // Fonction pour traiter la vente
            function addProductByBarcode(barcode) {
                const product = products.find(p => p.barcode === barcode);
                if (product) {
                    const success = addProductToCart({
                        productId: product.id,
                        productName: product.name,
                        productPrice: product.price,
                        productStock: product.stock_quantity,
                        initialQuantity: 1
                    });
                    if (success) {
                        notyf.success('Produit ajouté au panier');
                    }
                } else {
                    notyf.error('Produit non trouvé');
                }
            }

            // Fonction pour traiter la vente
            function addProductToCart(productData) {
                const initialQuantity = productData.initialQuantity || 1;
                const existingItem = cart.find(item => item.productId == productData.productId);

                try {
                    if (existingItem) {
                        const newQuantity = existingItem.quantity + initialQuantity;
                        if (newQuantity <= productData.productStock) {
                            existingItem.quantity = newQuantity;
                            existingItem.price = parseFloat(productData.productPrice);
                        } else {
                            notyf.error('Stock insuffisant');
                            return false;
                        }
                    } else {
                        cart.push({
                            productId: productData.productId,
                            productName: productData.productName,
                            price: parseFloat(productData.productPrice),
                            costPrice: parseFloat(productData.costPrice) || 0,
                            quantity: initialQuantity,
                            stock: parseInt(productData.productStock)
                        });
                    }

                    updateCartDisplay();
                    return true;
                } catch (error) {
                    console.error('Erreur lors de l\'ajout au panier:', error);
                    notyf.error('Une erreur est survenue lors de l\'ajout au panier');
                    return false;
                }
            }

            // Fonction pour supprimer un produit du panier
            function removeFromCart(productId) {
                cart = cart.filter(item => item.productId != productId);
                updateCartDisplay();
            }

            function updateQuantity(productId, newQuantity) {
                const item = cart.find(item => item.productId == productId);
                if (item) {
                    if (newQuantity <= 0) {
                        removeFromCart(productId);
                    } else if (newQuantity <= item.stock) {
                        item.quantity = newQuantity;
                        updateCartDisplay();
                    } else {
                        notyf.error('Stock insuffisant');
                    }
                }
            }

            function updateCartDisplay() {
                const cartContainer = document.getElementById('cart-items');

                if (cart.length === 0) {
                    cartContainer.innerHTML = '<p class="text-gray-500 text-center py-8">Panier vide</p>';
                } else {
                    cartContainer.innerHTML = cart.map(item => {
                        const itemTotal = item.price * item.quantity;
                        const itemProfit = item.costPrice > 0 ? (item.price - item.costPrice) * item.quantity : 0;
                        const profitMargin = item.costPrice > 0 ? ((item.price - item.costPrice) / item.price * 100) : 0;

                        return `
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                <div class="flex-1">
                                    <p class="font-medium text-sm">${item.productName}</p>
                                    <p class="text-xs text-gray-500">${item.price.toLocaleString()} FCFA x ${item.quantity} = ${itemTotal.toLocaleString()} FCFA</p>
                                    ${item.costPrice > 0 ? `<p class="text-xs text-green-600">Bénéfice: ${itemProfit.toLocaleString()} FCFA (${profitMargin.toFixed(1)}%)</p>` : ''}
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="updateQuantity(${item.productId}, ${item.quantity - 1})"
                                            class="w-6 h-6 bg-red-500 text-white rounded text-xs">-</button>
                                    <span class="w-8 text-center text-sm">${item.quantity}</span>
                                    <button onclick="updateQuantity(${item.productId}, ${item.quantity + 1})"
                                            class="w-6 h-6 bg-green-500 text-white rounded text-xs">+</button>
                                    <button onclick="removeFromCart(${item.productId})"
                                            class="w-6 h-6 bg-red-600 text-white rounded text-xs">×</button>
                                </div>
                            </div>
                        `;
                    }).join('');
                }

                updateTotals();
            }

            function updateTotals() {
                let subtotal = 0;
                let totalProfit = 0;
                let totalCost = 0;

                cart.forEach(item => {
                    const itemTotal = item.price * item.quantity;
                    const itemCost = item.costPrice * item.quantity;
                    subtotal += itemTotal;
                    totalCost += itemCost;
                    if (item.costPrice > 0) {
                        totalProfit += (item.price - item.costPrice) * item.quantity;
                    }
                });

                const discount = parseFloat(document.getElementById('discount-input').value) || 0;
                const total = subtotal - discount;
                const finalProfit = totalProfit - discount; // Le discount réduit le bénéfice

                document.getElementById('subtotal').textContent = subtotal.toLocaleString() + ' FCFA';
                document.getElementById('total-amount').textContent = total.toLocaleString() + ' FCFA';

                // Afficher les informations de bénéfice
                const profitInfo = document.getElementById('profit-summary');
                if (profitInfo) {
                    if (totalProfit > 0) {
                        const profitMargin = totalCost > 0 ? (finalProfit / total * 100) : 0;
                        profitInfo.innerHTML = `
                            <div class="text-xs text-green-600 mt-2 p-2 bg-green-50 rounded">
                                <div class="flex justify-between">
                                    <span>Bénéfice total:</span>
                                    <span class="font-medium">${finalProfit.toLocaleString()} FCFA</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Marge bénéficiaire:</span>
                                    <span class="font-medium">${profitMargin.toFixed(1)}%</span>
                                </div>
                            </div>
                        `;
                        profitInfo.classList.remove('hidden');
                    } else {
                        profitInfo.classList.add('hidden');
                    }
                }

                calculateChange();
            }

            function calculateChange() {
                const total = parseFloat(document.getElementById('total-amount').textContent.replace(/[^\d]/g, ''));
                const amountPaid = parseFloat(document.getElementById('amount-paid').value) || 0;
                const change = Math.max(0, amountPaid - total);

                document.getElementById('change-amount').textContent = change.toLocaleString() + ' FCFA';
            }

            // Fonction pour finaliser la vente
            async function processSale() {
                // Vérifications de base
                if (cart.length === 0) {
                    notyf.error('Le panier est vide');
                    return;
                }

                const customerId = document.getElementById('selected-customer-id').value;
                const paymentMethod = document.querySelector('input[name="payment_method"]:checked').value;
                const totalAmount = parseFloat(document.getElementById('total-amount').textContent.replace(/[^\d]/g, ''));
                const discount = parseFloat(document.getElementById('discount-input').value) || 0;

                // Vérifications supplémentaires pour le paiement en espèces
                if (paymentMethod === 'cash') {
                    const amountPaid = parseFloat(document.getElementById('amount-paid').value) || 0;
                    if (amountPaid < totalAmount) {
                        notyf.error('Le montant payé est insuffisant');
                        return;
                    }
                }

                // Vérifications pour le paiement à crédit
                if (paymentMethod === 'credit') {
                    if (!customerId) {
                        notyf.error('Veuillez sélectionner un client pour le paiement à crédit');
                        return;
                    }
                    const dueDate = document.getElementById('due-date').value;
                    if (!dueDate) {
                        notyf.error('Veuillez spécifier une date d\'échéance pour le crédit');
                        return;
                    }
                }

                try {
                    // Préparation des données de la vente
                    const saleData = {
                        customer_id: customerId || null,
                        payment_method: paymentMethod,
                        store_id: {{ $userStore ? $userStore->id : 'null' }}, // ID du magasin actuel
                        items: cart.map(item => ({
                            product_id: item.productId,
                            quantity: item.quantity,
                            unit_price: item.price,
                            cost_price: item.costPrice || 0 // Prix de revient pour le calcul du bénéfice
                        })),
                        total_amount: totalAmount,
                        discount: discount,
                        due_date: paymentMethod === 'credit' ? document.getElementById('due-date').value : null,
                        amount_paid: paymentMethod === 'cash' ? parseFloat(document.getElementById('amount-paid')
                            .value) : 0
                    };

                    // Récupération du token CSRF depuis le meta tag
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
                    if (!csrfToken) {
                        throw new Error('CSRF token non trouvé');
                    }

                    // Envoi de la requête au serveur
                    const response = await fetch('/pos/sale', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(saleData)
                    });

                    const data = await response.json();

                    if (!response.ok) {
                        // Si le serveur renvoie une erreur avec un message
                        const errorMessage = data.message || 'Erreur lors de l\'enregistrement de la vente';
                        throw new Error(errorMessage);
                    }

                    // Si la vente est réussie
                    notyf.success(data.message || 'Vente enregistrée avec succès');
                    clearCart(true); // Vider le panier silencieusement

                    // Actualiser la page après 1.5 secondes pour permettre à l'utilisateur de voir le message
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);

                } catch (error) {
                    console.error('Erreur:', error);
                    notyf.error(error.message || 'Une erreur est survenue lors de l\'enregistrement de la vente');
                }
            }

            function initializeCustomerSelect() {
                const customerSearch = document.getElementById('customer-search');
                const customerResults = document.getElementById('customer-results');
                const selectedCustomerId = document.getElementById('selected-customer-id');
                let isDropdownOpen = false;

                // Afficher tous les clients
                function showAllClients() {
                    if (!isDropdownOpen) {
                        displayResults(clients);
                        isDropdownOpen = true;
                        customerResults.classList.remove('hidden');
                    }
                }

                // Fonction de filtrage des clients
                function filterClients(query) {
                    if (!query) return clients;
                    query = query.toLowerCase();
                    return clients.filter(client =>
                        client.name.toLowerCase().includes(query) ||
                        (client.phone && client.phone.toLowerCase().includes(query))
                    );
                }

                // Fonction d'affichage des résultats
                function displayResults(filteredClients) {
                    customerResults.innerHTML = filteredClients.length === 0 ?
                        '<div class="p-2 text-gray-500">Aucun client trouvé</div>' :
                        filteredClients.map((client, idx) => `
                            <div class="cursor-pointer select-none relative py-2 px-3 hover:bg-blue-100 focus:bg-blue-100 transition-colors customer-option"
                                tabindex="0" data-idx="${idx}" data-id="${client.id}" data-name="${client.name}" 
                                data-balance="${client.current_balance}"
                                role="option">
                                <div class="flex items-center">
                                    <div class="flex-1">
                                        <span class="block text-sm font-medium text-gray-900">${client.name}</span>
                                        ${client.phone ? `<span class="block text-xs text-gray-500">${client.phone}</span>` : ''}
                                    </div>
                                    <span class="text-xs text-gray-500">
                                        Balance: ${Number(client.current_balance).toLocaleString()} FCFA
                                    </span>
                                </div>
                            </div>
                        `).join('');
                }

                // Gestionnaire de focus et de clic
                customerSearch.addEventListener('click', () => {
                    showAllClients();
                });

                customerSearch.addEventListener('focus', () => {
                    showAllClients();
                });

                // Gestionnaire de la recherche
                let searchTimeout;
                customerSearch.addEventListener('input', function() {
                    const query = this.value.trim();
                    clearTimeout(searchTimeout);

                    // Si le champ est vide, afficher tous les clients
                    if (query.length === 0) {
                        showAllClients();
                        selectedCustomerId.value = '';
                        window.selectedCustomerBalance = undefined;
                        handleCustomerChange();
                        return;
                    }

                    searchTimeout = setTimeout(() => {
                        const filteredClients = filterClients(query);
                        displayResults(filteredClients);
                        customerResults.classList.remove('hidden');
                        isDropdownOpen = true;
                    }, 100);
                });


                // Sélection à la souris
                customerResults.addEventListener('click', function(e) {
                    const target = e.target.closest('[data-id]');
                    if (target) {
                        selectCustomer(target.dataset.id, target.dataset.name, target.dataset.balance);
                    }
                });

                // Sélection au clavier
                customerSearch.addEventListener('keydown', function(e) {
                    const items = customerResults.querySelectorAll('[data-id]');
                    let idx = Array.from(items).findIndex(item => item.classList.contains('bg-blue-100'));
                    // On utilise la variable globale clients définie plus haut
                    if (e.key === 'ArrowDown') {
                        if (items.length === 0) return;
                        idx = (idx + 1) % items.length;
                        items.forEach(item => item.classList.remove('bg-blue-100'));
                        items[idx].classList.add('bg-blue-100');
                        items[idx].focus();
                        e.preventDefault();
                    } else if (e.key === 'ArrowUp') {
                        if (items.length === 0) return;
                        idx = (idx - 1 + items.length) % items.length;
                        items.forEach(item => item.classList.remove('bg-blue-100'));
                        items[idx].classList.add('bg-blue-100');
                        items[idx].focus();
                        e.preventDefault();
                    } else if (e.key === 'Enter' && idx >= 0) {
                        items[idx].click();
                        e.preventDefault();
                    } else if (e.key === 'Escape') {
                        customerResults.classList.add('hidden');
                    }
                });

                // Gestion du clic en dehors
                document.addEventListener('click', function(e) {
                    const isClickInside = customerResults.contains(e.target) || customerSearch.contains(e.target);
                    if (!isClickInside && isDropdownOpen) {
                        customerResults.classList.add('hidden');
                        isDropdownOpen = false;
                    }
                }, true);

                // Empêcher la propagation des clics dans la liste
                customerResults.addEventListener('click', (e) => {
                    e.stopPropagation();
                });

                // Accessibilité : rôle ARIA
                customerResults.setAttribute('role', 'listbox');
                customerSearch.setAttribute('aria-autocomplete', 'list');
                customerSearch.setAttribute('aria-controls', 'customer-results');
                customerSearch.setAttribute('role', 'combobox');
                customerSearch.setAttribute('aria-expanded', 'false');
                customerSearch.setAttribute('aria-haspopup', 'listbox');

                // Afficher les résultats au focus
                customerSearch.addEventListener('focus', function() {
                    if (this.value.trim().length > 0) {
                        const filteredClients = filterClients(this.value);
                        displayResults(filteredClients);
                    }
                });

                // Mettre à jour l'attribut aria-expanded
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.target === customerResults) {
                            customerSearch.setAttribute('aria-expanded',
                                !customerResults.classList.contains('hidden'));
                        }
                    });
                });
                observer.observe(customerResults, {
                    attributes: true,
                    attributeFilter: ['class']
                });

                window.selectCustomer = function(id, name, current_balance) {
                    if (id) {
                        document.getElementById('selected-customer-id').value = id;
                        document.getElementById('customer-search').value = name;
                        window.selectedCustomerBalance = parseFloat(current_balance);
                        handleCustomerChange();
                        customerResults.classList.add('hidden');
                        isDropdownOpen = false;
                        customerSearch.focus();
                    }
                };

                // Si un ID de client est passé dans l'URL, sélectionnez-le automatiquement
                const urlParams = new URLSearchParams(window.location.search);
                const customerId = urlParams.get('customer');
                if (customerId) {
                    const client = clients.find(c => c.id == customerId);
                    if (client) {
                        selectCustomer(client.id, client.name, client.current_balance);
                    }
                }
            }
            document.addEventListener('DOMContentLoaded', function() {
                initializeCustomerSelect();
            });
        </script>
    @endpush
</x-app-layout>
