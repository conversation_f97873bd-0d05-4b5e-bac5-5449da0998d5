<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <i class="fas fa-box mr-2"></i>{{ $product->name }}
            </h2>
            <div class="flex space-x-2">
                @can('edit_products')
                <a href="{{ route('products.edit', $product) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-edit mr-2"></i>Modifier
                </a>
                @endcan
                <a href="{{ route('products.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Informations du produit -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                
                <!-- Détails principaux -->
                <div class="lg:col-span-2 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-info-circle mr-2"></i>Informations du produit
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Nom</label>
                                    <p class="text-lg font-semibold text-gray-900">{{ $product->name }}</p>
                                </div>
                                
                                @if($product->description)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Description</label>
                                    <p class="text-gray-900">{{ $product->description }}</p>
                                </div>
                                @endif
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Code-barres</label>
                                    <p class="text-gray-900 font-mono">{{ $product->barcode ?? 'Non défini' }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Unité</label>
                                    <p class="text-gray-900">{{ $product->unit }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Statut</label>
                                    @if($product->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Actif
                                    </span>
                                    @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i>Inactif
                                    </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations de stock -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-warehouse mr-2"></i>Stock
                        </h3>
                        
                        <div class="space-y-4">
                            <div class="text-center">
                                <div class="text-3xl font-bold 
                                    @if($product->stock_quantity <= 0) text-red-600
                                    @elseif($product->isLowStock()) text-yellow-600
                                    @else text-green-600 @endif">
                                    {{ $product->stock_quantity }}
                                </div>
                                <p class="text-sm text-gray-500">{{ $product->unit }} en stock</p>
                            </div>
                            
                            <div class="border-t pt-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Seuil d'alerte:</span>
                                    <span class="font-medium">{{ $product->min_stock_level }}</span>
                                </div>
                                
                                @if($product->isLowStock())
                                <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                                    <p class="text-sm text-yellow-800">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        Stock faible - Réapprovisionnement recommandé
                                    </p>
                                </div>
                                @endif
                                
                                @if($product->stock_quantity <= 0)
                                <div class="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                                    <p class="text-sm text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Rupture de stock
                                    </p>
                                </div>
                                @endif
                            </div>
                            
                            @can('manage_stock')
                            <div class="border-t pt-4">
                                <button onclick="openStockModal({{ $product->id }}, '{{ $product->name }}', {{ $product->stock_quantity }})"
                                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors">
                                    <i class="fas fa-warehouse mr-2"></i>Ajuster le stock
                                </button>
                            </div>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>

            <!-- Historique des mouvements de stock -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-history mr-2"></i>Historique des mouvements de stock
                    </h3>
                    
                    @if($product->stockMovements->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Type
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Quantité
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Stock
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Raison
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Utilisateur
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($product->stockMovements->sortByDesc('created_at') as $movement)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $movement->created_at->format('d/m/Y H:i') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @switch($movement->type)
                                            @case('in')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-arrow-up mr-1"></i>Entrée
                                                </span>
                                                @break
                                            @case('out')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i class="fas fa-arrow-down mr-1"></i>Sortie
                                                </span>
                                                @break
                                            @case('adjustment')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <i class="fas fa-edit mr-1"></i>Ajustement
                                                </span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium
                                        @if($movement->quantity > 0) text-green-600 @else text-red-600 @endif">
                                        {{ $movement->quantity > 0 ? '+' : '' }}{{ $movement->quantity }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $movement->previous_stock }} → {{ $movement->new_stock }}
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        {{ $movement->reason }}
                                        @if($movement->reference)
                                        <br><span class="text-xs text-gray-500">Réf: {{ $movement->reference }}</span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $movement->user->name }}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-8">
                        <i class="fas fa-history text-gray-300 text-4xl mb-4"></i>
                        <p class="text-gray-500">Aucun mouvement de stock enregistré</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'ajustement de stock -->
    <div id="stock-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Ajuster le stock</h3>
                <form id="stock-form" method="POST">
                    @csrf
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Produit</label>
                        <p id="product-name" class="text-sm text-gray-900"></p>
                        <p class="text-sm text-gray-500">Stock actuel: <span id="current-stock"></span></p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Type d'ajustement</label>
                        <select name="adjustment_type" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="in">Entrée de stock</option>
                            <option value="out">Sortie de stock</option>
                            <option value="adjustment">Ajustement (nouveau stock)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quantité</label>
                        <input type="number" name="quantity" required min="1" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Raison</label>
                        <input type="text" name="reason" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md"
                               placeholder="Ex: Réapprovisionnement, Inventaire...">
                    </div>
                    
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="closeStockModal()" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Annuler
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Ajuster
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function openStockModal(productId, productName, currentStock) {
            document.getElementById('product-name').textContent = productName;
            document.getElementById('current-stock').textContent = currentStock;
            document.getElementById('stock-form').action = `/products/${productId}/adjust-stock`;
            document.getElementById('stock-modal').classList.remove('hidden');
        }

        function closeStockModal() {
            document.getElementById('stock-modal').classList.add('hidden');
            document.getElementById('stock-form').reset();
        }

        // Fermer le modal en cliquant à l'extérieur
        document.getElementById('stock-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeStockModal();
            }
        });
    </script>
    @endpush
</x-app-layout>
