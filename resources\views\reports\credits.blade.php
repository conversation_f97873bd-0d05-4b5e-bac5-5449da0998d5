<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-credit-card mr-3 text-blue-600"></i>Rapport des Crédits
                </h2>
                <p class="text-sm text-gray-600 mt-1">Analyse détaillée des crédits clients</p>
            </div>
            <div class="flex space-x-2">
                <a href="{{ route('reports.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Retour aux rapports
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filtres -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-filter mr-2"></i>Filtres et Export
                    </h3>
                    
                    <form method="GET" action="{{ route('reports.credits') }}" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                    Filtrer par statut
                                </label>
                                <select name="status" id="status" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="" {{ request('status') === '' ? 'selected' : '' }}>
                                        Tous les crédits
                                    </option>
                                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>
                                        Actifs
                                    </option>
                                    <option value="paid" {{ request('status') === 'paid' ? 'selected' : '' }}>
                                        Payés
                                    </option>
                                    <option value="overdue" {{ request('status') === 'overdue' ? 'selected' : '' }}>
                                        En retard
                                    </option>
                                    <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>
                                        Annulés
                                    </option>
                                </select>
                            </div>
                            
                            <div class="flex items-end">
                                <button type="submit" 
                                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-search mr-2"></i>Filtrer
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Boutons d'export -->
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex flex-wrap gap-2">
                            <form method="GET" action="{{ route('reports.credits') }}" class="inline">
                                <input type="hidden" name="status" value="{{ request('status') }}">
                                <input type="hidden" name="format" value="pdf">
                                <button type="submit" 
                                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-file-pdf mr-2"></i>Exporter PDF
                                </button>
                            </form>
                            
                            {{-- <form method="GET" action="{{ route('reports.credits') }}" class="inline">
                                <input type="hidden" name="status" value="{{ request('status') }}">
                                <input type="hidden" name="format" value="excel">
                                <button type="submit" 
                                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-file-excel mr-2"></i>Exporter Excel
                                </button>
                            </form> --}}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistiques -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-credit-card text-blue-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Crédits</p>
                                <p class="text-2xl font-bold text-gray-900">{{ number_format($totalCredits, 0, ',', ' ') }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-green-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Montant Payé</p>
                                <p class="text-2xl font-bold text-gray-900">{{ number_format($totalPaid, 0, ',', ' ') }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-clock text-yellow-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Montant Restant</p>
                                <p class="text-2xl font-bold text-gray-900">{{ number_format($totalRemaining, 0, ',', ' ') }} FCFA</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-percentage text-purple-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Taux de Recouvrement</p>
                                <p class="text-2xl font-bold text-gray-900">
                                    @if($totalCredits > 0)
                                        {{ number_format(($totalPaid / $totalCredits) * 100, 1) }}%
                                    @else
                                        0%
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Répartition par statut -->
            @if($creditsByStatus->count() > 0)
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-chart-pie mr-2"></i>Répartition par Statut
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        @foreach($creditsByStatus as $status => $data)
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">
                                        @switch($status)
                                            @case('active') Actifs @break
                                            @case('paid') Payés @break
                                            @case('overdue') En retard @break
                                            @case('cancelled') Annulés @break
                                            @default {{ ucfirst($status) }}
                                        @endswitch
                                    </p>
                                    <p class="text-lg font-bold text-gray-900">{{ $data['count'] }}</p>
                                    <p class="text-sm text-gray-600">{{ number_format($data['total'], 0, ',', ' ') }} FCFA</p>
                                </div>
                                <div class="text-2xl">
                                    @switch($status)
                                        @case('active')
                                            <i class="fas fa-clock text-blue-500"></i>
                                            @break
                                        @case('paid')
                                            <i class="fas fa-check-circle text-green-500"></i>
                                            @break
                                        @case('overdue')
                                            <i class="fas fa-exclamation-triangle text-red-500"></i>
                                            @break
                                        @case('cancelled')
                                            <i class="fas fa-times-circle text-gray-500"></i>
                                            @break
                                        @default
                                            <i class="fas fa-question-circle text-gray-400"></i>
                                    @endswitch
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Tableau des crédits -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-list mr-2"></i>Détail des Crédits
                        @if(request('status'))
                            - {{ ucfirst(request('status')) }}
                        @else
                            - Tous les Crédits
                        @endif
                    </h3>
                    
                    @if($credits->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Client
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Vente
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Montant Total
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Montant Payé
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Montant Restant
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date d'Échéance
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Statut
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($credits as $credit)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            <a href="{{ route('customers.show', $credit->customer) }}" 
                                               class="text-blue-600 hover:text-blue-800">
                                                {{ $credit->customer->name }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-gray-500">{{ $credit->customer->email ?? 'N/A' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <a href="{{ route('sales.show', $credit->sale) }}" 
                                               class="text-blue-600 hover:text-blue-800">
                                                {{ $credit->sale->sale_number }}
                                            </a>
                                        </div>
                                        <div class="text-sm text-gray-500">{{ $credit->sale->created_at->format('d/m/Y') }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ number_format($credit->total_amount, 0, ',', ' ') }} FCFA
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            {{ number_format($credit->amount_paid, 0, ',', ' ') }} FCFA
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ number_format($credit->remaining_balance, 0, ',', ' ') }} FCFA
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            @if($credit->due_date)
                                                {{ $credit->due_date->format('d/m/Y') }}
                                                @if($credit->due_date->isPast() && $credit->remaining_balance > 0)
                                                    <span class="text-red-600 text-xs">({{ $credit->due_date->diffForHumans() }})</span>
                                                @endif
                                            @else
                                                <span class="text-gray-400">Non définie</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @switch($credit->status)
                                            @case('active')
                                                @if($credit->due_date && $credit->due_date->isPast() && $credit->remaining_balance > 0)
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        <i class="fas fa-exclamation-triangle mr-1"></i>En retard
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        <i class="fas fa-clock mr-1"></i>Actif
                                                    </span>
                                                @endif
                                                @break
                                            @case('paid')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle mr-1"></i>Payé
                                                </span>
                                                @break
                                            @case('overdue')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <i class="fas fa-exclamation-triangle mr-1"></i>En retard
                                                </span>
                                                @break
                                            @case('cancelled')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    <i class="fas fa-times-circle mr-1"></i>Annulé
                                                </span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="{{ route('credits.show', $credit) }}" 
                                           class="text-blue-600 hover:text-blue-900 mr-3">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($credit->status === 'active')
                                        <a href="{{ route('credits.payments', $credit) }}" 
                                           class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-plus-circle"></i>
                                        </a>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-12">
                        <div class="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-credit-card text-gray-400 text-3xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun crédit trouvé</h3>
                        <p class="text-gray-500">
                            @if(request('status'))
                                Aucun crédit avec le statut "{{ request('status') }}".
                            @else
                                Aucun crédit dans la base de données.
                            @endif
                        </p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
