<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StockTransfer extends Model
{
    use HasFactory;

    protected $fillable = [
        'transfer_number',
        'from_store_id',
        'to_store_id',
        'requested_by',
        'approved_by',
        'shipped_by',
        'received_by',
        'cancelled_by',
        'status',
        'priority',
        'reason',
        'cancellation_reason',
        'total_items',
        'total_value',
        'requested_at',
        'approved_at',
        'shipped_at',
        'received_at',
        'cancelled_at',
        'notes'
    ];

    protected function casts(): array
    {
        return [
            'total_value' => 'decimal:2',
            'requested_at' => 'datetime',
            'approved_at' => 'datetime',
            'shipped_at' => 'datetime',
            'received_at' => 'datetime',
            'cancelled_at' => 'datetime',
        ];
    }

    /**
     * Relations
     */
    public function fromStore(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'from_store_id');
    }

    public function toStore(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'to_store_id');
    }

    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function shippedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'shipped_by');
    }

    public function receivedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    public function cancelledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    public function items(): HasMany
    {
        return $this->hasMany(StockTransferItem::class);
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeShipped($query)
    {
        return $query->where('status', 'shipped');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeForStore($query, $storeId)
    {
        return $query->where(function($q) use ($storeId) {
            $q->where('from_store_id', $storeId)
              ->orWhere('to_store_id', $storeId);
        });
    }

    public function scopeHighPriority($query)
    {
        return $query->where('priority', 'high');
    }

    /**
     * Accessors
     */
    public function getStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'En attente',
            'approved' => 'Approuvé',
            'shipped' => 'Expédié',
            'completed' => 'Terminé',
            'cancelled' => 'Annulé'
        ];

        return $labels[$this->status] ?? $this->status;
    }

    public function getPriorityLabelAttribute()
    {
        $labels = [
            'low' => 'Faible',
            'normal' => 'Normal',
            'high' => 'Élevée',
            'urgent' => 'Urgent'
        ];

        return $labels[$this->priority] ?? $this->priority;
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'warning',
            'approved' => 'info',
            'shipped' => 'primary',
            'completed' => 'success',
            'cancelled' => 'danger'
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    public function getPriorityColorAttribute()
    {
        $colors = [
            'low' => 'secondary',
            'normal' => 'primary',
            'high' => 'warning',
            'urgent' => 'danger'
        ];

        return $colors[$this->priority] ?? 'primary';
    }

    /**
     * Méthodes utilitaires
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    public function canBeShipped(): bool
    {
        return $this->status === 'approved';
    }

    public function canBeReceived(): bool
    {
        return $this->status === 'shipped';
    }

    public function canBeCancelled(): bool
    {
        return !in_array($this->status, ['completed', 'cancelled']);
    }

    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function getDurationAttribute()
    {
        if (!$this->requested_at) {
            return null;
        }

        $endDate = $this->completed_at ?? $this->cancelled_at ?? now();
        return $this->requested_at->diffForHumans($endDate, true);
    }

    public function getProgressPercentageAttribute()
    {
        switch ($this->status) {
            case 'pending':
                return 25;
            case 'approved':
                return 50;
            case 'shipped':
                return 75;
            case 'completed':
                return 100;
            case 'cancelled':
                return 0;
            default:
                return 0;
        }
    }

    /**
     * Calculer les statistiques du transfert
     */
    public function getStatistics()
    {
        $items = $this->items;
        
        return [
            'total_items' => $items->count(),
            'total_requested' => $items->sum('quantity_requested'),
            'total_approved' => $items->sum('quantity_approved'),
            'total_shipped' => $items->sum('quantity_sent'),
            'total_received' => $items->sum('quantity_received'),
            'total_value' => $this->total_value,
            'completion_rate' => $this->getCompletionRate(),
            'items_by_status' => $items->groupBy('status')->map->count()
        ];
    }

    /**
     * Calculer le taux de completion
     */
    public function getCompletionRate()
    {
        $totalRequested = $this->items->sum('quantity_requested');
        $totalReceived = $this->items->sum('quantity_received');
        
        if ($totalRequested == 0) {
            return 0;
        }
        
        return round(($totalReceived / $totalRequested) * 100, 2);
    }

    /**
     * Obtenir les items avec des écarts
     */
    public function getDiscrepancies()
    {
        return $this->items->filter(function($item) {
            return $item->quantity_requested != $item->quantity_received;
        });
    }

    /**
     * Vérifier si le transfert a des écarts
     */
    public function hasDiscrepancies(): bool
    {
        return $this->getDiscrepancies()->count() > 0;
    }
}
