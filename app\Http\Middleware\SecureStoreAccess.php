<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Store;
use App\Services\SecurityAuditService;
use Symfony\Component\HttpFoundation\Response;

class SecureStoreAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        $user = Auth::user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Obtenir l'ID du magasin depuis la route ou la session
        $storeId = $this->getStoreId($request);
        
        if (!$storeId) {
            Log::warning('Tentative d\'accès sans magasin spécifié', [
                'user_id' => $user->id,
                'route' => $request->route()->getName(),
                'ip' => $request->ip()
            ]);
            
            return redirect()->route('dashboard')
                ->with('error', 'Magasin non spécifié.');
        }

        $store = Store::find($storeId);
        
        if (!$store || !$store->is_active) {
            Log::warning('Tentative d\'accès à un magasin inexistant ou inactif', [
                'user_id' => $user->id,
                'store_id' => $storeId,
                'ip' => $request->ip()
            ]);
            
            return redirect()->route('dashboard')
                ->with('error', 'Magasin non trouvé ou inactif.');
        }

        // Vérifier l'accès de base au magasin
        if (!$this->hasStoreAccess($user, $store)) {
            Log::warning('Tentative d\'accès non autorisé à un magasin', [
                'user_id' => $user->id,
                'store_id' => $store->id,
                'user_store_id' => $user->store_id,
                'ip' => $request->ip(),
                'route' => $request->route()->getName()
            ]);
            
            return redirect()->route('dashboard')
                ->with('error', 'Vous n\'avez pas accès à ce magasin.');
        }

        // Vérifier les permissions spécifiques si demandées
        if (!empty($permissions)) {
            foreach ($permissions as $permission) {
                if (!$this->hasPermission($user, $store, $permission)) {
                    Log::warning('Tentative d\'accès avec permission insuffisante', [
                        'user_id' => $user->id,
                        'store_id' => $store->id,
                        'permission' => $permission,
                        'ip' => $request->ip(),
                        'route' => $request->route()->getName()
                    ]);
                    
                    return redirect()->back()
                        ->with('error', 'Permission insuffisante pour cette action.');
                }
            }
        }

        // Vérifier les tentatives de manipulation d'ID
        if ($this->detectIdManipulation($request, $user, $store)) {
            Log::alert('Tentative de manipulation d\'ID détectée', [
                'user_id' => $user->id,
                'store_id' => $store->id,
                'request_data' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
            
            return response()->json(['error' => 'Action non autorisée'], 403);
        }

        // Ajouter le magasin au contexte de la requête
        $request->attributes->set('current_store', $store);
        
        // Partager avec les vues
        view()->share('currentStore', $store);

        return $next($request);
    }

    /**
     * Obtenir l'ID du magasin depuis la requête
     */
    private function getStoreId(Request $request): ?int
    {
        // Depuis les paramètres de route
        if ($request->route('store')) {
            $store = $request->route('store');
            return is_object($store) ? $store->id : (int) $store;
        }

        // Depuis les paramètres de requête
        if ($request->has('store_id')) {
            return (int) $request->get('store_id');
        }

        // Depuis la session
        if (session()->has('current_store_id')) {
            return (int) session('current_store_id');
        }

        // Depuis le magasin assigné à l'utilisateur
        $user = Auth::user();
        if ($user && $user->store_id) {
            return $user->store_id;
        }

        return null;
    }

    /**
     * Vérifier l'accès de base au magasin
     */
    private function hasStoreAccess($user, Store $store): bool
    {
        // Super admin a accès à tout
        if ($user->hasRole('superAdmin')) {
            return true;
        }

        // Admin a accès à tous les magasins
        if ($user->hasRole('admin')) {
            return true;
        }

        // Manager a accès aux magasins qu'il gère
        if ($user->hasRole('manager')) {
            return $user->managedStores()->where('id', $store->id)->exists();
        }

        // Employé a accès seulement à son magasin assigné
        return $user->store_id === $store->id;
    }

    /**
     * Vérifier une permission spécifique
     */
    private function hasPermission($user, Store $store, string $permission): bool
    {
        switch ($permission) {
            case 'manage_stock':
                return $user->can('manageStock', $store);
            
            case 'manage_prices':
                return $user->can('managePrices', $store);
            
            case 'view_reports':
                return $user->can('viewReports', $store);
            
            case 'view_financials':
                return $user->can('viewFinancials', $store);
            
            case 'manage_settings':
                return $user->can('manageSettings', $store);
            
            case 'transfer_stock':
                return $user->can('transferStock', $store);
            
            case 'assign_users':
                return $user->can('assignUsers', $store);
            
            case 'export_data':
                return $user->can('exportData', $store);
            
            default:
                return false;
        }
    }

    /**
     * Détecter les tentatives de manipulation d'ID
     */
    private function detectIdManipulation(Request $request, $user, Store $store): bool
    {
        // Vérifier si l'utilisateur essaie d'accéder à des données d'autres magasins
        $suspiciousFields = ['store_id', 'customer_id', 'product_id', 'sale_id'];
        
        foreach ($suspiciousFields as $field) {
            if ($request->has($field)) {
                $value = $request->get($field);
                
                if ($field === 'store_id' && (int) $value !== $store->id) {
                    return true;
                }
                
                // Vérifier que les autres entités appartiennent au magasin
                if ($field === 'customer_id' && $value) {
                    $customer = \App\Models\Customer::find($value);
                    if ($customer && $customer->store_id !== $store->id) {
                        return true;
                    }
                }
                
                if ($field === 'sale_id' && $value) {
                    $sale = \App\Models\Sale::find($value);
                    if ($sale && $sale->store_id !== $store->id) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}
