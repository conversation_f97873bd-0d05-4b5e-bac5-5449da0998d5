<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Store;
use App\Models\StoreStock;
use App\Models\StockMovement;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class StoreStockController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of store stocks.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $userStore = $user->store;

        // Super admins peuvent voir tous les magasins
        if ($user->roles->pluck('name')->contains('superAdmin')) {
            $stores = Store::active()->get();
            $selectedStoreId = $request->get('store_id', $userStore?->id);
        } else {
            $stores = collect([$userStore]);
            $selectedStoreId = $userStore->id;
        }

        if (!$selectedStoreId) {
            return redirect()->route('dashboard')
                ->with('error', 'Vous devez être assigné à un magasin.');
        }

        $selectedStore = Store::find($selectedStoreId);
        
        // Récupérer les stocks avec pagination et filtres
        $query = StoreStock::with(['product', 'store'])
            ->where('store_id', $selectedStoreId)
            ->active();

        // Filtres
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('product', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            switch ($status) {
                case 'low_stock':
                    $query->whereColumn('quantity', '<=', 'min_stock_level');
                    break;
                case 'out_of_stock':
                    $query->where('quantity', 0);
                    break;
                case 'overstock':
                    $query->whereColumn('quantity', '>', 'max_stock_level');
                    break;
                case 'reorder':
                    $query->whereColumn('quantity', '<=', 'reorder_point');
                    break;
            }
        }

        $stocks = $query->paginate(20);

        return view('store-stocks.index', compact('stocks', 'stores', 'selectedStore'));
    }

    /**
     * Show the form for creating a new store stock.
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        $userStore = $user->store;

        $products = Product::active()->get();
        
        if ($user->roles->pluck('name')->contains('superAdmin')) {
            $stores = Store::active()->get();
        } else {
            $stores = collect([$userStore]);
        }

        $selectedProductId = $request->get('product_id');
        $selectedStoreId = $request->get('store_id', $userStore?->id);

        return view('store-stocks.create', compact('products', 'stores', 'selectedProductId', 'selectedStoreId'));
    }

    /**
     * Store a newly created store stock in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'store_id' => 'required|exists:stores,id',
            'quantity' => 'required|integer|min:0',
            'min_stock_level' => 'required|integer|min:0',
            'max_stock_level' => 'nullable|integer|min:0|gte:min_stock_level',
            'reorder_point' => 'nullable|integer|min:0',
            'location' => 'nullable|string|max:100',
        ]);

        // Vérifier que l'utilisateur a accès à ce magasin
        $user = Auth::user();
        if (!$user->roles->pluck('name')->contains('superAdmin') && $user->store_id != $request->store_id) {
            abort(403, 'Accès non autorisé à ce magasin.');
        }

        // Vérifier qu'il n'existe pas déjà un stock pour ce produit dans ce magasin
        $existingStock = StoreStock::where('product_id', $request->product_id)
            ->where('store_id', $request->store_id)
            ->first();

        if ($existingStock) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Un stock existe déjà pour ce produit dans ce magasin.');
        }

        DB::beginTransaction();
        try {
            $storeStock = StoreStock::create($request->all());

            // Créer un mouvement de stock initial si la quantité > 0
            if ($request->quantity > 0) {
                StockMovement::create([
                    'product_id' => $request->product_id,
                    'store_id' => $request->store_id,
                    'user_id' => Auth::id(),
                    'type' => 'in',
                    'quantity' => $request->quantity,
                    'previous_stock' => 0,
                    'new_stock' => $request->quantity,
                    'reference' => 'STOCK_INITIAL',
                    'reason' => 'Stock initial',
                ]);
            }

            DB::commit();

            return redirect()->route('store-stocks.index', ['store_id' => $request->store_id])
                ->with('success', 'Stock créé avec succès.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->withInput()
                ->with('error', 'Erreur lors de la création : ' . $e->getMessage());
        }
    }

    /**
     * Display the specified store stock.
     */
    public function show(StoreStock $storeStock)
    {
        $user = Auth::user();
        if (!$user->roles->pluck('name')->contains('superAdmin') && $user->store_id != $storeStock->store_id) {
            abort(403, 'Accès non autorisé.');
        }

        $storeStock->load(['product', 'store']);
        
        // Historique des mouvements de stock
        $stockMovements = StockMovement::where('product_id', $storeStock->product_id)
            ->where('store_id', $storeStock->store_id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('store-stocks.show', compact('storeStock', 'stockMovements'));
    }

    /**
     * Show the form for editing the specified store stock.
     */
    public function edit(StoreStock $storeStock)
    {
        $user = Auth::user();
        if (!$user->roles->pluck('name')->contains('superAdmin') && $user->store_id != $storeStock->store_id) {
            abort(403, 'Accès non autorisé.');
        }

        return view('store-stocks.edit', compact('storeStock'));
    }

    /**
     * Update the specified store stock in storage.
     */
    public function update(Request $request, StoreStock $storeStock)
    {
        $user = Auth::user();
        if (!$user->roles->pluck('name')->contains('superAdmin') && $user->store_id != $storeStock->store_id) {
            abort(403, 'Accès non autorisé.');
        }

        $request->validate([
            'min_stock_level' => 'required|integer|min:0',
            'max_stock_level' => 'nullable|integer|min:0|gte:min_stock_level',
            'reorder_point' => 'nullable|integer|min:0',
            'location' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        $storeStock->update($request->all());

        return redirect()->route('store-stocks.index', ['store_id' => $storeStock->store_id])
            ->with('success', 'Stock mis à jour avec succès.');
    }

    /**
     * Adjust stock quantity
     */
    public function adjustStock(Request $request, StoreStock $storeStock)
    {
        $user = Auth::user();
        if (!$user->roles->pluck('name')->contains('superAdmin') && $user->store_id != $storeStock->store_id) {
            abort(403, 'Accès non autorisé.');
        }

        $request->validate([
            'adjustment_type' => 'required|in:add,remove,set',
            'quantity' => 'required|integer|min:0',
            'reason' => 'required|string|max:255',
            'reference' => 'nullable|string|max:100',
        ]);

        DB::beginTransaction();
        try {
            $previousStock = $storeStock->quantity;
            $newStock = $previousStock;

            switch ($request->adjustment_type) {
                case 'add':
                    $storeStock->addStock($request->quantity, $request->reason, $request->reference);
                    $newStock = $previousStock + $request->quantity;
                    break;
                case 'remove':
                    $storeStock->removeStock($request->quantity, $request->reason, $request->reference);
                    $newStock = $previousStock - $request->quantity;
                    break;
                case 'set':
                    $storeStock->adjustStock($request->quantity, $request->reason, $request->reference);
                    $newStock = $request->quantity;
                    break;
            }

            DB::commit();

            return redirect()->route('store-stocks.show', $storeStock)
                ->with('success', 'Stock ajusté avec succès.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Erreur lors de l\'ajustement : ' . $e->getMessage());
        }
    }

    /**
     * Transfer stock between stores
     */
    public function transferStock(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'from_store_id' => 'required|exists:stores,id',
            'to_store_id' => 'required|exists:stores,id|different:from_store_id',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'reference' => 'nullable|string|max:100',
        ]);

        $user = Auth::user();
        if (!$user->roles->pluck('name')->contains('superAdmin')) {
            abort(403, 'Seuls les super admins peuvent effectuer des transferts entre magasins.');
        }

        DB::beginTransaction();
        try {
            $fromStock = StoreStock::where('product_id', $request->product_id)
                ->where('store_id', $request->from_store_id)
                ->first();

            $toStock = StoreStock::where('product_id', $request->product_id)
                ->where('store_id', $request->to_store_id)
                ->first();

            if (!$fromStock || !$fromStock->hasStock($request->quantity)) {
                throw new \Exception('Stock insuffisant dans le magasin source.');
            }

            if (!$toStock) {
                throw new \Exception('Le produit n\'existe pas dans le magasin de destination.');
            }

            // Retirer du magasin source
            $fromStock->removeStock(
                $request->quantity, 
                'Transfert vers ' . $toStock->store->name,
                $request->reference
            );

            // Ajouter au magasin destination
            $toStock->addStock(
                $request->quantity,
                'Transfert depuis ' . $fromStock->store->name,
                $request->reference
            );

            DB::commit();

            return redirect()->back()
                ->with('success', 'Transfert effectué avec succès.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Erreur lors du transfert : ' . $e->getMessage());
        }
    }
}
