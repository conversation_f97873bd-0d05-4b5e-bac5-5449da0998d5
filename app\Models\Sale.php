<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\DB;

class Sale extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_number',
        'user_id',
        'store_id',
        'customer_id',
        'subtotal',
        'discount_amount',
        'total_amount',
        'payment_method',
        'amount_paid',
        'change_amount',
        'status',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'subtotal' => 'decimal:2',
            'discount_amount' => 'decimal:2',
            'total_amount' => 'decimal:2',
            'amount_paid' => 'decimal:2',
            'change_amount' => 'decimal:2',
        ];
    }

    /**
     * Relations
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    public function credit()
    {
        return $this->hasOne(Credit::class);
    }

    /**
     * Scopes
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($sale) {
            if (empty($sale->sale_number)) {
                $sale->sale_number = 'SALE-' . date('Ymd') . '-' . str_pad(
                    static::whereDate('created_at', today())->count() + 1,
                    4,
                    '0',
                    STR_PAD_LEFT
                );
            }
        });
    }

    /**
     * Accessors & Mutators
     */
    public function isCreditSale()
    {
        return $this->payment_method === 'credit';
    }

    public function isPaid()
    {
        return $this->amount_paid >= $this->total_amount;
    }

    /**
     * Profit calculation methods
     */
    public function getTotalProfitAttribute()
    {
        return $this->saleItems->sum('profit_amount') ?? 0;
    }

    public function getAverageProfitMarginAttribute()
    {
        $items = $this->saleItems->where('profit_margin', '!=', null);
        if ($items->isEmpty()) {
            return null;
        }

        return $items->avg('profit_margin');
    }

    public function calculateProfit()
    {
        $totalProfit = 0;
        $totalCost = 0;
        $totalRevenue = 0;

        foreach ($this->saleItems as $item) {
            if ($item->cost_price) {
                $itemCost = $item->cost_price * $item->quantity;
                $itemRevenue = $item->unit_price * $item->quantity;

                $totalCost += $itemCost;
                $totalRevenue += $itemRevenue;
                $totalProfit += ($itemRevenue - $itemCost);
            }
        }

        return [
            'total_cost' => $totalCost,
            'total_revenue' => $totalRevenue,
            'total_profit' => $totalProfit,
            'profit_margin' => $totalRevenue > 0 ? ($totalProfit / $totalRevenue) * 100 : 0
        ];
    }

    /**
     * Best sellers and recent sales
     */
    public static function getBestSellers()
    {
        return DB::table('sale_items')
            ->join('products', 'sale_items.product_id', '=', 'products.id')
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->where('sales.status', 'completed')
            ->select(
                'products.name',
                DB::raw('SUM(sale_items.quantity) as quantity'),
                DB::raw('SUM(sale_items.quantity * sale_items.unit_price) as total')
            )
            ->groupBy('products.id', 'products.name')
            ->orderByDesc('quantity')
            ->limit(5)
            ->get();
    }

    public static function getRecentSales()
    {
        return static::with('customer')
            ->where('status', 'completed')
            ->select('id', 'customer_id', 'total_amount', 'created_at')
            ->orderByDesc('created_at')
            ->limit(5)
            ->get()
            ->map(function ($sale) {
                return [
                    'customer_name' => $sale->customer ? $sale->customer->name : null,
                    'total_amount' => $sale->total_amount,
                    'created_at' => $sale->created_at
                ];
            });
    }

    public function calculateTotals()
    {
        $this->subtotal = $this->items->sum(function ($item) {
            return $item->quantity * $item->unit_price;
        });
        
        // Appliquer la TVA du magasin
        $taxRate = $this->store->default_tax_rate ?? 0;
        $this->tax_amount = $this->subtotal * ($taxRate / 100);
        
        // Calculer le total final
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        
        // Calculer la monnaie
        if ($this->amount_paid > 0) {
            $this->change_amount = max(0, $this->amount_paid - $this->total_amount);
        }
    }

    public function processPayment(float $amount, string $method = 'cash')
    {
        DB::transaction(function () use ($amount, $method) {
            $this->amount_paid = $amount;
            $this->payment_method = $method;
            $this->calculateTotals();
            
            if ($this->total_amount > $this->amount_paid) {
                // Créer un crédit si le paiement est partiel
                $credit = Credit::create([
                    'customer_id' => $this->customer_id,
                    'sale_id' => $this->id,
                    'amount' => $this->total_amount - $this->amount_paid,
                    'due_date' => now()->addDays(30),
                    'status' => 'pending'
                ]);
            }
            
            $this->status = $this->amount_paid >= $this->total_amount ? 'completed' : 'partial';
            $this->save();
            
            // Mettre à jour le stock
            foreach ($this->items as $item) {
                $this->store->stocks()
                    ->where('product_id', $item->product_id)
                    ->decrement('quantity', $item->quantity);
            }
        });
    }
}
