<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-store mr-3 text-blue-600"></i>Nouveau Magasin
                </h2>
                <p class="text-sm text-gray-600 mt-1">Créez un nouveau magasin dans votre réseau</p>
            </div>
            <x-button href="{{ route('stores.index') }}" variant="outline" icon="fas fa-arrow-left">
                Retour à la liste
            </x-button>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <form method="POST" action="{{ route('stores.store') }}" class="space-y-6">
                @csrf

                <!-- Informations de base -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>Informations de base
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field>
                            <x-input-label for="name" value="Nom du magasin *" />
                            <x-text-input id="name" name="name" type="text" 
                                         value="{{ old('name') }}" required autofocus 
                                         placeholder="Ex: Magasin Centre-ville" />
                            <x-input-error :messages="$errors->get('name')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="code" value="Code magasin *" />
                            <x-text-input id="code" name="code" type="text" 
                                         value="{{ old('code') }}" required 
                                         placeholder="Ex: MAG001" />
                            <x-input-error :messages="$errors->get('code')" />
                            <p class="text-xs text-gray-500 mt-1">Code unique pour identifier le magasin</p>
                        </x-form-field>

                        <x-form-field class="md:col-span-2">
                            <x-input-label for="description" value="Description" />
                            <textarea id="description" name="description" rows="3"
                                     class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                     placeholder="Description du magasin...">{{ old('description') }}</textarea>
                            <x-input-error :messages="$errors->get('description')" />
                        </x-form-field>
                    </div>
                </div>

                <!-- Adresse et contact -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-map-marker-alt mr-2 text-green-600"></i>Adresse et contact
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field class="md:col-span-2">
                            <x-input-label for="address" value="Adresse complète" />
                            <textarea id="address" name="address" rows="2"
                                     class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                     placeholder="Adresse complète du magasin...">{{ old('address') }}</textarea>
                            <x-input-error :messages="$errors->get('address')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="phone" value="Téléphone" />
                            <x-text-input id="phone" name="phone" type="tel" 
                                         value="{{ old('phone') }}" 
                                         placeholder="Ex: +225 01 02 03 04 05" />
                            <x-input-error :messages="$errors->get('phone')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="email" value="Email" />
                            <x-text-input id="email" name="email" type="email" 
                                         value="{{ old('email') }}" 
                                         placeholder="Ex: <EMAIL>" />
                            <x-input-error :messages="$errors->get('email')" />
                        </x-form-field>
                    </div>
                </div>

                <!-- Responsable -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-user-tie mr-2 text-purple-600"></i>Responsable du magasin
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field>
                            <x-input-label for="manager_name" value="Nom du responsable" />
                            <x-text-input id="manager_name" name="manager_name" type="text" 
                                         value="{{ old('manager_name') }}" 
                                         placeholder="Nom complet du responsable" />
                            <x-input-error :messages="$errors->get('manager_name')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="manager_phone" value="Téléphone du responsable" />
                            <x-text-input id="manager_phone" name="manager_phone" type="tel" 
                                         value="{{ old('manager_phone') }}" 
                                         placeholder="Téléphone du responsable" />
                            <x-input-error :messages="$errors->get('manager_phone')" />
                        </x-form-field>
                    </div>
                </div>

                <!-- Configuration -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-cog mr-2 text-orange-600"></i>Configuration
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <x-form-field>
                            <x-input-label for="default_tax_rate" value="Taux de taxe par défaut (%)" />
                            <x-text-input id="default_tax_rate" name="default_tax_rate" type="number" 
                                         step="0.01" min="0" max="100"
                                         value="{{ old('default_tax_rate', '18.00') }}" />
                            <x-input-error :messages="$errors->get('default_tax_rate')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="currency" value="Devise" />
                            <select id="currency" name="currency" 
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="FCFA" {{ old('currency', 'FCFA') === 'FCFA' ? 'selected' : '' }}>FCFA</option>
                                <option value="EUR" {{ old('currency') === 'EUR' ? 'selected' : '' }}>Euro (EUR)</option>
                                <option value="USD" {{ old('currency') === 'USD' ? 'selected' : '' }}>Dollar (USD)</option>
                            </select>
                            <x-input-error :messages="$errors->get('currency')" />
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="timezone" value="Fuseau horaire" />
                            <select id="timezone" name="timezone" 
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="Africa/Abidjan" {{ old('timezone', 'Africa/Abidjan') === 'Africa/Abidjan' ? 'selected' : '' }}>
                                    Abidjan (GMT+0)
                                </option>
                                <option value="Europe/Paris" {{ old('timezone') === 'Europe/Paris' ? 'selected' : '' }}>
                                    Paris (GMT+1)
                                </option>
                            </select>
                            <x-input-error :messages="$errors->get('timezone')" />
                        </x-form-field>
                    </div>

                    <div class="mt-6">
                        <div class="flex items-center">
                            <input id="is_active" name="is_active" type="checkbox" value="1" 
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Magasin actif
                            </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Un magasin inactif ne peut pas effectuer de ventes</p>
                    </div>
                </div>

                <!-- Initialisation -->
                <div class="bg-white p-6 rounded-lg shadow-sm">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-rocket mr-2 text-red-600"></i>Initialisation (optionnel)
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-form-field>
                            <x-input-label for="copy_from_store" value="Copier depuis un magasin existant" />
                            <select id="copy_from_store" name="copy_from_store"
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="">-- Aucun --</option>
                                @if(isset($existingStores))
                                    @foreach($existingStores as $store)
                                        <option value="{{ $store->id }}" {{ old('copy_from_store') == $store->id ? 'selected' : '' }}>
                                            {{ $store->name }} ({{ $store->code }})
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                            <x-input-error :messages="$errors->get('copy_from_store')" />
                            <p class="text-xs text-gray-500 mt-1">
                                Les prix des produits seront copiés, les stocks initialisés à 0
                            </p>
                        </x-form-field>

                        <x-form-field>
                            <x-input-label for="initial_users" value="Utilisateurs à assigner" />
                            <select id="initial_users" name="initial_users[]" multiple
                                   class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                @if(isset($availableUsers))
                                    @foreach($availableUsers as $user)
                                        <option value="{{ $user->id }}"
                                               {{ in_array($user->id, old('initial_users', [])) ? 'selected' : '' }}>
                                            {{ $user->name }} ({{ $user->email }})
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                            <x-input-error :messages="$errors->get('initial_users')" />
                            <p class="text-xs text-gray-500 mt-1">
                                Maintenez Ctrl/Cmd pour sélectionner plusieurs utilisateurs
                            </p>
                        </x-form-field>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-4">
                    <x-button href="{{ route('stores.index') }}" variant="outline">
                        Annuler
                    </x-button>
                    <x-button type="submit" variant="primary" icon="fas fa-save">
                        Créer le magasin
                    </x-button>
                </div>
            </form>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Auto-génération du code à partir du nom
                const nameInput = document.getElementById('name');
                const codeInput = document.getElementById('code');
                
                nameInput.addEventListener('input', function() {
                    if (!codeInput.value || codeInput.dataset.autoGenerated) {
                        const name = this.value.toUpperCase()
                            .replace(/[^A-Z0-9\s]/g, '')
                            .replace(/\s+/g, '')
                            .substring(0, 6);
                        codeInput.value = name;
                        codeInput.dataset.autoGenerated = 'true';
                    }
                });
                
                codeInput.addEventListener('input', function() {
                    if (this.value) {
                        delete this.dataset.autoGenerated;
                    }
                });
            });
        </script>
    @endpush
</x-app-layout>
