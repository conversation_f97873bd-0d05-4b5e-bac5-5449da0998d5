<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Store;

class StoreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stores = [
            [
                'name' => 'Magasin Centre-Ville',
                'code' => 'CENTRE',
                'description' => 'Magasin principal situé au centre-ville',
                'address' => '123 Avenue de la République',
                'city' => 'Ouagadougou',
                'phone' => '+226 25 30 40 50',
                'email' => '<EMAIL>',
                'manager_name' => '<PERSON>',
                'default_tax_rate' => 18.00,
                'currency' => 'XOF',
                'is_active' => true,
                'settings' => [
                    'opening_hours' => '08:00-18:00',
                    'max_discount_percent' => 10,
                    'auto_print_receipt' => true,
                ]
            ],
            [
                'name' => 'Magasin Zone 4',
                'code' => 'ZONE4',
                'description' => 'Succursale située en Zone 4',
                'address' => '456 Rue de la Paix',
                'city' => 'Ouagadougou',
                'phone' => '+226 25 31 41 51',
                'email' => '<EMAIL>',
                'manager_name' => '<PERSON> Kaboré',
                'default_tax_rate' => 18.00,
                'currency' => 'XOF',
                'is_active' => true,
                'settings' => [
                    'opening_hours' => '07:30-19:00',
                    'max_discount_percent' => 5,
                    'auto_print_receipt' => false,
                ]
            ],
            [
                'name' => 'Magasin Bobo-Dioulasso',
                'code' => 'BOBO',
                'description' => 'Succursale de Bobo-Dioulasso',
                'address' => '789 Boulevard de la Révolution',
                'city' => 'Bobo-Dioulasso',
                'phone' => '+226 20 97 85 63',
                'email' => '<EMAIL>',
                'manager_name' => 'Ibrahim Traoré',
                'default_tax_rate' => 18.00,
                'currency' => 'XOF',
                'is_active' => true,
                'settings' => [
                    'opening_hours' => '08:00-17:30',
                    'max_discount_percent' => 8,
                    'auto_print_receipt' => true,
                ]
            ]
        ];

        foreach ($stores as $storeData) {
            Store::create($storeData);
        }
    }
}
